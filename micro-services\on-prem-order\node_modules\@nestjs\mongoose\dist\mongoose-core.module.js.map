{"version": 3, "file": "mongoose-core.module.js", "sourceRoot": "", "sources": ["../lib/mongoose-core.module.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAQwB;AACxB,uCAAyC;AACzC,qCAAqC;AAErC,+BAA4C;AAC5C,8CAA4C;AAC5C,4DAA0E;AAO1E,6DAG8B;AAIvB,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAC7B,YACqD,cAAsB,EACxD,SAAoB;QADc,mBAAc,GAAd,cAAc,CAAQ;QACxD,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,MAAM,CAAC,OAAO,CACZ,GAAW,EACX,UAAiC,EAAE;QAEnC,MAAM,EACJ,aAAa,EACb,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,sBAAsB,EACtB,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,GAAG,eAAe,EACnB,GAAG,OAAO,CAAC;QAEZ,MAAM,yBAAyB,GAC7B,iBAAiB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;QAEpD,MAAM,uBAAuB,GAC3B,sBAAsB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAE/C,MAAM,sBAAsB,GAAG,IAAA,mCAAkB,EAAC,cAAc,CAAC,CAAC;QAElE,MAAM,8BAA8B,GAAG;YACrC,OAAO,EAAE,6CAAwB;YACjC,QAAQ,EAAE,sBAAsB;SACjC,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,OAAO,EAAE,sBAAsB;YAC/B,UAAU,EAAE,KAAK,IAAkB,EAAE,CACnC,MAAM,IAAA,oBAAa,EACjB,IAAA,YAAK,EAAC,KAAK,IAAI,EAAE,CACf,yBAAyB,CACvB,MAAM,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,eAAe,EAAE;gBACxD,cAAc;gBACd,kBAAkB;aACnB,CAAC,EACF,sBAAsB,CACvB,CACF,CAAC,IAAI,CACJ,IAAA,4BAAW,EAAC,aAAa,EAAE,UAAU,EAAE,eAAe,CAAC,EACvD,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;gBACnB,MAAM,uBAAuB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CACH,CACF;SACJ,CAAC;QACF,OAAO;YACL,MAAM,EAAE,oBAAkB;YAC1B,SAAS,EAAE,CAAC,kBAAkB,EAAE,8BAA8B,CAAC;YAC/D,OAAO,EAAE,CAAC,kBAAkB,CAAC;SAC9B,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAmC;QACrD,MAAM,sBAAsB,GAAG,IAAA,mCAAkB,EAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAE1E,MAAM,8BAA8B,GAAG;YACrC,OAAO,EAAE,6CAAwB;YACjC,QAAQ,EAAE,sBAAsB;SACjC,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,OAAO,EAAE,sBAAsB;YAC/B,UAAU,EAAE,KAAK,EACf,qBAAmD,EACrC,EAAE;gBAChB,MAAM,EACJ,aAAa,EACb,UAAU,EACV,GAAG,EACH,iBAAiB,EACjB,sBAAsB,EACtB,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,GAAG,eAAe,EACnB,GAAG,qBAAqB,CAAC;gBAE1B,MAAM,yBAAyB,GAC7B,iBAAiB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;gBAEpD,MAAM,uBAAuB,GAC3B,sBAAsB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;gBAE/C,OAAO,MAAM,IAAA,oBAAa,EACxB,IAAA,YAAK,EAAC,KAAK,IAAI,EAAE,CACf,yBAAyB,CACvB,MAAM,IAAI,CAAC,wBAAwB,CACjC,GAAa,EACb,eAAe,EACf,EAAE,cAAc,EAAE,kBAAkB,EAAE,CACvC,EACD,sBAAsB,CACvB,CACF,CAAC,IAAI,CACJ,IAAA,4BAAW,EAAC,aAAa,EAAE,UAAU,EAAE,eAAe,CAAC,EACvD,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;oBACnB,MAAM,uBAAuB,CAAC,KAAK,CAAC,CAAC;gBACvC,CAAC,CAAC,CACH,CACF,CAAC;YACJ,CAAC;YACD,MAAM,EAAE,CAAC,4CAAuB,CAAC;SAClC,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC1D,OAAO;YACL,MAAM,EAAE,oBAAkB;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,SAAS,EAAE;gBACT,GAAG,cAAc;gBACjB,kBAAkB;gBAClB,8BAA8B;aAC/B;YACD,OAAO,EAAE,CAAC,kBAAkB,CAAC;SAC9B,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,oBAAoB,CACjC,OAAmC;QAEnC,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAwC,CAAC;QAClE,OAAO;YACL,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;YACxC;gBACE,OAAO,EAAE,QAAQ;gBACjB,QAAQ;aACT;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CACvC,OAAmC;QAEnC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,4CAAuB;gBAChC,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;aAC7B,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG;YACb,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,WAAW,CAAiC;SAC1E,CAAC;QACF,OAAO;YACL,OAAO,EAAE,4CAAuB;YAChC,UAAU,EAAE,KAAK,EAAE,cAAsC,EAAE,EAAE,CAC3D,MAAM,cAAc,CAAC,qBAAqB,EAAE;YAC9C,MAAM;SACP,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC3C,GAAW,EACX,eAA+B,EAC/B,cAGC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAEnE,IAAI,cAAc,EAAE,cAAc,EAAE,CAAC;YACnC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,cAAc,EAAE,kBAAkB,EAAE,CAAC,UAAU,CAAC,CAAC;QAEjD,OAAO,UAAU,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM,IAAI,CAAC,cAAc,CAAC,CAAC;QAChE,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;CACF,CAAA;AA7LY,gDAAkB;6BAAlB,kBAAkB;IAF9B,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC,EAAE,CAAC;IAGN,WAAA,IAAA,eAAM,EAAC,6CAAwB,CAAC,CAAA;6CACL,gBAAS;GAH5B,kBAAkB,CA6L9B"}