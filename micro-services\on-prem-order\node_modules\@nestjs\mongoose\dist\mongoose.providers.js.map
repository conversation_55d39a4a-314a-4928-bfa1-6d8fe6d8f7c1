{"version": 3, "file": "mongoose.providers.js", "sourceRoot": "", "sources": ["../lib/mongoose.providers.ts"], "names": [], "mappings": ";;AAKA,0DA0BC;AAED,oEA4BC;AA3DD,4DAA4E;AAG5E,SAAgB,uBAAuB,CACrC,cAAuB,EACvB,UAA6B,EAAE;IAE/B,OAAO,OAAO,CAAC,MAAM,CACnB,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC;QACrB,GAAG,SAAS;QACZ,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3C,OAAO,EAAE,IAAA,8BAAa,EAAC,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC;YAC9C,UAAU,EAAE,CAAC,KAAsB,EAAE,EAAE,CACrC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC;YAChD,MAAM,EAAE,CAAC,IAAA,8BAAa,EAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;SACrD,CAAC,CAAC;QACH;YACE,OAAO,EAAE,IAAA,8BAAa,EAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;YACnD,UAAU,EAAE,CAAC,UAAsB,EAAE,EAAE;gBACrC,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1C,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;oBAChC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;gBACpE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,EAAE,CAAC,IAAA,mCAAkB,EAAC,cAAc,CAAC,CAAC;SAC7C;KACF,EACD,EAAgB,CACjB,CAAC;AACJ,CAAC;AAED,SAAgB,4BAA4B,CAC1C,cAAuB,EACvB,iBAAsC,EAAE;IAExC,OAAO,cAAc,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE;QACjD,OAAO;YACL,GAAG,SAAS;YACZ;gBACE,OAAO,EAAE,IAAA,8BAAa,EAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC;gBACnD,UAAU,EAAE,KAAK,EAAE,UAAsB,EAAE,GAAG,IAAe,EAAE,EAAE;oBAC/D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;oBAChD,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAC5B,MAAM,CAAC,IAAI,EACX,MAAM,EACN,MAAM,CAAC,UAAU,CAClB,CAAC;oBACF,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,MAAM,EAAE,CAAC,IAAA,mCAAkB,EAAC,cAAc,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;aACvE;YACD,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC3C,OAAO,EAAE,IAAA,8BAAa,EAAC,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC;gBAC9C,UAAU,EAAE,CAAC,KAAsB,EAAE,EAAE,CACrC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC;gBAChD,MAAM,EAAE,CAAC,IAAA,8BAAa,EAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;aACrD,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC,EAAE,EAAgB,CAAC,CAAC;AACvB,CAAC"}