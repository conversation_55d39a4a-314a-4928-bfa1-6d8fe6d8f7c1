{"name": "@nestjs/mongoose", "version": "11.0.3", "description": "Nest - modern, fast, powerful node.js web framework (@mongoose)", "author": "<PERSON><PERSON><PERSON>", "repository": "https://github.com/nestjs/mongoose.git", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "eslint \"lib/**/*.ts\" --fix", "format": "prettier \"lib/**/*.ts\" --write", "build": "rm -rf dist && tsc -p tsconfig.build.json", "precommit": "lint-staged", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prerelease": "npm run build", "release": "release-it", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "test:e2e:dev": "jest --config ./tests/jest-e2e.json --runInBand --watch", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.23.0", "@nestjs/common": "11.0.12", "@nestjs/core": "11.0.12", "@nestjs/platform-express": "11.0.12", "@nestjs/testing": "11.0.12", "@types/jest": "29.5.14", "@types/node": "22.13.13", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.4", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.0", "mongoose": "8.13.0", "prettier": "3.5.3", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rxjs": "7.8.2", "supertest": "7.1.0", "ts-jest": "29.3.0", "ts-node": "10.9.2", "typescript": "5.8.2", "typescript-eslint": "8.28.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0", "mongoose": "^7.0.0 || ^8.0.0", "rxjs": "^7.0.0"}, "lint-staged": {"**/*.{ts,json}": []}}