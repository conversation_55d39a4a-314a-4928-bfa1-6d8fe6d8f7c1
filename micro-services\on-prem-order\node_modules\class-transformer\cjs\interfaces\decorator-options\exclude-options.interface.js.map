{"version": 3, "file": "exclude-options.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/decorator-options/exclude-options.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Possible transformation options for the @Exclude decorator.\n */\nexport interface ExcludeOptions {\n  /**\n   * Expose this property only when transforming from plain to class instance.\n   */\n  toClassOnly?: boolean;\n\n  /**\n   * Expose this property only when transforming from class instance to plain object.\n   */\n  toPlainOnly?: boolean;\n}\n"]}