{"version": 3, "file": "exclude-metadata.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/metadata/exclude-metadata.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ExcludeOptions } from '..';\n\n/**\n * This object represents metadata assigned to a property via the @Exclude decorator.\n */\nexport interface ExcludeMetadata {\n  target: Function;\n\n  /**\n   * The property name this metadata belongs to on the target (class or property).\n   *\n   * Note: If the decorator is applied to a class the propertyName will be undefined.\n   */\n  propertyName: string | undefined;\n\n  /**\n   * Options passed to the @Exclude operator for this property.\n   */\n  options: ExcludeOptions;\n}\n"]}