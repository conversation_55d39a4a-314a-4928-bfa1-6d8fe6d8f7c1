{"version": 3, "file": "type-options.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/decorator-options/type-options.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DiscriminatorDescriptor } from './type-discriminator-descriptor.interface';\n\n/**\n * Possible transformation options for the @Type decorator.\n */\nexport interface TypeOptions {\n  /**\n   * Optional discriminator object, when provided the property value will be\n   * initialized according to the specified object.\n   */\n  discriminator?: DiscriminatorDescriptor;\n\n  /**\n   * Indicates whether to keep the discriminator property on the\n   * transformed object or not. Disabled by default.\n   *\n   * @default false\n   */\n  keepDiscriminatorProperty?: boolean;\n}\n"]}