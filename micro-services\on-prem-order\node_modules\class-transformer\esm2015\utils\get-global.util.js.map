{"version": 3, "file": "get-global.util.js", "sourceRoot": "", "sources": ["../../../src/utils/get-global.util.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,MAAM,UAAU,SAAS;IACvB,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;QACrC,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,OAAO,MAAM,CAAC;KACf;IAED,6DAA6D;IAC7D,yCAAyC;IACzC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACjC,6DAA6D;QAC7D,yCAAyC;QACzC,OAAO,MAAM,CAAC;KACf;IAED,6DAA6D;IAC7D,uCAAuC;IACvC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B,6DAA6D;QAC7D,uCAAuC;QACvC,OAAO,IAAI,CAAC;KACb;AACH,CAAC", "sourcesContent": ["/**\n * This function returns the global object across Node and browsers.\n *\n * Note: `globalThis` is the standardized approach however it has been added to\n * Node.js in version 12. We need to include this snippet until Node 12 EOL.\n */\nexport function getGlobal() {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  }\n\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore: Cannot find name 'window'.\n  if (typeof window !== 'undefined') {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore: Cannot find name 'window'.\n    return window;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore: Cannot find name 'self'.\n  if (typeof self !== 'undefined') {\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore: Cannot find name 'self'.\n    return self;\n  }\n}\n"]}