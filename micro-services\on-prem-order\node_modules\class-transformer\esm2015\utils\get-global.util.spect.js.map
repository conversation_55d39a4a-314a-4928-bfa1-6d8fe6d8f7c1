{"version": 3, "file": "get-global.util.spect.js", "sourceRoot": "", "sources": ["../../../src/utils/get-global.util.spect.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,GAAG,CAAC;AAE9B,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;QAC/D,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;QACpE,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;QAChC,OAAO,MAAM,CAAC,MAAM,CAAC;QAErB,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;IAC5B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import { getGlobal } from '.';\n\ndescribe('getGlobal()', () => {\n  it('should return true if <PERSON><PERSON><PERSON> is present in globalThis', () => {\n    expect(getGlobal().Buffer).toBe(true);\n  });\n\n  it('should return false if <PERSON><PERSON><PERSON> is not present in globalThis', () => {\n    const bufferImp = global.Buffer;\n    delete global.Buffer;\n\n    expect(getGlobal().Buffer).toBe(false);\n\n    global.Buffer = bufferImp;\n  });\n});\n"]}