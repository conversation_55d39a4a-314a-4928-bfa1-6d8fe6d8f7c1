{"version": 3, "file": "TransformOperationExecutor.js", "sourceRoot": "", "sources": ["../../src/TransformOperationExecutor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,WAAW,CAAC;AAEnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAE/C,SAAS,oBAAoB,CAAC,SAAmB;IAC/C,IAAM,KAAK,GAAG,IAAK,SAAiB,EAAE,CAAC;IACvC,IAAI,CAAC,CAAC,KAAK,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;QACjD,OAAO,EAAE,CAAC;KACX;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;IAOE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,oCAAoB,kBAAsC,EAAU,OAA8B;QAA9E,uBAAkB,GAAlB,kBAAkB,CAAoB;QAAU,YAAO,GAAP,OAAO,CAAuB;QAVlG,4EAA4E;QAC5E,qBAAqB;QACrB,4EAA4E;QAEpE,mBAAc,GAAG,IAAI,GAAG,EAAuB,CAAC;IAM6C,CAAC;IAEtG,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,8CAAS,GAAT,UACE,MAAyD,EACzD,KAAwD,EACxD,UAAmC,EACnC,SAAmB,EACnB,KAAc,EACd,KAAiB;QANnB,iBA2VC;QArVC,sBAAA,EAAA,SAAiB;QAEjB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,GAAG,EAAE;YAChD,IAAM,UAAQ,GACZ,SAAS,IAAI,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;gBACxE,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC;gBACjC,CAAC,CAAC,EAAE,CAAC;YACR,KAAe,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,KAAK;gBACvC,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACrD,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBACnE,IAAI,cAAc,SAAA,CAAC;oBACnB,IACE,OAAO,UAAU,KAAK,UAAU;wBAChC,UAAU;wBACV,UAAU,CAAC,OAAO;wBAClB,UAAU,CAAC,OAAO,CAAC,aAAa;wBAChC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ;wBACzC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EACzC;wBACA,IAAI,KAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;4BACjE,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAC7D,UAAA,OAAO;gCACL,OAAA,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAE,UAAuC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;4BAAlG,CAAkG,CACrG,CAAC;4BACF,IAAM,OAAO,GAAoB,EAAE,SAAS,EAAE,UAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;4BAChG,IAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;4BACjD,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;4BACpG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,yBAAyB;gCAC/C,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;yBAC9D;wBAED,IAAI,KAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;4BACjE,cAAc,GAAG,QAAQ,CAAC,WAAW,CAAC;yBACvC;wBACD,IAAI,KAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;4BACjE,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAClG,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,WAAW,EAAtC,CAAsC,CAClD,CAAC,IAAI,CAAC;yBACR;qBACF;yBAAM;wBACL,cAAc,GAAG,UAAU,CAAC;qBAC7B;oBACD,IAAM,OAAK,GAAG,KAAI,CAAC,SAAS,CAC1B,SAAS,EACT,QAAQ,EACR,cAAc,EACd,SAAS,EACT,QAAQ,YAAY,GAAG,EACvB,KAAK,GAAG,CAAC,CACV,CAAC;oBAEF,IAAI,UAAQ,YAAY,GAAG,EAAE;wBAC3B,UAAQ,CAAC,GAAG,CAAC,OAAK,CAAC,CAAC;qBACrB;yBAAM;wBACL,UAAQ,CAAC,IAAI,CAAC,OAAK,CAAC,CAAC;qBACtB;iBACF;qBAAM,IAAI,KAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oBACxE,IAAI,UAAQ,YAAY,GAAG,EAAE;wBAC3B,UAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;qBACxB;yBAAM;wBACL,UAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBACzB;iBACF;YACH,CAAC,CAAC,CAAC;YACH,OAAO,UAAQ,CAAC;SACjB;aAAM,IAAI,UAAU,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE;YAC1C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YACxD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM,IAAI,UAAU,KAAK,MAAM,IAAI,CAAC,KAAK,EAAE;YAC1C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YACxD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM,IAAI,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,EAAE;YAC3C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YACxD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SACvB;aAAM,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACnE,IAAI,KAAK,YAAY,IAAI,EAAE;gBACzB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aAClC;YACD,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YACxD,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;SACxB;aAAM,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,KAAK,YAAY,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;YAC/F,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,KAAK,CAAC;YACxD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3B;aAAM,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;YACrC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBACjC,KAAK,CAAC,IAAI,CACR,UAAC,IAAS,IAAK,OAAA,OAAO,CAAC,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,EAArF,CAAqF,EACpG,MAAM,CACP,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;YACpG,oEAAoE;YACpE,kHAAkH;YAClH,OAAO,KAAK,CAAC,CAAC,8BAA8B;SAC7C;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;YACtD,wBAAwB;YACxB,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM,CAAC,gEAAgE;gBAC9G,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,EAAE;oBACxD,uEAAuE;oBACvE,kEAAkE;oBAClE,6EAA6E;oBAC7E,mDAAmD;iBACpD;qBAAM;oBACL,kDAAkD;oBAClD,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC;iBAChC;YACH,IAAI,CAAC,UAAU,IAAI,MAAM;gBAAE,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;YAE3D,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBACpC,sDAAsD;gBACtD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAChC;YAED,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAsB,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,QAAQ,GAAQ,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACzC,IACE,CAAC,MAAM;gBACP,CAAC,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;oBAC5D,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC,EAChE;gBACA,IAAI,KAAK,EAAE;oBACT,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;iBACtB;qBAAM,IAAI,UAAU,EAAE;oBACrB,QAAQ,GAAG,IAAK,UAAkB,EAAE,CAAC;iBACtC;qBAAM;oBACL,QAAQ,GAAG,EAAE,CAAC;iBACf;aACF;oCAGU,GAAG;gBACZ,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,aAAa,EAAE;;iBAEjD;gBAED,IAAM,QAAQ,GAAG,GAAG,CAAC;gBACrB,IAAI,WAAW,GAAG,GAAG,EACnB,YAAY,GAAG,GAAG,CAAC;gBACrB,IAAI,CAAC,OAAK,OAAO,CAAC,gBAAgB,IAAI,UAAU,EAAE;oBAChD,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;wBACjE,IAAM,cAAc,GAAG,sBAAsB,CAAC,8BAA8B,CAAC,UAAsB,EAAE,GAAG,CAAC,CAAC;wBAC1G,IAAI,cAAc,EAAE;4BAClB,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;4BAC3C,WAAW,GAAG,cAAc,CAAC,YAAY,CAAC;yBAC3C;qBACF;yBAAM,IACL,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;wBAC7D,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAC7D;wBACA,IAAM,cAAc,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,UAAsB,EAAE,GAAG,CAAC,CAAC;wBAC9F,IAAI,cAAc,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE;4BAC3E,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;yBAC3C;qBACF;iBACF;gBAED,iBAAiB;gBACjB,IAAI,QAAQ,GAAQ,SAAS,CAAC;gBAC9B,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oBACjE;;;;;uBAKG;oBACH,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;iBAC5B;qBAAM;oBACL,IAAI,KAAK,YAAY,GAAG,EAAE;wBACxB,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;qBAChC;yBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,QAAQ,EAAE;wBAC9C,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;qBAC9B;yBAAM;wBACL,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;qBAC5B;iBACF;gBAED,mBAAmB;gBACnB,IAAI,IAAI,GAAQ,SAAS,EACvB,aAAa,GAAG,QAAQ,YAAY,GAAG,CAAC;gBAC1C,IAAI,UAAU,IAAI,KAAK,EAAE;oBACvB,IAAI,GAAG,UAAU,CAAC;iBACnB;qBAAM,IAAI,UAAU,EAAE;oBACrB,IAAM,UAAQ,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,UAAsB,EAAE,YAAY,CAAC,CAAC;oBAC/F,IAAI,UAAQ,EAAE;wBACZ,IAAM,OAAO,GAAoB,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;wBAChG,IAAM,OAAO,GAAG,UAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,UAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAQ,CAAC,aAAa,CAAC;wBAChG,IACE,UAAQ,CAAC,OAAO;4BAChB,UAAQ,CAAC,OAAO,CAAC,aAAa;4BAC9B,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ;4BACvC,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,EACvC;4BACA,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,KAAK,CAAC,EAAE;gCACvC,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oCACjE,IAAI,GAAG,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,OAAO;wCACzD,IAAI,QAAQ,IAAI,QAAQ,YAAY,MAAM,IAAI,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,IAAI,QAAQ,EAAE;4CACjG,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;yCAC3E;oCACH,CAAC,CAAC,CAAC;oCACH,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oCAC5D,IAAI,CAAC,UAAQ,CAAC,OAAO,CAAC,yBAAyB,EAAE;wCAC/C,IAAI,QAAQ,IAAI,QAAQ,YAAY,MAAM,IAAI,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,IAAI,QAAQ,EAAE;4CACjG,OAAO,QAAQ,CAAC,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;yCAC1D;qCACF;iCACF;gCACD,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oCACjE,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC;iCAC7B;gCACD,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oCACjE,IAAI,QAAQ,EAAE;wCACZ,QAAQ,CAAC,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,UAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAC9F,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,WAAW,EAAtC,CAAsC,CAClD,CAAC,IAAI,CAAC;qCACR;iCACF;6BACF;iCAAM;gCACL,IAAI,GAAG,UAAQ,CAAC;6BACjB;yBACF;6BAAM;4BACL,IAAI,GAAG,OAAO,CAAC;yBAChB;wBACD,aAAa,GAAG,aAAa,IAAI,UAAQ,CAAC,aAAa,KAAK,GAAG,CAAC;qBACjE;yBAAM,IAAI,OAAK,OAAO,CAAC,UAAU,EAAE;wBAClC,oCAAoC;wBACpC,OAAK,OAAO,CAAC,UAAU;6BACpB,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAA3D,CAA2D,CAAC;6BAC1E,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,CAAC,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,EAArC,CAAqC,CAAC,CAAC;qBAC1D;yBAAM,IACL,OAAK,OAAO,CAAC,wBAAwB;wBACrC,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAC7D;wBACA,wFAAwF;wBACxF,sHAAsH;wBACtH,IAAM,aAAa,GAAI,OAAe,CAAC,WAAW,CAChD,aAAa,EACZ,UAAuB,CAAC,SAAS,EAClC,YAAY,CACb,CAAC;wBAEF,IAAI,aAAa,EAAE;4BACjB,IAAI,GAAG,aAAa,CAAC;yBACtB;qBACF;iBACF;gBAED,wDAAwD;gBACxD,IAAM,WAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAC9C,CAAC,CAAC,OAAK,gBAAgB,CAAC,UAAsB,EAAE,YAAY,CAAC;oBAC7D,CAAC,CAAC,SAAS,CAAC;gBAEd,iHAAiH;gBACjH,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAExD,+CAA+C;gBAC/C,yDAAyD;gBACzD,2IAA2I;gBAC3I,qIAAqI;gBAErI,oFAAoF;gBACpF,IAAI,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE;oBAClC,IAAM,UAAU,GAAG,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;oBAChG,IACE,CAAC,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;wBAC5D,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC;wBAChE,6DAA6D;wBAC7D,CAAC,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,YAAY,QAAQ,CAAC;0CAGrE;iBACZ;gBAED,IAAI,CAAC,OAAK,OAAO,CAAC,mBAAmB,IAAI,CAAC,OAAK,UAAU,CAAC,QAAQ,CAAC,EAAE;oBACnE,IAAM,YAAY,GAAG,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;oBACvG,IAAI,UAAU,SAAA,CAAC;oBAEf,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;wBACjE,qBAAqB;wBACrB,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;wBACjC,8BAA8B;wBAC9B,UAAU,GAAG,OAAK,0BAA0B,CAC1C,UAAU,EACV,UAAsB,EACtB,YAAY,EACZ,KAAK,EACL,OAAK,kBAAkB,CACxB,CAAC;wBACF,yFAAyF;wBACzF,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;wBACxE,mCAAmC;wBACnC,UAAU,GAAG,OAAK,SAAS,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,WAAS,EAAE,aAAa,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;qBAC/F;yBAAM;wBACL,IAAI,QAAQ,KAAK,SAAS,IAAI,OAAK,OAAO,CAAC,mBAAmB,EAAE;4BAC9D,wCAAwC;4BACxC,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;yBACpC;6BAAM;4BACL,UAAU,GAAG,OAAK,SAAS,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAS,EAAE,aAAa,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;4BAC5F,UAAU,GAAG,OAAK,0BAA0B,CAC1C,UAAU,EACV,UAAsB,EACtB,YAAY,EACZ,KAAK,EACL,OAAK,kBAAkB,CACxB,CAAC;yBACH;qBACF;oBAED,IAAI,UAAU,KAAK,SAAS,IAAI,OAAK,OAAO,CAAC,iBAAiB,EAAE;wBAC9D,IAAI,QAAQ,YAAY,GAAG,EAAE;4BAC3B,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;yBACvC;6BAAM;4BACL,QAAQ,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;yBACpC;qBACF;iBACF;qBAAM,IAAI,OAAK,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;oBACxE,IAAI,UAAU,GAAG,QAAQ,CAAC;oBAC1B,UAAU,GAAG,OAAK,0BAA0B,CAC1C,UAAU,EACV,UAAsB,EACtB,GAAG,EACH,KAAK,EACL,OAAK,kBAAkB,CACxB,CAAC;oBACF,IAAI,UAAU,KAAK,SAAS,IAAI,OAAK,OAAO,CAAC,iBAAiB,EAAE;wBAC9D,IAAI,QAAQ,YAAY,GAAG,EAAE;4BAC3B,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;yBACvC;6BAAM;4BACL,QAAQ,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;yBACpC;qBACF;iBACF;;;YAzMH,qBAAqB;YACrB,KAAkB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI;gBAAjB,IAAM,GAAG,aAAA;wBAAH,GAAG;aAyMb;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBACpC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACnC;YAED,OAAO,QAAQ,CAAC;SACjB;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAEO,+DAA0B,GAAlC,UACE,KAAU,EACV,MAAgB,EAChB,GAAW,EACX,GAAQ,EACR,kBAAsC;QALxC,iBAoCC;QA7BC,IAAI,SAAS,GAAG,sBAAsB,CAAC,sBAAsB,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEpG,2BAA2B;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACtC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ;gBACnC,IAAI,CAAC,QAAQ,CAAC,OAAO;oBAAE,OAAO,IAAI,CAAC;gBAEnC,OAAO,KAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;SACJ;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YACrD,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ;gBACnC,IAAI,CAAC,QAAQ,CAAC,OAAO;oBAAE,OAAO,IAAI,CAAC;gBAEnC,OAAO,KAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,QAAQ;gBACnC,OAAO,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;YAC1F,CAAC,CAAC,CAAC;SACJ;QAED,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;YACxB,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,IAAI,EAAE,kBAAkB,EAAE,OAAO,EAAE,KAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iCAAiC;IACzB,+CAAU,GAAlB,UAAmB,MAA2B;QAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAEO,qDAAgB,GAAxB,UAAyB,MAAgB,EAAE,YAAoB;QAC7D,IAAI,CAAC,MAAM;YAAE,OAAO,SAAS,CAAC;QAC9B,IAAM,IAAI,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/C,CAAC;IAEO,4CAAO,GAAf,UAAgB,MAAgB,EAAE,MAA2B,EAAE,KAAc;QAA7E,iBAuGC;QAtGC,+BAA+B;QAC/B,IAAI,QAAQ,GAAG,sBAAsB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,QAAQ,KAAK,MAAM;YAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAC,CAAC,gCAAgC;QAE1G,mCAAmC;QACnC,IAAI,IAAI,GAAU,EAAE,CAAC;QACrB,IAAI,QAAQ,KAAK,WAAW,IAAI,KAAK,EAAE;YACrC,IAAI,MAAM,YAAY,GAAG,EAAE;gBACzB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;aAClC;iBAAM;gBACL,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC5B;SACF;QAED,IAAI,KAAK,EAAE;YACT,4DAA4D;YAC5D,OAAO,IAAI,CAAC;SACb;QAED;;;WAGG;QACH,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,MAAM,EAAE;YACnF,IAAM,iBAAiB,GAAG,sBAAsB,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvG,IAAM,kBAAkB,GAAG,sBAAsB,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzG,IAAI,mCAAO,iBAAiB,SAAK,kBAAkB,OAAC,CAAC;SACtD;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,MAAM,EAAE;YAC5C,kCAAkC;YAClC,IAAI,iBAAiB,GAAG,sBAAsB,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACrG,IAAI,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,EAAE;gBACjE,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAA,GAAG;oBAC3C,IAAM,cAAc,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAC9E,IAAI,cAAc,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE;wBAC3E,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;qBACpC;oBAED,OAAO,GAAG,CAAC;gBACb,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBACxC,IAAI,GAAG,iBAAiB,CAAC;aAC1B;iBAAM;gBACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;aACvC;YAED,8BAA8B;YAC9B,IAAM,oBAAkB,GAAG,sBAAsB,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzG,IAAI,oBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG;oBACpB,OAAO,CAAC,oBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;aACJ;YAED,2BAA2B;YAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;gBACtC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG;oBACpB,IAAM,cAAc,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAC9E,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,OAAO;wBAAE,OAAO,IAAI,CAAC;oBAE5D,OAAO,KAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACvF,CAAC,CAAC,CAAC;aACJ;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;gBACrD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG;oBACpB,IAAM,cAAc,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAC9E,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,OAAO;wBAAE,OAAO,IAAI,CAAC;oBAE5D,OAAO,KAAI,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG;oBACpB,IAAM,cAAc,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBAC9E,OAAO,CACL,CAAC,cAAc;wBACf,CAAC,cAAc,CAAC,OAAO;wBACvB,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM;wBAC9B,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CACtC,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;SACF;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE;YACvE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,GAAG;gBACpB,OAAA,KAAI,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,UAAA,MAAM;oBACvC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC;gBACjD,CAAC,CAAC;YAFF,CAEE,CACH,CAAC;SACH;QAED,gCAAgC;QAChC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,EAAE,IAAI;YAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iDAAY,GAApB,UAAqB,KAAa,EAAE,KAAa;QAC/C,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,QAAQ,IAAI,KAAK;YAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;QAChE,IAAI,QAAQ,IAAI,KAAK;YAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;QAE/D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,gDAAW,GAAnB,UAAoB,MAAgB;QAClC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,WAAW,IAAI,OAAA,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAA5B,CAA4B,CAAC,CAAC;IAC/E,CAAC;IACH,iCAAC;AAAD,CAAC,AArhBD,IAqhBC", "sourcesContent": ["import { defaultMetadataStorage } from './storage';\nimport { ClassTransformOptions, TypeHelpOptions, TypeMetadata, TypeOptions } from './interfaces';\nimport { TransformationType } from './enums';\nimport { getGlobal, isPromise } from './utils';\n\nfunction instantiateArrayType(arrayType: Function): Array<any> | Set<any> {\n  const array = new (arrayType as any)();\n  if (!(array instanceof Set) && !('push' in array)) {\n    return [];\n  }\n  return array;\n}\n\nexport class TransformOperationExecutor {\n  // -------------------------------------------------------------------------\n  // Private Properties\n  // -------------------------------------------------------------------------\n\n  private recursionStack = new Set<Record<string, any>>();\n\n  // -------------------------------------------------------------------------\n  // Constructor\n  // -------------------------------------------------------------------------\n\n  constructor(private transformationType: TransformationType, private options: ClassTransformOptions) {}\n\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  transform(\n    source: Record<string, any> | Record<string, any>[] | any,\n    value: Record<string, any> | Record<string, any>[] | any,\n    targetType: Function | TypeMetadata,\n    arrayType: Function,\n    isMap: boolean,\n    level: number = 0\n  ): any {\n    if (Array.isArray(value) || value instanceof Set) {\n      const newValue =\n        arrayType && this.transformationType === TransformationType.PLAIN_TO_CLASS\n          ? instantiateArrayType(arrayType)\n          : [];\n      (value as any[]).forEach((subValue, index) => {\n        const subSource = source ? source[index] : undefined;\n        if (!this.options.enableCircularCheck || !this.isCircular(subValue)) {\n          let realTargetType;\n          if (\n            typeof targetType !== 'function' &&\n            targetType &&\n            targetType.options &&\n            targetType.options.discriminator &&\n            targetType.options.discriminator.property &&\n            targetType.options.discriminator.subTypes\n          ) {\n            if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n              realTargetType = targetType.options.discriminator.subTypes.find(\n                subType =>\n                  subType.name === subValue[(targetType as { options: TypeOptions }).options.discriminator.property]\n              );\n              const options: TypeHelpOptions = { newObject: newValue, object: subValue, property: undefined };\n              const newType = targetType.typeFunction(options);\n              realTargetType === undefined ? (realTargetType = newType) : (realTargetType = realTargetType.value);\n              if (!targetType.options.keepDiscriminatorProperty)\n                delete subValue[targetType.options.discriminator.property];\n            }\n\n            if (this.transformationType === TransformationType.CLASS_TO_CLASS) {\n              realTargetType = subValue.constructor;\n            }\n            if (this.transformationType === TransformationType.CLASS_TO_PLAIN) {\n              subValue[targetType.options.discriminator.property] = targetType.options.discriminator.subTypes.find(\n                subType => subType.value === subValue.constructor\n              ).name;\n            }\n          } else {\n            realTargetType = targetType;\n          }\n          const value = this.transform(\n            subSource,\n            subValue,\n            realTargetType,\n            undefined,\n            subValue instanceof Map,\n            level + 1\n          );\n\n          if (newValue instanceof Set) {\n            newValue.add(value);\n          } else {\n            newValue.push(value);\n          }\n        } else if (this.transformationType === TransformationType.CLASS_TO_CLASS) {\n          if (newValue instanceof Set) {\n            newValue.add(subValue);\n          } else {\n            newValue.push(subValue);\n          }\n        }\n      });\n      return newValue;\n    } else if (targetType === String && !isMap) {\n      if (value === null || value === undefined) return value;\n      return String(value);\n    } else if (targetType === Number && !isMap) {\n      if (value === null || value === undefined) return value;\n      return Number(value);\n    } else if (targetType === Boolean && !isMap) {\n      if (value === null || value === undefined) return value;\n      return Boolean(value);\n    } else if ((targetType === Date || value instanceof Date) && !isMap) {\n      if (value instanceof Date) {\n        return new Date(value.valueOf());\n      }\n      if (value === null || value === undefined) return value;\n      return new Date(value);\n    } else if (!!getGlobal().Buffer && (targetType === Buffer || value instanceof Buffer) && !isMap) {\n      if (value === null || value === undefined) return value;\n      return Buffer.from(value);\n    } else if (isPromise(value) && !isMap) {\n      return new Promise((resolve, reject) => {\n        value.then(\n          (data: any) => resolve(this.transform(undefined, data, targetType, undefined, undefined, level + 1)),\n          reject\n        );\n      });\n    } else if (!isMap && value !== null && typeof value === 'object' && typeof value.then === 'function') {\n      // Note: We should not enter this, as promise has been handled above\n      // This option simply returns the Promise preventing a JS error from happening and should be an inaccessible path.\n      return value; // skip promise transformation\n    } else if (typeof value === 'object' && value !== null) {\n      // try to guess the type\n      if (!targetType && value.constructor !== Object /* && TransformationType === TransformationType.CLASS_TO_PLAIN*/)\n        if (!Array.isArray(value) && value.constructor === Array) {\n          // Somebody attempts to convert special Array like object to Array, eg:\n          // const evilObject = { '100000000': '100000000', __proto__: [] };\n          // This could be used to cause Denial-of-service attack so we don't allow it.\n          // See prevent-array-bomb.spec.ts for more details.\n        } else {\n          // We are good we can use the built-in constructor\n          targetType = value.constructor;\n        }\n      if (!targetType && source) targetType = source.constructor;\n\n      if (this.options.enableCircularCheck) {\n        // add transformed type to prevent circular references\n        this.recursionStack.add(value);\n      }\n\n      const keys = this.getKeys(targetType as Function, value, isMap);\n      let newValue: any = source ? source : {};\n      if (\n        !source &&\n        (this.transformationType === TransformationType.PLAIN_TO_CLASS ||\n          this.transformationType === TransformationType.CLASS_TO_CLASS)\n      ) {\n        if (isMap) {\n          newValue = new Map();\n        } else if (targetType) {\n          newValue = new (targetType as any)();\n        } else {\n          newValue = {};\n        }\n      }\n\n      // traverse over keys\n      for (const key of keys) {\n        if (key === '__proto__' || key === 'constructor') {\n          continue;\n        }\n\n        const valueKey = key;\n        let newValueKey = key,\n          propertyName = key;\n        if (!this.options.ignoreDecorators && targetType) {\n          if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n            const exposeMetadata = defaultMetadataStorage.findExposeMetadataByCustomName(targetType as Function, key);\n            if (exposeMetadata) {\n              propertyName = exposeMetadata.propertyName;\n              newValueKey = exposeMetadata.propertyName;\n            }\n          } else if (\n            this.transformationType === TransformationType.CLASS_TO_PLAIN ||\n            this.transformationType === TransformationType.CLASS_TO_CLASS\n          ) {\n            const exposeMetadata = defaultMetadataStorage.findExposeMetadata(targetType as Function, key);\n            if (exposeMetadata && exposeMetadata.options && exposeMetadata.options.name) {\n              newValueKey = exposeMetadata.options.name;\n            }\n          }\n        }\n\n        // get a subvalue\n        let subValue: any = undefined;\n        if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n          /**\n           * This section is added for the following report:\n           * https://github.com/typestack/class-transformer/issues/596\n           *\n           * We should not call functions or constructors when transforming to class.\n           */\n          subValue = value[valueKey];\n        } else {\n          if (value instanceof Map) {\n            subValue = value.get(valueKey);\n          } else if (value[valueKey] instanceof Function) {\n            subValue = value[valueKey]();\n          } else {\n            subValue = value[valueKey];\n          }\n        }\n\n        // determine a type\n        let type: any = undefined,\n          isSubValueMap = subValue instanceof Map;\n        if (targetType && isMap) {\n          type = targetType;\n        } else if (targetType) {\n          const metadata = defaultMetadataStorage.findTypeMetadata(targetType as Function, propertyName);\n          if (metadata) {\n            const options: TypeHelpOptions = { newObject: newValue, object: value, property: propertyName };\n            const newType = metadata.typeFunction ? metadata.typeFunction(options) : metadata.reflectedType;\n            if (\n              metadata.options &&\n              metadata.options.discriminator &&\n              metadata.options.discriminator.property &&\n              metadata.options.discriminator.subTypes\n            ) {\n              if (!(value[valueKey] instanceof Array)) {\n                if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                  type = metadata.options.discriminator.subTypes.find(subType => {\n                    if (subValue && subValue instanceof Object && metadata.options.discriminator.property in subValue) {\n                      return subType.name === subValue[metadata.options.discriminator.property];\n                    }\n                  });\n                  type === undefined ? (type = newType) : (type = type.value);\n                  if (!metadata.options.keepDiscriminatorProperty) {\n                    if (subValue && subValue instanceof Object && metadata.options.discriminator.property in subValue) {\n                      delete subValue[metadata.options.discriminator.property];\n                    }\n                  }\n                }\n                if (this.transformationType === TransformationType.CLASS_TO_CLASS) {\n                  type = subValue.constructor;\n                }\n                if (this.transformationType === TransformationType.CLASS_TO_PLAIN) {\n                  if (subValue) {\n                    subValue[metadata.options.discriminator.property] = metadata.options.discriminator.subTypes.find(\n                      subType => subType.value === subValue.constructor\n                    ).name;\n                  }\n                }\n              } else {\n                type = metadata;\n              }\n            } else {\n              type = newType;\n            }\n            isSubValueMap = isSubValueMap || metadata.reflectedType === Map;\n          } else if (this.options.targetMaps) {\n            // try to find a type in target maps\n            this.options.targetMaps\n              .filter(map => map.target === targetType && !!map.properties[propertyName])\n              .forEach(map => (type = map.properties[propertyName]));\n          } else if (\n            this.options.enableImplicitConversion &&\n            this.transformationType === TransformationType.PLAIN_TO_CLASS\n          ) {\n            // if we have no registererd type via the @Type() decorator then we check if we have any\n            // type declarations in reflect-metadata (type declaration is emited only if some decorator is added to the property.)\n            const reflectedType = (Reflect as any).getMetadata(\n              'design:type',\n              (targetType as Function).prototype,\n              propertyName\n            );\n\n            if (reflectedType) {\n              type = reflectedType;\n            }\n          }\n        }\n\n        // if value is an array try to get its custom array type\n        const arrayType = Array.isArray(value[valueKey])\n          ? this.getReflectedType(targetType as Function, propertyName)\n          : undefined;\n\n        // const subValueKey = TransformationType === TransformationType.PLAIN_TO_CLASS && newKeyName ? newKeyName : key;\n        const subSource = source ? source[valueKey] : undefined;\n\n        // if its deserialization then type if required\n        // if we uncomment this types like string[] will not work\n        // if (this.transformationType === TransformationType.PLAIN_TO_CLASS && !type && subValue instanceof Object && !(subValue instanceof Date))\n        //     throw new Error(`Cannot determine type for ${(targetType as any).name }.${propertyName}, did you forget to specify a @Type?`);\n\n        // if newValue is a source object that has method that match newKeyName then skip it\n        if (newValue.constructor.prototype) {\n          const descriptor = Object.getOwnPropertyDescriptor(newValue.constructor.prototype, newValueKey);\n          if (\n            (this.transformationType === TransformationType.PLAIN_TO_CLASS ||\n              this.transformationType === TransformationType.CLASS_TO_CLASS) &&\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            ((descriptor && !descriptor.set) || newValue[newValueKey] instanceof Function)\n          )\n            //  || TransformationType === TransformationType.CLASS_TO_CLASS\n            continue;\n        }\n\n        if (!this.options.enableCircularCheck || !this.isCircular(subValue)) {\n          const transformKey = this.transformationType === TransformationType.PLAIN_TO_CLASS ? newValueKey : key;\n          let finalValue;\n\n          if (this.transformationType === TransformationType.CLASS_TO_PLAIN) {\n            // Get original value\n            finalValue = value[transformKey];\n            // Apply custom transformation\n            finalValue = this.applyCustomTransformations(\n              finalValue,\n              targetType as Function,\n              transformKey,\n              value,\n              this.transformationType\n            );\n            // If nothing change, it means no custom transformation was applied, so use the subValue.\n            finalValue = value[transformKey] === finalValue ? subValue : finalValue;\n            // Apply the default transformation\n            finalValue = this.transform(subSource, finalValue, type, arrayType, isSubValueMap, level + 1);\n          } else {\n            if (subValue === undefined && this.options.exposeDefaultValues) {\n              // Set default value if nothing provided\n              finalValue = newValue[newValueKey];\n            } else {\n              finalValue = this.transform(subSource, subValue, type, arrayType, isSubValueMap, level + 1);\n              finalValue = this.applyCustomTransformations(\n                finalValue,\n                targetType as Function,\n                transformKey,\n                value,\n                this.transformationType\n              );\n            }\n          }\n\n          if (finalValue !== undefined || this.options.exposeUnsetFields) {\n            if (newValue instanceof Map) {\n              newValue.set(newValueKey, finalValue);\n            } else {\n              newValue[newValueKey] = finalValue;\n            }\n          }\n        } else if (this.transformationType === TransformationType.CLASS_TO_CLASS) {\n          let finalValue = subValue;\n          finalValue = this.applyCustomTransformations(\n            finalValue,\n            targetType as Function,\n            key,\n            value,\n            this.transformationType\n          );\n          if (finalValue !== undefined || this.options.exposeUnsetFields) {\n            if (newValue instanceof Map) {\n              newValue.set(newValueKey, finalValue);\n            } else {\n              newValue[newValueKey] = finalValue;\n            }\n          }\n        }\n      }\n\n      if (this.options.enableCircularCheck) {\n        this.recursionStack.delete(value);\n      }\n\n      return newValue;\n    } else {\n      return value;\n    }\n  }\n\n  private applyCustomTransformations(\n    value: any,\n    target: Function,\n    key: string,\n    obj: any,\n    transformationType: TransformationType\n  ): boolean {\n    let metadatas = defaultMetadataStorage.findTransformMetadatas(target, key, this.transformationType);\n\n    // apply versioning options\n    if (this.options.version !== undefined) {\n      metadatas = metadatas.filter(metadata => {\n        if (!metadata.options) return true;\n\n        return this.checkVersion(metadata.options.since, metadata.options.until);\n      });\n    }\n\n    // apply grouping options\n    if (this.options.groups && this.options.groups.length) {\n      metadatas = metadatas.filter(metadata => {\n        if (!metadata.options) return true;\n\n        return this.checkGroups(metadata.options.groups);\n      });\n    } else {\n      metadatas = metadatas.filter(metadata => {\n        return !metadata.options || !metadata.options.groups || !metadata.options.groups.length;\n      });\n    }\n\n    metadatas.forEach(metadata => {\n      value = metadata.transformFn({ value, key, obj, type: transformationType, options: this.options });\n    });\n\n    return value;\n  }\n\n  // preventing circular references\n  private isCircular(object: Record<string, any>): boolean {\n    return this.recursionStack.has(object);\n  }\n\n  private getReflectedType(target: Function, propertyName: string): Function | undefined {\n    if (!target) return undefined;\n    const meta = defaultMetadataStorage.findTypeMetadata(target, propertyName);\n    return meta ? meta.reflectedType : undefined;\n  }\n\n  private getKeys(target: Function, object: Record<string, any>, isMap: boolean): string[] {\n    // determine exclusion strategy\n    let strategy = defaultMetadataStorage.getStrategy(target);\n    if (strategy === 'none') strategy = this.options.strategy || 'exposeAll'; // exposeAll is default strategy\n\n    // get all keys that need to expose\n    let keys: any[] = [];\n    if (strategy === 'exposeAll' || isMap) {\n      if (object instanceof Map) {\n        keys = Array.from(object.keys());\n      } else {\n        keys = Object.keys(object);\n      }\n    }\n\n    if (isMap) {\n      // expose & exclude do not apply for map keys only to fields\n      return keys;\n    }\n\n    /**\n     * If decorators are ignored but we don't want the extraneous values, then we use the\n     * metadata to decide which property is needed, but doesn't apply the decorator effect.\n     */\n    if (this.options.ignoreDecorators && this.options.excludeExtraneousValues && target) {\n      const exposedProperties = defaultMetadataStorage.getExposedProperties(target, this.transformationType);\n      const excludedProperties = defaultMetadataStorage.getExcludedProperties(target, this.transformationType);\n      keys = [...exposedProperties, ...excludedProperties];\n    }\n\n    if (!this.options.ignoreDecorators && target) {\n      // add all exposed to list of keys\n      let exposedProperties = defaultMetadataStorage.getExposedProperties(target, this.transformationType);\n      if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n        exposedProperties = exposedProperties.map(key => {\n          const exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n          if (exposeMetadata && exposeMetadata.options && exposeMetadata.options.name) {\n            return exposeMetadata.options.name;\n          }\n\n          return key;\n        });\n      }\n      if (this.options.excludeExtraneousValues) {\n        keys = exposedProperties;\n      } else {\n        keys = keys.concat(exposedProperties);\n      }\n\n      // exclude excluded properties\n      const excludedProperties = defaultMetadataStorage.getExcludedProperties(target, this.transformationType);\n      if (excludedProperties.length > 0) {\n        keys = keys.filter(key => {\n          return !excludedProperties.includes(key);\n        });\n      }\n\n      // apply versioning options\n      if (this.options.version !== undefined) {\n        keys = keys.filter(key => {\n          const exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n          if (!exposeMetadata || !exposeMetadata.options) return true;\n\n          return this.checkVersion(exposeMetadata.options.since, exposeMetadata.options.until);\n        });\n      }\n\n      // apply grouping options\n      if (this.options.groups && this.options.groups.length) {\n        keys = keys.filter(key => {\n          const exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n          if (!exposeMetadata || !exposeMetadata.options) return true;\n\n          return this.checkGroups(exposeMetadata.options.groups);\n        });\n      } else {\n        keys = keys.filter(key => {\n          const exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n          return (\n            !exposeMetadata ||\n            !exposeMetadata.options ||\n            !exposeMetadata.options.groups ||\n            !exposeMetadata.options.groups.length\n          );\n        });\n      }\n    }\n\n    // exclude prefixed properties\n    if (this.options.excludePrefixes && this.options.excludePrefixes.length) {\n      keys = keys.filter(key =>\n        this.options.excludePrefixes.every(prefix => {\n          return key.substr(0, prefix.length) !== prefix;\n        })\n      );\n    }\n\n    // make sure we have unique keys\n    keys = keys.filter((key, index, self) => {\n      return self.indexOf(key) === index;\n    });\n\n    return keys;\n  }\n\n  private checkVersion(since: number, until: number): boolean {\n    let decision = true;\n    if (decision && since) decision = this.options.version >= since;\n    if (decision && until) decision = this.options.version < until;\n\n    return decision;\n  }\n\n  private checkGroups(groups: string[]): boolean {\n    if (!groups) return true;\n\n    return this.options.groups.some(optionGroup => groups.includes(optionGroup));\n  }\n}\n"]}