# console-polyfill
Browser console polyfill. Makes it safe to do `console.log()`-s etc always.

## Usage

* `npm install --save console-polyfill`; `require('console-polyfill')`
* Or, just include console-polyfill before your scripts.
* Or, do `bower install console-polyfill` if you’re using [<PERSON><PERSON>](http://bower.io).

## License

[MIT](https://github.com/paulmillr/mit) (c) 2016 <PERSON> (http://paulmillr.com)
