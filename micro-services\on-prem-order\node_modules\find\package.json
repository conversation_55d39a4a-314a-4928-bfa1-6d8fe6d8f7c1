{"name": "find", "version": "0.2.9", "description": "Find files or directories by name", "url": "https://github.com/yuanchuan/find", "author": "yuanchuan <<EMAIL>> (http://yuanchuan.name)", "main": "./index.js", "dependencies": {"traverse-chain": "~0.1.0"}, "devDependencies": {"mocha": "^2.2.4", "tmp": "^0.0.25"}, "keywords": ["find", "findfile", "search", "files"], "license": "MIT", "repository": {"url": "git://github.com/yuanchuan/find.git", "type": "git"}, "bugs": {"url": "https://github.com/yuanchuan/find/issues"}, "homepage": "https://github.com/yuanchuan/find#readme", "directories": {"test": "test"}, "scripts": {"test": "mocha"}}