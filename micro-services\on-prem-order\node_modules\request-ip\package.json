{"name": "request-ip", "version": "3.3.0", "description": "A small Node.js module to retrieve the request's IP address", "keywords": ["request ip", "ip", "address", "request", "proxy", "client", "header", "X-Client-IP", "X-Forwarded-For", "CF-Connecting-IP", "Fastly-Client-IP", "True-Client-IP", "X-Real-IP", "X-Cluster-Client-IP", "X-Forwarded", "Forwarded-For", "connection.remoteAddress", "connection.socket.remoteAddress", "req.info.remoteAddress", "middleware", "ipv4", "ipv6", "fastify", "x-appengine-user-ip", "cloudflare", "Cf-Pseudo-IPv4"], "homepage": "https://github.com/p<PERSON><PERSON><PERSON>/request-ip", "bugs": {"url": "https://github.com/p<PERSON><PERSON><PERSON>/request-ip/issues"}, "repository": {"type": "git", "url": "https://github.com/p<PERSON><PERSON><PERSON>/request-ip.git"}, "license": "MIT", "author": "<PERSON><PERSON> <petar<PERSON><PERSON><PERSON>+<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "files": ["lib"], "main": "./lib/index.js", "scripts": {"build": "babel src --out-dir lib", "changelog": "github_changelog_generator -u p<PERSON><PERSON>ov -p request-ip", "coverage": "nyc report --reporter=text-lcov | coveralls", "test": "nyc --reporter=html --reporter=text --check-coverage --lines=100 --statements=100 tape ./test/*.js"}, "prettier": "@shopify/prettier-config", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@shopify/eslint-plugin": "^41.3.0", "@shopify/prettier-config": "^1.1.2", "coveralls": "^3.0.2", "eslint": "^8.16.0", "nyc": "^13.1.0", "request": "^2.54.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}}