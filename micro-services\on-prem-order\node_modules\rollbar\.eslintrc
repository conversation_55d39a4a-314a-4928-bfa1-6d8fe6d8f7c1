{
  "parser": "babel-eslint",
  "extends": "eslint:recommended",
  "parserOptions": {
    "ecmafeatures": {
      "modules": true
    }
  },
  "env": {
    // I write for browser
    "browser": true,
    // in CommonJS
    "node": true
  },
  "plugins": [
  ],
  "globals": { "Promise": true },
  "rules": {
    "strict": 0,
    "camelcase": [2, {"properties": "never"}],
    "quotes": [2, "single", "avoid-escape"],
    "no-underscore-dangle": 0,
    "no-useless-escape": 0,
    "complexity": [2, { "max": 35 }],
    "no-use-before-define": [0, { "functions": false }],
    "no-unused-vars": [2, { "argsIgnorePattern": "^_" }],
    "no-prototype-builtins": 0
  }
}
