name: Rollbar.js CI

on:
  push:
    branches: [master]
    tags: [v*]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-20.04

    strategy:
      matrix:
        include:
          - node: 14
            npm: ^8
          - node: 16
            npm: ^8
          - node: 18
            npm: ^9
          - node: 20
            npm: ^10
          - node: latest
            npm: latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up node ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}

      - name: Update npm
        run: npm install -g npm@${{ matrix.npm }}

      - name: npm install
        run: npm install

      - name: <PERSON><PERSON>
        uses: wearerequired/lint-action@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          prettier: true
          eslint: true
          eslint_args: '--max-warnings 0'
          eslint_extensions: js

      - name: Run tests
        run: npm run test_ci
