(()=>{"use strict";!function(r,t,n){var e=t.Rollbar;if(e){e.configure({payload:{notifier:{plugins:{jquery:{version:"0.0.8"}}}}});var a=function(r){if(e.error(r),t.console){var n="[reported to <PERSON>bar]";e.options&&!e.options.enabled&&(n="[Rollbar not enabled]"),t.console.log(r.message+" "+n)}};r(n).ajaxError((function(r,t,n,a){var o=t.status,i=n.url,u=n.type;if(o){var s={status:o,url:i,type:u,isAjax:!0,data:n.data,jqXHR_responseText:t.responseText,jqXHR_statusText:t.statusText},d=a||"jQuery ajax error for "+u;e.warning(d,s)}}));var o=r.fn.ready;r.fn.ready=function(r){return o.call(this,(function(t){try{r(t)}catch(r){a(r)}}))};var i=r.event.add;r.event.add=function(t,n,e,o,u){var s,d=function(r){return function(){try{return r.apply(this,arguments)}catch(r){a(r)}}};return e.handler?(s=e.handler,e.handler=d(e.handler)):(s=e,e=d(e)),s.guid?e.guid=s.guid:e.guid=s.guid=r.guid++,i.call(this,t,n,e,o,u)}}}(jQuery,window,document)})();