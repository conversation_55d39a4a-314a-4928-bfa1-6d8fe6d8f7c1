{"version": 3, "file": "rollbar.min.js", "mappings": "6BAGA,SAAUA,GACR,aACKA,EAAOC,UACVD,EAAOC,QAAU,CAAC,GASpB,IAPA,IACIC,EAAMC,EADNC,EAAMJ,EAAOC,QAEbI,EAAQ,WAAY,EACpBC,EAAa,CAAC,UACdC,EAAU,wMAE0DC,MAAM,KACvEN,EAAOI,EAAWG,OAAYL,EAAIF,KAAOE,EAAIF,GAAQ,CAAC,GAC7D,KAAOC,EAASI,EAAQE,OAAYL,EAAID,KAASC,EAAID,GAAUE,EAEhE,CAfD,CAeqB,oBAAXK,OAAyBC,KAAOD,O,sBClB1C,yBACI,aAKI,EAA6B,CAAC,aAAsB,0BAAP,EAM7C,SAA0BE,GAG9B,IAAIC,EAA8B,eAC9BC,EAAyB,iCACzBC,EAA4B,8BAEhC,MAAO,CAOHC,MAAO,SAAiCC,GACpC,QAAgC,IAArBA,EAAMC,iBAAkE,IAA7BD,EAAM,mBACxD,OAAON,KAAKQ,WAAWF,GACpB,GAAIA,EAAMG,OAASH,EAAMG,MAAMC,MAAMP,GACxC,OAAOH,KAAKW,YAAYL,GACrB,GAAIA,EAAMG,MACb,OAAOT,KAAKY,gBAAgBN,GAE5B,MAAM,IAAIO,MAAM,kCAExB,EAGAC,gBAAiB,SAA2CC,GAExD,IAA8B,IAA1BA,EAAQC,QAAQ,KAChB,MAAO,CAACD,GAGZ,IACIE,EADS,+BACMC,KAAKH,EAAQI,QAAQ,QAAS,KACjD,MAAO,CAACF,EAAM,GAAIA,EAAM,SAAMG,EAAWH,EAAM,SAAMG,EACzD,EAEAT,YAAa,SAAuCL,GAKhD,OAJeA,EAAMG,MAAMZ,MAAM,MAAMwB,QAAO,SAASC,GACnD,QAASA,EAAKZ,MAAMP,EACxB,GAAGH,MAEauB,KAAI,SAASD,GACrBA,EAAKN,QAAQ,WAAa,IAE1BM,EAAOA,EAAKH,QAAQ,aAAc,QAAQA,QAAQ,+BAAgC,KAEtF,IAAIK,EAAgBF,EAAKH,QAAQ,OAAQ,IAAIA,QAAQ,eAAgB,KAIjEM,EAAWD,EAAcd,MAAM,4BAK/BgB,GAFJF,EAAgBC,EAAWD,EAAcL,QAAQM,EAAS,GAAI,IAAMD,GAEzC3B,MAAM,OAAO8B,MAAM,GAE1CC,EAAgB5B,KAAKc,gBAAgBW,EAAWA,EAAS,GAAKC,EAAO5B,OACrE+B,EAAeH,EAAOI,KAAK,WAAQV,EACnCW,EAAW,CAAC,OAAQ,eAAef,QAAQY,EAAc,KAAO,OAAIR,EAAYQ,EAAc,GAElG,OAAO,IAAI3B,EAAW,CAClB4B,aAAcA,EACdE,SAAUA,EACVC,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAEhB,GAAGtB,KACP,EAEAY,gBAAiB,SAA2CN,GAKxD,OAJeA,EAAMG,MAAMZ,MAAM,MAAMwB,QAAO,SAASC,GACnD,OAAQA,EAAKZ,MAAMN,EACvB,GAAGJ,MAEauB,KAAI,SAASD,GAMzB,GAJIA,EAAKN,QAAQ,YAAc,IAC3BM,EAAOA,EAAKH,QAAQ,mDAAoD,SAGjD,IAAvBG,EAAKN,QAAQ,OAAsC,IAAvBM,EAAKN,QAAQ,KAEzC,OAAO,IAAIf,EAAW,CAClB4B,aAAcP,IAGlB,IAAIa,EAAoB,6BACpBC,EAAUd,EAAKZ,MAAMyB,GACrBN,EAAeO,GAAWA,EAAQ,GAAKA,EAAQ,QAAKhB,EACpDQ,EAAgB5B,KAAKc,gBAAgBQ,EAAKH,QAAQgB,EAAmB,KAEzE,OAAO,IAAIlC,EAAW,CAClB4B,aAAcA,EACdE,SAAUH,EAAc,GACxBI,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAGpB,GAAGtB,KACP,EAEAQ,WAAY,SAAsC6B,GAC9C,OAAKA,EAAE9B,YAAe8B,EAAEC,QAAQtB,QAAQ,OAAS,GAC7CqB,EAAEC,QAAQzC,MAAM,MAAM0C,OAASF,EAAE9B,WAAWV,MAAM,MAAM0C,OACjDvC,KAAKwC,YAAYH,GAChBA,EAAE5B,MAGHT,KAAKyC,aAAaJ,GAFlBrC,KAAK0C,aAAaL,EAIjC,EAEAG,YAAa,SAAuCH,GAKhD,IAJA,IAAIM,EAAS,oCACTC,EAAQP,EAAEC,QAAQzC,MAAM,MACxBgD,EAAS,GAEJC,EAAI,EAAGC,EAAMH,EAAML,OAAQO,EAAIC,EAAKD,GAAK,EAAG,CACjD,IAAIpC,EAAQiC,EAAOzB,KAAK0B,EAAME,IAC1BpC,GACAmC,EAAOG,KAAK,IAAI/C,EAAW,CACvB8B,SAAUrB,EAAM,GAChBsB,WAAYtB,EAAM,GAClBwB,OAAQU,EAAME,KAG1B,CAEA,OAAOD,CACX,EAEAH,aAAc,SAAwCL,GAKlD,IAJA,IAAIM,EAAS,6DACTC,EAAQP,EAAE9B,WAAWV,MAAM,MAC3BgD,EAAS,GAEJC,EAAI,EAAGC,EAAMH,EAAML,OAAQO,EAAIC,EAAKD,GAAK,EAAG,CACjD,IAAIpC,EAAQiC,EAAOzB,KAAK0B,EAAME,IAC1BpC,GACAmC,EAAOG,KACH,IAAI/C,EAAW,CACX4B,aAAcnB,EAAM,SAAMU,EAC1BW,SAAUrB,EAAM,GAChBsB,WAAYtB,EAAM,GAClBwB,OAAQU,EAAME,KAI9B,CAEA,OAAOD,CACX,EAGAJ,aAAc,SAAwCnC,GAKlD,OAJeA,EAAMG,MAAMZ,MAAM,MAAMwB,QAAO,SAASC,GACnD,QAASA,EAAKZ,MAAMR,KAAiCoB,EAAKZ,MAAM,oBACpE,GAAGV,MAEauB,KAAI,SAASD,GACzB,IAMI2B,EANAvB,EAASJ,EAAKzB,MAAM,KACpB+B,EAAgB5B,KAAKc,gBAAgBY,EAAO5B,OAC5CoD,EAAgBxB,EAAOyB,SAAW,GAClCtB,EAAeqB,EACd/B,QAAQ,iCAAkC,MAC1CA,QAAQ,aAAc,UAAOC,EAE9B8B,EAAaxC,MAAM,iBACnBuC,EAAUC,EAAa/B,QAAQ,qBAAsB,OAEzD,IAAIiC,OAAoBhC,IAAZ6B,GAAqC,8BAAZA,OACjC7B,EAAY6B,EAAQpD,MAAM,KAE9B,OAAO,IAAII,EAAW,CAClB4B,aAAcA,EACduB,KAAMA,EACNrB,SAAUH,EAAc,GACxBI,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAEhB,GAAGtB,KACP,EAER,GAnM4D,8BAM5D,CAZA,E,oBCAA,yBACI,aAKI,EAAqB,QAAW,0BAAP,EAMzB,WAEJ,SAASqD,EAAUC,GACf,OAAQC,MAAMC,WAAWF,KAAOG,SAASH,EAC7C,CAEA,SAASI,EAAYC,GACjB,OAAOA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,UAAU,EACvD,CAEA,SAASC,EAAQC,GACb,OAAO,WACH,OAAOhE,KAAKgE,EAChB,CACJ,CAEA,IAAIC,EAAe,CAAC,gBAAiB,SAAU,WAAY,cACvDC,EAAe,CAAC,eAAgB,cAChCC,EAAc,CAAC,WAAY,eAAgB,UAC3CC,EAAa,CAAC,QACdC,EAAc,CAAC,cAEfC,EAAQL,EAAaM,OAAOL,EAAcC,EAAaC,EAAYC,GAEvE,SAASpE,EAAWuE,GAChB,GAAKA,EACL,IAAK,IAAI1B,EAAI,EAAGA,EAAIwB,EAAM/B,OAAQO,SACR1B,IAAlBoD,EAAIF,EAAMxB,KACV9C,KAAK,MAAQ0D,EAAYY,EAAMxB,KAAK0B,EAAIF,EAAMxB,IAG1D,CAEA7C,EAAWwE,UAAY,CACnBC,QAAS,WACL,OAAO1E,KAAKoD,IAChB,EACAuB,QAAS,SAASC,GACd,GAA0C,mBAAtCC,OAAOJ,UAAUK,SAASC,KAAKH,GAC/B,MAAM,IAAII,UAAU,yBAExBhF,KAAKoD,KAAOwB,CAChB,EAEAK,cAAe,WACX,OAAOjF,KAAKkF,UAChB,EACAC,cAAe,SAASP,GACpB,GAAIA,aAAa3E,EACbD,KAAKkF,WAAaN,MACf,MAAIA,aAAaC,QAGpB,MAAM,IAAIG,UAAU,+CAFpBhF,KAAKkF,WAAa,IAAIjF,EAAW2E,EAGrC,CACJ,EAEAE,SAAU,WACN,IAAI/C,EAAW/B,KAAKoF,eAAiB,GACjCpD,EAAahC,KAAKqF,iBAAmB,GACrCpD,EAAejC,KAAKsF,mBAAqB,GACzCzD,EAAe7B,KAAKuF,mBAAqB,GAC7C,OAAIvF,KAAKwF,YACDzD,EACO,WAAaA,EAAW,IAAMC,EAAa,IAAMC,EAAe,IAEpE,UAAYD,EAAa,IAAMC,EAEtCJ,EACOA,EAAe,KAAOE,EAAW,IAAMC,EAAa,IAAMC,EAAe,IAE7EF,EAAW,IAAMC,EAAa,IAAMC,CAC/C,GAGJhC,EAAWwF,WAAa,SAAgC9B,GACpD,IAAI+B,EAAiB/B,EAAI3C,QAAQ,KAC7B2E,EAAehC,EAAIiC,YAAY,KAE/B/D,EAAe8B,EAAIG,UAAU,EAAG4B,GAChCtC,EAAOO,EAAIG,UAAU4B,EAAiB,EAAGC,GAAc9F,MAAM,KAC7DgG,EAAiBlC,EAAIG,UAAU6B,EAAe,GAElD,GAAoC,IAAhCE,EAAe7E,QAAQ,KACvB,IAAIC,EAAQ,gCAAgCC,KAAK2E,EAAgB,IAC7D9D,EAAWd,EAAM,GACjBe,EAAaf,EAAM,GACnBgB,EAAehB,EAAM,GAG7B,OAAO,IAAIhB,EAAW,CAClB4B,aAAcA,EACduB,KAAMA,QAAQhC,EACdW,SAAUA,EACVC,WAAYA,QAAcZ,EAC1Ba,aAAcA,QAAgBb,GAEtC,EAEA,IAAK,IAAI0B,EAAI,EAAGA,EAAImB,EAAa1B,OAAQO,IACrC7C,EAAWwE,UAAU,MAAQf,EAAYO,EAAanB,KAAOiB,EAAQE,EAAanB,IAClF7C,EAAWwE,UAAU,MAAQf,EAAYO,EAAanB,KAAO,SAAUkB,GACnE,OAAO,SAASY,GACZ5E,KAAKgE,GAAK8B,QAAQlB,EACtB,CACH,CAJ4D,CAI1DX,EAAanB,IAGpB,IAAK,IAAIiD,EAAI,EAAGA,EAAI7B,EAAa3B,OAAQwD,IACrC9F,EAAWwE,UAAU,MAAQf,EAAYQ,EAAa6B,KAAOhC,EAAQG,EAAa6B,IAClF9F,EAAWwE,UAAU,MAAQf,EAAYQ,EAAa6B,KAAO,SAAU/B,GACnE,OAAO,SAASY,GACZ,IAAKvB,EAAUuB,GACX,MAAM,IAAII,UAAUhB,EAAI,qBAE5BhE,KAAKgE,GAAKgC,OAAOpB,EACrB,CACH,CAP4D,CAO1DV,EAAa6B,IAGpB,IAAK,IAAIE,EAAI,EAAGA,EAAI9B,EAAY5B,OAAQ0D,IACpChG,EAAWwE,UAAU,MAAQf,EAAYS,EAAY8B,KAAOlC,EAAQI,EAAY8B,IAChFhG,EAAWwE,UAAU,MAAQf,EAAYS,EAAY8B,KAAO,SAAUjC,GAClE,OAAO,SAASY,GACZ5E,KAAKgE,GAAKkC,OAAOtB,EACrB,CACH,CAJ2D,CAIzDT,EAAY8B,IAGnB,OAAOhG,CACX,GAxIwC,8BAMxC,CAZA,E,6BCEA,IAAIkG,EAAI,EAAQ,KACZC,EAAU,EAAQ,KAElBC,EAAiB,CACnBC,SAAU,kBACVC,KAAM,eACNC,OAAQ,KACRC,QAAS,IACTC,SAAU,SACVC,KAAM,KAsBR,SAASC,EAAIC,EAASC,EAAWC,EAAQC,EAAYC,GACnDjH,KAAK6G,QAAUA,EACf7G,KAAK8G,UAAYA,EACjB9G,KAAKkH,IAAMH,EACX/G,KAAKgH,WAAaA,EAClBhH,KAAKiH,WAAaA,EAClBjH,KAAKmH,YAAcN,EAAQM,YAC3BnH,KAAKoH,iBAAmBC,EAAcR,EAASE,EACjD,CA0EA,SAASM,EAAcR,EAASK,GAC9B,OAAOd,EAAQkB,wBAAwBT,EAASR,EAAgBa,EAClE,CArEAN,EAAInC,UAAU8C,SAAW,SAAUC,EAAMC,GACvC,IAAIL,EAAmBhB,EAAQgB,iBAC7BpH,KAAKoH,iBACL,QAEEM,EAAUtB,EAAQuB,aAAa3H,KAAKmH,YAAaK,EAAMxH,KAAKiH,YAC5DW,EAAO5H,KAGX6H,YAAW,WACTD,EAAKd,UAAUgB,KAAKF,EAAKT,YAAaC,EAAkBM,EAASD,EACnE,GAAG,EACL,EAOAb,EAAInC,UAAUsD,iBAAmB,SAAUP,EAAMC,GAC/C,IAEIO,EAFAN,EAAUtB,EAAQuB,aAAa3H,KAAKmH,YAAaK,EAAMxH,KAAKiH,YAShE,OALEe,EADEhI,KAAKgH,WACWhH,KAAKgH,WAAWiB,SAASP,GAEzBvB,EAAE+B,UAAUR,IAGZpH,OACdmH,GACFA,EAASO,EAAgB1H,OAEpB,MAGF0H,EAAgBG,KACzB,EAOAvB,EAAInC,UAAU2D,gBAAkB,SAAUC,EAAaZ,GACrD,IAAIL,EAAmBhB,EAAQgB,iBAC7BpH,KAAKoH,iBACL,QAEFpH,KAAK8G,UAAUsB,gBACbpI,KAAKmH,YACLC,EACAiB,EACAZ,EAEJ,EAEAb,EAAInC,UAAU6D,UAAY,SAAUzB,GAClC,IAAI0B,EAAavI,KAAKuI,WAMtB,OALAvI,KAAK6G,QAAUV,EAAEqC,MAAMD,EAAY1B,GACnC7G,KAAKoH,iBAAmBC,EAAcrH,KAAK6G,QAAS7G,KAAKkH,UACxB9F,IAA7BpB,KAAK6G,QAAQM,cACfnH,KAAKmH,YAAcnH,KAAK6G,QAAQM,aAE3BnH,IACT,EAMAyI,EAAOC,QAAU9B,C,6BCrHjB,IAAIT,EAAI,EAAQ,KAsGhBsC,EAAOC,QAAU,CACff,aArGF,SAAsBR,EAAaK,EAAMP,GACvC,IAAKd,EAAEwC,OAAOnB,EAAKoB,QAAS,UAAW,CACrC,IAAIC,EAAgB1C,EAAE+B,UAAUV,EAAKoB,QAAS3B,GAC1C4B,EAAcvI,MAChBkH,EAAKoB,QAAU,uCAEfpB,EAAKoB,QAAUC,EAAcV,OAAS,GAEpCX,EAAKoB,QAAQrG,OAAS,MACxBiF,EAAKoB,QAAUpB,EAAKoB,QAAQE,OAAO,EAAG,KAE1C,CACA,MAAO,CACLC,aAAc5B,EACdK,KAAMA,EAEV,EAsFEF,wBApFF,SAAiCT,EAASmC,EAAU9B,GAClD,IAAIZ,EAAW0C,EAAS1C,SACpBI,EAAWsC,EAAStC,SACpBC,EAAOqC,EAASrC,KAChBJ,EAAOyC,EAASzC,KAChBC,EAASwC,EAASxC,OAClByC,EAAUpC,EAAQoC,QAClBnC,EAuBN,SAAyBD,GACvB,IAAIqC,EACgB,oBAAVnJ,QAAyBA,QACjB,oBAAR6H,MAAuBA,KAC7Bd,EAAYD,EAAQsC,kBAAoB,WACf,IAAlBD,EAAQE,QAAuBtC,EAAY,YAChB,IAA3BoC,EAAQG,iBAAgCvC,EAAY,SAC/D,OAAOA,CACT,CA/BkBwC,CAAgBzC,GAE5B0C,EAAQ1C,EAAQ0C,MACpB,GAAI1C,EAAQ2C,SAAU,CACpB,IAAIC,EAAOvC,EAAI7G,MAAMwG,EAAQ2C,UAC7BlD,EAAWmD,EAAKnD,SAChBI,EAAW+C,EAAK/C,SAChBC,EAAO8C,EAAK9C,KACZJ,EAAOkD,EAAKC,SACZlD,EAASiD,EAAKjD,MAChB,CACA,MAAO,CACLyC,QAASA,EACT3C,SAAUA,EACVI,SAAUA,EACVC,KAAMA,EACNJ,KAAMA,EACNC,OAAQA,EACR+C,MAAOA,EACPzC,UAAWA,EAEf,EAyDEM,iBA7CF,SAA0BN,EAAWtH,GACnC,IAAIkH,EAAWI,EAAUJ,UAAY,SACjCC,EACFG,EAAUH,OACI,UAAbD,EAAuB,GAAkB,WAAbA,EAAwB,SAAMtF,GACzDkF,EAAWQ,EAAUR,SACrBC,EAAOO,EAAUP,KACjB0C,EAAUnC,EAAUmC,QACpBU,EAAe7C,EAAUA,UAU7B,OATIA,EAAUN,SACZD,GAAcO,EAAUN,QAEtBM,EAAUyC,QACZhD,EAAOG,EAAW,KAAOJ,EAAWC,EACpCD,EAAWQ,EAAUyC,MAAMK,MAAQ9C,EAAUyC,MAAMjD,SACnDK,EAAOG,EAAUyC,MAAM5C,KACvBD,EAAWI,EAAUyC,MAAM7C,UAAYA,GAElC,CACLuC,QAASA,EACTvC,SAAUA,EACVJ,SAAUA,EACVC,KAAMA,EACNI,KAAMA,EACNnH,OAAQA,EACRsH,UAAW6C,EAEf,EAmBEE,iBAjBF,SAA0BC,EAAMvD,GAC9B,IAAIwD,EAAoB,MAAMC,KAAKF,GAC/BG,EAAqB,MAAMD,KAAKzD,GAQpC,OANIwD,GAAqBE,EACvB1D,EAAOA,EAAKzC,UAAU,GACZiG,GAAsBE,IAChC1D,EAAO,IAAMA,GAGRuD,EAAOvD,CAChB,E,6BCpGA,IAAI2D,EAAU,EAAQ,KAElBrD,EAA6B,oBAAX9G,QAA2BA,OAAOoK,eACpDC,EAAQvD,GAAWA,EAAQwD,aAAe,UAC1CC,EAAiC,oBAAXvK,QAA2BA,OAAOqK,IAA0C,mBAAzBrK,OAAOqK,GAAOG,aAAoDnJ,IAA3BrB,OAAOqK,GAAOG,SAMlI,GAJuB,oBAAXxK,QAA4BA,OAAOyK,oBAC7CzK,OAAOyK,mBAAoB,IAAKC,MAAQC,YAGrCJ,GAAezD,EAAS,CAC3B,IAAI8D,EAAU,IAAIT,EAAQrD,GAC1B9G,OAAOqK,GAASO,CAClB,KAA6B,oBAAX5K,QAChBA,OAAOmK,QAAUA,EACjBnK,OAAO6K,iBAAkB,GACA,oBAAThD,OAChBA,KAAKsC,QAAUA,EACftC,KAAKgD,iBAAkB,GAGzBnC,EAAOC,QAAUwB,C,6BCrBjB,IAAIW,EAAS,EAAQ,KACjB1E,EAAI,EAAQ,KACZ2E,EAAM,EAAQ,KACdC,EAAS,EAAQ,KACjBC,EAAU,EAAQ,KAElBC,EAAY,EAAQ,KACpBlE,EAAS,EAAQ,KAEjBmE,EAAa,EAAQ,KACrBC,EAAmB,EAAQ,KAC3BC,EAAa,EAAQ,KACrBC,EAAmB,EAAQ,IAC3BC,EAAc,EAAQ,KAE1B,SAASX,EAAQ9D,EAAS0E,GACxBvL,KAAK6G,QAAUV,EAAEqF,cAAcnF,EAAgBQ,EAAS,KAAMkE,GAC9D/K,KAAK6G,QAAQ4E,mBAAqB5E,EAClC,IAAI6E,EAAY1L,KAAK2L,WAAWC,UAC5BC,EAAe7L,KAAK2L,WAAWG,aAC/BC,EAAe/L,KAAK2L,WAAWI,aACnC/L,KAAKgM,YAAchM,KAAK2L,WAAWK,YACnChM,KAAKiM,MAAQjM,KAAK2L,WAAWM,MAC7B,IAAIjF,EAAahH,KAAK2L,WAAW3E,WAE7BF,EAAY,IAAImE,EAAUjE,GAC1BkF,EAAM,IAAIpB,EAAI9K,KAAK6G,QAASC,EAAWC,EAAQC,GAC/C0E,IACF1L,KAAK4L,UAAY,IAAIF,EAAU1L,KAAK6G,UAEtC7G,KAAKuL,OACHA,GAAU,IAAIV,EAAO7K,KAAK6G,QAASqF,EAAKnB,EAAQ/K,KAAK4L,UAAW,WAClE,IAAI1C,EAAUiD,IACVC,EAA+B,oBAAZC,UAA2BA,SAClDrM,KAAKsM,SAAWpD,EAAQqD,QAAUrD,EAAQqD,OAAOC,QACjDxM,KAAKyM,uBAAyB,EA2ehC,SAAiCC,EAAUxC,EAAShB,GAClDwD,EACGC,aAAazB,EAAW0B,oBACxBD,aAAazB,EAAW2B,qBACxBF,aAAazB,EAAW4B,6BACxBH,aAAazB,EAAW6B,aACxBJ,aAAazB,EAAW8B,eAAe9D,IACvCyD,aAAazB,EAAW+B,cAAc/D,IACtCyD,aAAazB,EAAWgC,cAAchE,IACtCyD,aAAazB,EAAWiC,SACxBR,aAAaxB,EAAiBiC,qBAC9BT,aAAaxB,EAAiBkC,kBAC9BV,aAAaxB,EAAiBmC,oBAC9BX,aAAazB,EAAWqC,YAAYrD,EAAQ+B,QAC5CU,aAAaxB,EAAiBqC,mBAC9Bb,aAAaxB,EAAiBsC,cAAc1C,IAC5C4B,aAAaxB,EAAiBuC,sBAC9Bf,aAAaxB,EAAiBwC,mBAC9BhB,aAAaxB,EAAiByC,cACnC,CA7fEC,CAAwB7N,KAAKuL,OAAOmB,SAAU1M,KAAMkJ,GAC/BlJ,KAAKuL,OAAOuC,MAggB9BC,aAAa1C,EAAiB2C,YAC9BD,aAAa3C,EAAW6C,aACxBF,aAAa1C,EAAiB6C,gBAAgBnD,IAC9CgD,aAAa1C,EAAiB8C,oBAAoBpD,IAClDgD,aAAa1C,EAAiB+C,gBAAgBrD,IAC9CgD,aAAa1C,EAAiBgD,iBAAiBtD,IApgBlD/K,KAAKsO,wBACDzC,IACF7L,KAAK8L,aAAe,IAAID,EACtB7L,KAAK6G,QACL7G,KAAKuL,OAAOK,UACZ5L,KACAkJ,EACAkD,GAEFpM,KAAK8L,aAAayC,cAEpBpI,EAAEqI,UAAUzC,GAGZ/L,KAAKkK,QAAUlK,IACjB,CAEA,IAAIyO,EAAY,KAehB,SAASC,EAAoBC,GAC3B,IAAIrM,EAAU,6BACdyI,EAAOzK,MAAMgC,GACTqM,GACFA,EAAc,IAAI9N,MAAMyB,GAE5B,CA2eA,SAASsM,EAAkBxL,GACzB,IAAK,IAAIN,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,IAAOD,EAC5C,GAAIqD,EAAE0I,WAAWzL,EAAKN,IACpB,OAAOM,EAAKN,EAIlB,CAEA,SAASqJ,IACP,MACoB,oBAAVpM,QAAyBA,QACjB,oBAAR6H,MAAuBA,IAEnC,CA7gBA+C,EAAQmE,KAAO,SAAUjI,EAAS0E,GAChC,OAAIkD,EACKA,EAAUpP,OAAOwH,GAASyB,UAAUzB,GAE7C4H,EAAY,IAAI9D,EAAQ9D,EAAS0E,EAEnC,EAEAZ,EAAQlG,UAAUkH,WAAa,CAAC,EAEhChB,EAAQoE,cAAgB,SAAUpD,GAChChB,EAAQlG,UAAUkH,WAAaA,CACjC,EAUAhB,EAAQlG,UAAUpF,OAAS,SAAUwH,GAEnC,OADA7G,KAAKuL,OAAOlM,OAAOwH,GACZ7G,IACT,EACA2K,EAAQtL,OAAS,SAAUwH,GACzB,GAAI4H,EACF,OAAOA,EAAUpP,OAAOwH,GAExB6H,GAEJ,EAEA/D,EAAQlG,UAAU6D,UAAY,SAAUzB,EAASmI,GAC/C,IAAIzG,EAAavI,KAAK6G,QAClBa,EAAU,CAAC,EAaf,OAZIsH,IACFtH,EAAU,CAAEA,QAASsH,IAEvBhP,KAAK6G,QAAUV,EAAEqF,cAAcjD,EAAY1B,EAASa,EAASqD,GAC7D/K,KAAK6G,QAAQ4E,mBAAqBtF,EAAEqF,cAClCjD,EAAWkD,mBACX5E,EACAa,GAEF1H,KAAKuL,OAAOjD,UAAUtI,KAAK6G,QAASmI,GACpChP,KAAK8L,cAAgB9L,KAAK8L,aAAaxD,UAAUtI,KAAK6G,SACtD7G,KAAKsO,wBACEtO,IACT,EACA2K,EAAQrC,UAAY,SAAUzB,EAASmI,GACrC,GAAIP,EACF,OAAOA,EAAUnG,UAAUzB,EAASmI,GAEpCN,GAEJ,EAEA/D,EAAQlG,UAAUwK,UAAY,WAC5B,OAAOjP,KAAKuL,OAAO0D,SACrB,EACAtE,EAAQsE,UAAY,WAClB,GAAIR,EACF,OAAOA,EAAUQ,YAEjBP,GAEJ,EAEA/D,EAAQlG,UAAUyK,IAAM,WACtB,IAAIC,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAO2D,IAAIC,GACT,CAAEG,KAAMA,EACjB,EACA3E,EAAQuE,IAAM,WACZ,GAAIT,EACF,OAAOA,EAAUS,IAAIK,MAAMd,EAAWY,WAGtCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAU+K,MAAQ,WACxB,IAAIL,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOiE,MAAML,GACX,CAAEG,KAAMA,EACjB,EACA3E,EAAQ6E,MAAQ,WACd,GAAIf,EACF,OAAOA,EAAUe,MAAMD,MAAMd,EAAWY,WAGxCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUgL,KAAO,WACvB,IAAIN,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOkE,KAAKN,GACV,CAAEG,KAAMA,EACjB,EACA3E,EAAQ8E,KAAO,WACb,GAAIhB,EACF,OAAOA,EAAUgB,KAAKF,MAAMd,EAAWY,WAGvCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUiL,KAAO,WACvB,IAAIP,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOmE,KAAKP,GACV,CAAEG,KAAMA,EACjB,EACA3E,EAAQ+E,KAAO,WACb,GAAIjB,EACF,OAAOA,EAAUiB,KAAKH,MAAMd,EAAWY,WAGvCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUkL,QAAU,WAC1B,IAAIR,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOoE,QAAQR,GACb,CAAEG,KAAMA,EACjB,EACA3E,EAAQgF,QAAU,WAChB,GAAIlB,EACF,OAAOA,EAAUkB,QAAQJ,MAAMd,EAAWY,WAG1CX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUnE,MAAQ,WACxB,IAAI6O,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOjL,MAAM6O,GACX,CAAEG,KAAMA,EACjB,EACA3E,EAAQrK,MAAQ,WACd,GAAImO,EACF,OAAOA,EAAUnO,MAAMiP,MAAMd,EAAWY,WAGxCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUmL,SAAW,WAC3B,IAAIT,EAAOnP,KAAKoP,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADAtP,KAAKuL,OAAOqE,SAAST,GACd,CAAEG,KAAMA,EACjB,EACA3E,EAAQiF,SAAW,WACjB,GAAInB,EACF,OAAOA,EAAUmB,SAASL,MAAMd,EAAWY,WAG3CX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQlG,UAAUsD,iBAAmB,SAAUoH,GAC7C,OAAOnP,KAAKuL,OAAOxD,iBAAiBoH,EACtC,EACAxE,EAAQ5C,iBAAmB,WACzB,GAAI0G,EACF,OAAOA,EAAU1G,iBAAiBwH,MAAMd,EAAWY,WAEnDX,GAEJ,EAEA/D,EAAQlG,UAAUoL,gBAAkB,SAAUxH,GAC5C,OAAOrI,KAAKuL,OAAOsE,gBAAgBxH,EACrC,EACAsC,EAAQkF,gBAAkB,WACxB,GAAIpB,EACF,OAAOA,EAAUoB,gBAAgBN,MAAMd,EAAWY,WAElDX,GAEJ,EAEA/D,EAAQlG,UAAU6J,sBAAwB,WACxC,IAAIpF,EAAUiD,IAETnM,KAAK8P,iCACJ9P,KAAK6G,QAAQkJ,iBAAmB/P,KAAK6G,QAAQmJ,4BAC/ChF,EAAQiF,0BAA0B/G,EAASlJ,MACvCA,KAAKgM,aAAehM,KAAK6G,QAAQqJ,yBACnClQ,KAAKgM,YAAY9C,EAASlJ,MAE5BA,KAAK8P,gCAAiC,GAGrC9P,KAAKmQ,iCAENnQ,KAAK6G,QAAQuJ,4BACbpQ,KAAK6G,QAAQwJ,6BAEbrF,EAAQoF,2BAA2BlH,EAASlJ,MAC5CA,KAAKmQ,gCAAiC,EAG5C,EAEAxF,EAAQlG,UAAU6L,wBAA0B,SAC1ChO,EACA4E,EACAqJ,EACAC,EACAlQ,EACAsI,GAEA,GAAK5I,KAAK6G,QAAQkJ,iBAAoB/P,KAAK6G,QAAQmJ,yBAAnD,CAQA,GACEhQ,KAAK6G,QAAQ4J,wBACbzQ,KAAKsM,UACK,OAAVhM,GACQ,KAAR4G,EAEA,MAAO,YAGT,IAAIiI,EACAuB,EAAYvK,EAAEwK,uBAChBrO,EACA4E,EACAqJ,EACAC,EACAlQ,EACA,UACA,qBACAgL,GAEEnF,EAAEyK,QAAQtQ,IACZ6O,EAAOnP,KAAKoP,YAAY,CAAC9M,EAAShC,EAAOsI,KACpCiI,oBAAsBH,EAClBvK,EAAEyK,QAAQ1J,IACnBiI,EAAOnP,KAAKoP,YAAY,CAAC9M,EAAS4E,EAAK0B,KAClCiI,oBAAsBH,GAE3BvB,EAAOnP,KAAKoP,YAAY,CAAC9M,EAASsG,KAC7B8H,UAAYA,EAEnBvB,EAAK2B,MAAQ9Q,KAAK6G,QAAQkK,mBAC1B5B,EAAK6B,aAAc,EACnBhR,KAAKuL,OAAO2D,IAAIC,EAtChB,CAuCF,EAcAxE,EAAQlG,UAAUwM,sBAAwB,WACxC,GAAKjR,KAAK6G,QAAQ4J,wBAA2BzQ,KAAKsM,SAAlD,CAIA,IAAI4E,EAAIlR,KAkCR,IACEa,MAAMsQ,kBAlCR,SAA2B7Q,EAAO8Q,GAEhC,GAAIF,EAAErK,QAAQ4J,wBACRS,EAAEzE,uBAAwB,CAQ5B,GAFAyE,EAAEzE,wBAA0B,GAEvBnM,EAIH,OAIFA,EAAM+Q,cAAe,EAKrBH,EAAEZ,wBAAwBhQ,EAAMgC,QAAS,KAAM,KAAM,KAAMhC,EAC7D,CAIF,OAAOA,EAAMG,KACf,CAKA,CAAE,MAAO4B,GACPrC,KAAK6G,QAAQ4J,wBAAyB,EACtCzQ,KAAKM,MAAM,iCAAkC+B,EAC/C,CAzCA,CA0CF,EAEAsI,EAAQlG,UAAU6M,yBAA2B,SAAUC,EAAQC,GAC7D,GACGxR,KAAK6G,QAAQuJ,4BACbpQ,KAAK6G,QAAQwJ,0BAFhB,CAOA,IAAI/N,EAAU,6CACd,GAAIiP,EACF,GAAIA,EAAOjP,QACTA,EAAUiP,EAAOjP,YACZ,CACL,IAAImP,EAAetL,EAAE+B,UAAUqJ,GAC3BE,EAAatJ,QACf7F,EAAUmP,EAAatJ,MAE3B,CAEF,IAGIgH,EAHAvG,EACD2I,GAAUA,EAAOG,iBAAqBF,GAAWA,EAAQE,gBAGxDvL,EAAEyK,QAAQW,GACZpC,EAAOnP,KAAKoP,YAAY,CAAC9M,EAASiP,EAAQ3I,KAE1CuG,EAAOnP,KAAKoP,YAAY,CAAC9M,EAASiP,EAAQ3I,KACrC8H,UAAYvK,EAAEwK,uBACjBrO,EACA,GACA,EACA,EACA,KACA,qBACA,GACAgJ,GAGJ6D,EAAK2B,MAAQ9Q,KAAK6G,QAAQkK,mBAC1B5B,EAAK6B,aAAc,EACnB7B,EAAKwC,cAAgBxC,EAAKwC,eAAiB,GAC3CxC,EAAKwC,cAAc3O,KAAKwO,GACxBxR,KAAKuL,OAAO2D,IAAIC,EApChB,CAqCF,EAEAxE,EAAQlG,UAAUmN,KAAO,SAAUC,EAAGjJ,EAASkJ,GAC7C,IACE,IAAIC,EASJ,GAPEA,EADE5L,EAAE0I,WAAWjG,GACPA,EAEA,WACN,OAAOA,GAAW,CAAC,CACrB,GAGGzC,EAAE0I,WAAWgD,GAChB,OAAOA,EAGT,GAAIA,EAAEG,QACJ,OAAOH,EAGT,IAAKA,EAAEI,mBACLJ,EAAEI,iBAAmB,WACfH,GAAW3L,EAAE0I,WAAWiD,IAC1BA,EAAQvC,MAAMvP,KAAMqP,WAEtB,IACE,OAAOwC,EAAEtC,MAAMvP,KAAMqP,UACvB,CAAE,MAAO6C,GACP,IAAI7P,EAAI6P,EAUR,MATI7P,GAAKtC,OAAOoS,uBAAyB9P,IACnC8D,EAAEwC,OAAOtG,EAAG,YACdA,EAAI,IAAI6D,OAAO7D,IAEjBA,EAAEqP,gBAAkBK,KAAW,CAAC,EAChC1P,EAAEqP,gBAAgBU,eAAiBP,EAAE/M,WAErC/E,OAAOoS,qBAAuB9P,GAE1BA,CACR,CACF,EAEAwP,EAAEI,iBAAiBD,SAAU,EAEzBH,EAAEQ,gBACJ,IAAK,IAAI9S,KAAQsS,EACXA,EAAEQ,eAAe9S,IAAkB,qBAATA,IAC5BsS,EAAEI,iBAAiB1S,GAAQsS,EAAEtS,IAMrC,OAAOsS,EAAEI,gBACX,CAAE,MAAO5P,GAEP,OAAOwP,CACT,CACF,EACAlH,EAAQiH,KAAO,SAAUC,EAAGjJ,GAC1B,GAAI6F,EACF,OAAOA,EAAUmD,KAAKC,EAAGjJ,GAEzB8F,GAEJ,EAEA/D,EAAQlG,UAAU6N,aAAe,WAC/B,IAAIC,EAAQpM,EAAEqM,qBAAqBnD,WACnC,OAAOrP,KAAKuL,OAAO+G,aAAaC,EAAME,KAAMF,EAAMG,SAAUH,EAAMzB,MACpE,EACAnG,EAAQ2H,aAAe,WACrB,GAAI7D,EACF,OAAOA,EAAU6D,aAAa/C,MAAMd,EAAWY,WAE/CX,GAEJ,EAGA/D,EAAQlG,UAAUkO,wBAA0B,SAAUtQ,EAAGuQ,GAIvD,OAHKA,IACHA,EAAK,IAAInI,MAEJzK,KAAKuL,OAAOoH,wBAAwBC,EAC7C,EAEAjI,EAAQlG,UAAUoO,YAAc,SAAUxQ,EAAGuQ,GAI3C,OAHKA,IACHA,EAAK,IAAInI,MAEJzK,KAAKuL,OAAOsH,YAAYD,EACjC,EAmCAjI,EAAQlG,UAAUqO,SAAW,WAC3B/H,EAAO0E,KACL,sHAEJ,EAEA9E,EAAQlG,UAAU2K,YAAc,SAAUhM,GACxC,OAAO+C,EAAE4M,WAAW3P,EAAM2H,EAAQ/K,KACpC,EAkBA,IAAIgJ,EAAW,EAAQ,KACnBgK,EAAc,EAAQ,KAEtB3M,EAAiB,CACnBI,QAASuC,EAASvC,QAClBuM,YAAaA,EAAYA,YACzBC,SAAUjK,EAASiK,SACnBC,YAAalK,EAASkK,YACtBnC,mBAAoB/H,EAAS+H,mBAC7BvH,SAAUR,EAASQ,SACnB2J,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,yBAAyB,EACzBC,WAAW,EACX/C,wBAAwB,EACxBgD,uBAAuB,EACvBvD,yBAAyB,GAG3BzH,EAAOC,QAAUiC,C,uBC5lBjBlC,EAAOC,QAAU,CACfsK,YAAa,CACX,KACA,OACA,SACA,WACA,SACA,mBACA,kBACA,wBACA,uBACA,eACA,cACA,yBACA,aACA,YACA,cACA,YACA,cACA,aACA,UACA,QACA,WACA,SACA,mBACA,qBACA,sBACA,kBACA,eACA,iBACA,QACA,SACA,SACA,MACA,OACA,OACA,OACA,gBACA,oBACA,sBACA,eACA,aACA,aACA,cACA,0BACA,SACA,YACA,WACA,UACA,SACA,eACA,kBACA,iBACA,UACA,SACA,UACA,U,uBChCJ,IAAIU,EAAY,CACdC,UAlBF,WACE,IAAIC,EACJ,GAAwB,oBAAbvH,SACT,OAAOuH,EAOT,IAJA,IAAIhP,EAAI,EACNiP,EAAMxH,SAASyH,cAAc,OAC7BC,EAAMF,EAAIG,qBAAqB,KAG7BH,EAAII,UAAY,uBAAqBrP,EAAI,2BAA0BmP,EAAI,KAG3E,OAAOnP,EAAI,EAAIA,EAAIgP,CACrB,GAMAnL,EAAOC,QAAUgL,C,uBC5BjB,SAASQ,EAAe7R,GACtB,OAAQA,EAAE8R,aAAa,SAAW,IAAIC,aACxC,CAiEA,SAASC,EAAoBC,GAC3B,IAAKA,IAASA,EAAKC,QACjB,MAAO,GAET,IAAIC,EAAM,CAACF,EAAKC,SACZD,EAAKG,IACPD,EAAIxR,KAAK,IAAMsR,EAAKG,IAElBH,EAAKI,SACPF,EAAIxR,KAAK,IAAMsR,EAAKI,QAAQ5S,KAAK,MAEnC,IAAK,IAAIgB,EAAI,EAAGA,EAAIwR,EAAKK,WAAWpS,OAAQO,IAC1C0R,EAAIxR,KACF,IAAMsR,EAAKK,WAAW7R,GAAG8R,IAAM,KAAON,EAAKK,WAAW7R,GAAGqF,MAAQ,MAIrE,OAAOqM,EAAI1S,KAAK,GAClB,CAiBA,SAAS+S,EAAgBC,GACvB,IAAKA,IAASA,EAAKP,QACjB,OAAO,KAET,IACEQ,EACAH,EACAI,EACAlS,EAJE0R,EAAM,CAAC,EAKXA,EAAID,QAAUO,EAAKP,QAAQH,cACvBU,EAAKL,KACPD,EAAIC,GAAKK,EAAKL,KAEhBM,EAAYD,EAAKC,YACqB,iBAAdA,IACtBP,EAAIE,QAAUK,EAAUlV,MAAM,QAEhC,IAAI8U,EAAa,CAAC,OAAQ,OAAQ,QAAS,OAE3C,IADAH,EAAIG,WAAa,GACZ7R,EAAI,EAAGA,EAAI6R,EAAWpS,OAAQO,IACjC8R,EAAMD,EAAW7R,IACjBkS,EAAOF,EAAKX,aAAaS,KAEvBJ,EAAIG,WAAW3R,KAAK,CAAE4R,IAAKA,EAAKzM,MAAO6M,IAG3C,OAAOR,CACT,CAEA/L,EAAOC,QAAU,CACfmM,gBAAiBA,EACjBR,oBAAqBA,EACrBY,qBAzFF,SAA8BC,GAS5B,IARA,IAKEC,EACAC,EAHEZ,EAAM,GACRzR,EAAM,EAICD,EAAIoS,EAAE3S,OAAS,EAAGO,GAAK,EAAGA,IAAK,CAGtC,GAFAqS,EAAUd,EAAoBa,EAAEpS,IAChCsS,EAAcrS,EARIsS,EAQEb,EAAIjS,OAA2B4S,EAAQ5S,OACvDO,EAAIoS,EAAE3S,OAAS,GAAK6S,GAAeE,GAAgB,CACrDd,EAAIe,QAAQ,OACZ,KACF,CACAf,EAAIe,QAAQJ,GACZpS,GAAOoS,EAAQ5S,MACjB,CACA,OAAOiS,EAAI1S,KAjBK,MAkBlB,EAsEE0T,YAzGF,SAAqBV,GAInB,IAHA,IAEIW,EADAjB,EAAM,GAEDkB,EAAS,EAAGZ,GAAQY,EAHZ,GAKiB,UADhCD,EAAkBZ,EAAgBC,IACdP,QAF4BmB,IAKhDlB,EAAIe,QAAQE,GACZX,EAAOA,EAAKa,WAEd,OAAOnB,CACT,EA6FEoB,oBApHF,SAA6BC,EAAKC,GAChC,OAAID,EAAIE,OACCF,EAAIE,OAETD,GAAOA,EAAIE,iBACNF,EAAIE,iBAAiBH,EAAII,QAASJ,EAAIK,cAD/C,CAIF,EA6GEC,mBArIF,SAA4BC,EAAS3D,EAAM4D,GACzC,GAAID,EAAQ7B,QAAQH,gBAAkB3B,EAAK2B,cACzC,OAAO,EAET,IAAKiC,EACH,OAAO,EAETD,EAAUlC,EAAekC,GACzB,IAAK,IAAItT,EAAI,EAAGA,EAAIuT,EAAS9T,OAAQO,IACnC,GAAIuT,EAASvT,KAAOsT,EAClB,OAAO,EAGX,OAAO,CACT,EAwHElC,eAAgBA,E,uBCvClBzL,EAAOC,QAAU,CACfuH,0BApGF,SAAmClQ,EAAQuW,EAASC,GAClD,GAAKxW,EAAL,CAGA,IAAIyW,EAEJ,GAA0C,mBAA/BF,EAAQG,mBACjBD,EAAaF,EAAQG,wBAChB,GAAI1W,EAAO2W,QAAS,CAEzB,IADAF,EAAazW,EAAO2W,QACbF,EAAWC,oBAChBD,EAAaA,EAAWC,mBAE1BH,EAAQG,mBAAqBD,CAC/B,CAEAF,EAAQrF,wBAER,IAAI0F,EAAK,WACP,IAAIvT,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,UAAW,IASrD,SAA+BtP,EAAQmR,EAAG2F,EAAKzT,GACzCrD,EAAOoS,uBACJ/O,EAAK,KACRA,EAAK,GAAKrD,EAAOoS,sBAEd/O,EAAK,KACRA,EAAK,GAAKrD,EAAOoS,qBAAqBT,iBAExC3R,EAAOoS,qBAAuB,MAGhC,IAAI2E,EAAM5F,EAAEZ,wBAAwBf,MAAM2B,EAAG9N,GAEzCyT,GACFA,EAAItH,MAAMxP,EAAQqD,GAMR,cAAR0T,IACF5F,EAAEzE,wBAA0B,EAEhC,CA/BIsK,CAAsBhX,EAAQuW,EAASE,EAAYpT,EACrD,EACImT,IACFI,EAAGF,mBAAqBD,GAE1BzW,EAAO2W,QAAUC,CAtBjB,CAuBF,EA2EEvG,2BAhDF,SAAoCrQ,EAAQuW,EAASC,GACnD,GAAKxW,EAAL,CAKgC,mBAAvBA,EAAOiX,aACdjX,EAAOiX,YAAYC,eAEnBlX,EAAOmX,oBAAoB,qBAAsBnX,EAAOiX,aAG1D,IAAIG,EAAmB,SAAUtB,GAC/B,IAAItE,EAAQC,EAAS4F,EACrB,IACE7F,EAASsE,EAAItE,MACf,CAAE,MAAOlP,GACPkP,OAASnQ,CACX,CACA,IACEoQ,EAAUqE,EAAIrE,OAChB,CAAE,MAAOnP,GACPmP,EAAU,yDACZ,CACA,IACE4F,EAASvB,EAAIuB,QACR7F,GAAU6F,IACb7F,EAAS6F,EAAO7F,OAChBC,EAAU4F,EAAO5F,QAErB,CAAE,MAAOnP,GAET,CACKkP,IACHA,EAAS,0DAGP+E,GAAWA,EAAQhF,0BACrBgF,EAAQhF,yBAAyBC,EAAQC,EAE7C,EACA2F,EAAiBF,cAAgBV,EACjCxW,EAAOiX,YAAcG,EACrBpX,EAAOsX,iBAAiB,qBAAsBF,EAxC9C,CAyCF,E,6BChGA,EAAQ,KACR,IAAIG,EAAY,EAAQ,KACpBnR,EAAI,EAAQ,KAkChBsC,EAAOC,QAAU,CACfpI,MAjCF,WACE,IAAI8C,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,UAAW,GACjDjM,EAAKmS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BrU,QAAQgB,MAAM6F,EAAEoR,mBAAmBnU,IAEnC9D,QAAQgB,MAAMiP,MAAMjQ,QAAS8D,EAEjC,EA0BEqM,KAxBF,WACE,IAAIrM,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,UAAW,GACjDjM,EAAKmS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BrU,QAAQmQ,KAAKtJ,EAAEoR,mBAAmBnU,IAElC9D,QAAQmQ,KAAKF,MAAMjQ,QAAS8D,EAEhC,EAiBE8L,IAfF,WACE,IAAI9L,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,UAAW,GACjDjM,EAAKmS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BrU,QAAQ4P,IAAI/I,EAAEoR,mBAAmBnU,IAEjC9D,QAAQ4P,IAAIK,MAAMjQ,QAAS8D,EAE/B,E,6BCjCA,IAAI+C,EAAI,EAAQ,KAShBsC,EAAOC,QAAU,CACfuF,YARF,SAAqBkB,EAAMqI,GACzB,OAAIrR,EAAEsR,IAAID,EAAU,qCACVrR,EAAEsR,IAAItI,EAAM,4BAGxB,E,6BCPA,IAAIxE,EAAU,EAAQ,KAClBiB,EAAY,EAAQ,KACpBE,EAAe,EAAQ,KACvBC,EAAe,EAAQ,KACvBC,EAAc,EAAQ,KACtBC,EAAQ,EAAQ,KAChBjF,EAAa,EAAQ,KAEzB2D,EAAQoE,cAAc,CACpBnD,UAAWA,EACXE,aAAcA,EACdC,aAAcA,EACdC,YAAaA,EACbC,MAAOA,EACPjF,WAAYA,IAGdyB,EAAOC,QAAUiC,C,6BCjBjB,IAAIxE,EAAI,EAAQ,KACZuR,EAAU,EAAQ,KAClBvW,EAAU,EAAQ,KAClB8K,EAAQ,EAAQ,KAChB0L,EAAY,EAAQ,KACpBC,EAAU,EAAQ,KAElB5O,EAAW,CACb6O,SAAS,EACTC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBC,oBAAoB,EACpBC,uBAAuB,EACvBC,uBAAuB,EACvBC,qBAAqB,EACrBlJ,KAAK,EACLmJ,KAAK,EACLC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,8BAA8B,GAGhC,SAASC,EAAQC,EAAclG,GAE7B,IADA,IAAImG,EACGD,EAAalG,GAAMlQ,SACxBqW,EAAID,EAAalG,GAAMtP,SACrB,GAAGyV,EAAE,IAAMA,EAAE,EAEnB,CAkCA,SAAS/M,EAAahF,EAAS+E,EAAW1B,EAAS2O,EAASC,GAC1D9Y,KAAK6G,QAAUA,EACf,IAAIkS,EAAiBlS,EAAQkS,gBACL,IAApBlS,EAAQuM,UAAwC,IAAnB2F,EAC/B/Y,KAAK+Y,eAAiB,CAAC,GAElB5S,EAAEwC,OAAOoQ,EAAgB,YAC5BA,EAAiB/P,GAEnBhJ,KAAK+Y,eAAiB5S,EAAEqC,MAAMQ,EAAU+P,IAE1C/Y,KAAKgZ,uBAAyBnS,EAAQmS,qBACtChZ,KAAKiZ,kBAAoBpS,EAAQoS,kBACjCjZ,KAAKkZ,qBAhCP,SAA8BlG,GAE5B,IADA,IAAImG,EAAW,GACNrW,EAAI,EAAGA,EAAIkQ,EAAYzQ,SAAUO,EACxCqW,EAASnW,KAAK,IAAIoW,OAAOpG,EAAYlQ,GAAI,MAE3C,OAAO,SAAUuW,GACf,IAAIC,EAnBR,SAA6BD,GAC3B,IAAKA,IAAgBA,EAAY1E,WAC/B,OAAO,KAGT,IADA,IAAI4E,EAAQF,EAAY1E,WACfO,EAAI,EAAGA,EAAIqE,EAAMhX,SAAU2S,EAClC,GAAqB,SAAjBqE,EAAMrE,GAAGN,IACX,OAAO2E,EAAMrE,GAAG/M,MAGpB,OAAO,IACT,CAQeqR,CAAoBH,GAC/B,IAAKC,EACH,OAAO,EAET,IAAK,IAAIxW,EAAI,EAAGA,EAAIqW,EAAS5W,SAAUO,EACrC,GAAIqW,EAASrW,GAAGkH,KAAKsP,GACnB,OAAO,EAGX,OAAO,CACT,CACF,CAe8BJ,CAAqBrS,EAAQmM,aACzDhT,KAAK4L,UAAYA,EACjB5L,KAAKkK,QAAUA,EACflK,KAAKyZ,WAAavP,EAAQqB,OAAOmB,SAAS+M,WAC1CzZ,KAAK6Y,QAAUA,GAAW,CAAC,EAC3B7Y,KAAK8Y,UAAYA,GAAa,CAAC,EAC/B9Y,KAAK2Y,aAAe,CAClBd,QAAS,GACT3I,IAAK,GACLoJ,WAAY,GACZC,aAAc,IAEhBvY,KAAK0Z,cAAgB,CACnBrB,IAAK,GACLE,aAAc,GACdoB,sBAAuB,IAGzB3Z,KAAK4Z,UAAY5Z,KAAK6Y,QAAQpX,SAC9BzB,KAAK6Z,UAAY7Z,KAAK4Z,WAAa5Z,KAAK4Z,UAAUE,IACpD,CA03BA,SAASC,EAAaC,GACpB,MAAsB,oBAARC,KAAuBD,aAAiBC,GACxD,CA13BApO,EAAapH,UAAU6D,UAAY,SAAUzB,GAC3C7G,KAAK6G,QAAUV,EAAEqC,MAAMxI,KAAK6G,QAASA,GACrC,IAAIkS,EAAiBlS,EAAQkS,eACzBmB,EAAc/T,EAAEqC,MAAMxI,KAAK+Y,iBACP,IAApBlS,EAAQuM,UAAwC,IAAnB2F,EAC/B/Y,KAAK+Y,eAAiB,CAAC,GAElB5S,EAAEwC,OAAOoQ,EAAgB,YAC5BA,EAAiB/P,GAEnBhJ,KAAK+Y,eAAiB5S,EAAEqC,MAAMQ,EAAU+P,IAE1C/Y,KAAKuO,WAAW2L,QACqB9Y,IAAjCyF,EAAQmS,uBACVhZ,KAAKgZ,uBAAyBnS,EAAQmS,2BAEN5X,IAA9ByF,EAAQoS,oBACVjZ,KAAKiZ,kBAAoBpS,EAAQoS,kBAErC,EAGApN,EAAapH,UAAU8J,WAAa,SAAU2L,IACxCla,KAAK+Y,eAAelB,SAAaqC,GAAeA,EAAYrC,SAG7D7X,KAAK+Y,eAAelB,SACrBqC,GACAA,EAAYrC,SAEZ7X,KAAKma,sBANLna,KAAKoa,qBASHpa,KAAK+Y,eAAe7J,KAASgL,GAAeA,EAAYhL,KAEhDlP,KAAK+Y,eAAe7J,KAAOgL,GAAeA,EAAYhL,KAChElP,KAAKqa,sBAFLra,KAAKsa,qBAKHta,KAAK+Y,eAAeV,KAAS6B,GAAeA,EAAY7B,KAEhDrY,KAAK+Y,eAAeV,KAAO6B,GAAeA,EAAY7B,KAChErY,KAAKua,kBAFLva,KAAKwa,iBAMLxa,KAAK+Y,eAAeT,YAClB4B,GAAeA,EAAY5B,YAI5BtY,KAAK+Y,eAAeT,YACrB4B,GACAA,EAAY5B,YAEZtY,KAAKya,yBANLza,KAAK0a,wBAUL1a,KAAK+Y,eAAeR,cAClB2B,GAAeA,EAAY3B,cAI5BvY,KAAK+Y,eAAeR,cACrB2B,GACAA,EAAY3B,cAEZvY,KAAK2a,2BANL3a,KAAK4a,0BAUL5a,KAAK+Y,eAAeP,uBAClB0B,GAAeA,EAAY1B,uBAI5BxY,KAAK+Y,eAAeP,uBACrB0B,GACAA,EAAY1B,uBAEZxY,KAAK6a,oCANL7a,KAAK8a,iCAQT,EAEAjP,EAAapH,UAAU0V,oBAAsB,WAC3CzB,EAAQ1Y,KAAK2Y,aAAc,UAC7B,EAEA9M,EAAapH,UAAU2V,kBAAoB,WACzC,IAAIxS,EAAO5H,KAEX,SAAS+a,EAASxb,EAAMyb,GAClBzb,KAAQyb,GAAO7U,EAAE0I,WAAWmM,EAAIzb,KAClC4B,EAAQ6Z,EAAKzb,GAAM,SAAU0b,GAC3B,OAAOrT,EAAKsC,QAAQ0H,KAAKqJ,EAC3B,GAEJ,CAEA,GAAI,mBAAoBjb,KAAK6Y,QAAS,CACpC,IAAIqC,EAAOlb,KAAK6Y,QAAQxP,eAAe5E,UACvCtD,EACE+Z,EACA,QACA,SAAUD,GACR,OAAO,SAAUzb,EAAQ0H,GACvB,IAAIiU,EAAcpB,EAAa7S,GAmB/B,OAlBIf,EAAEwC,OAAOzB,EAAK,WAAaiU,KAC7BjU,EAAMiU,EAAcjU,EAAIpC,WAAaoC,EACjClH,KAAKob,eACPpb,KAAKob,cAAc5b,OAASA,EAC5BQ,KAAKob,cAAclU,IAAMA,EACzBlH,KAAKob,cAAcC,YAAc,KACjCrb,KAAKob,cAAcE,cAAgBnV,EAAEoV,MACrCvb,KAAKob,cAAcI,YAAc,MAEjCxb,KAAKob,cAAgB,CACnB5b,OAAQA,EACR0H,IAAKA,EACLmU,YAAa,KACbC,cAAenV,EAAEoV,MACjBC,YAAa,OAIZP,EAAK1L,MAAMvP,KAAMqP,UAC1B,CACF,GACArP,KAAK2Y,aACL,WAGFxX,EACE+Z,EACA,oBACA,SAAUD,GACR,OAAO,SAAUQ,EAAQtT,GAiBvB,OAfKnI,KAAKob,gBACRpb,KAAKob,cAAgB,CAAC,GAEpBjV,EAAEwC,OAAO8S,EAAQ,WAAatV,EAAEwC,OAAOR,EAAO,YAC5CP,EAAKmR,eAAef,wBACjBhY,KAAKob,cAAcM,kBACtB1b,KAAKob,cAAcM,gBAAkB,CAAC,GAExC1b,KAAKob,cAAcM,gBAAgBD,GAAUtT,GAGlB,iBAAzBsT,EAAOrH,gBACTpU,KAAKob,cAAcO,qBAAuBxT,IAGvC8S,EAAK1L,MAAMvP,KAAMqP,UAC1B,CACF,GACArP,KAAK2Y,aACL,WAGFxX,EACE+Z,EACA,QACA,SAAUD,GAER,OAAO,SAAUzT,GAEf,IAAIwT,EAAMhb,KAEV,SAAS4b,IACP,GAAIZ,EAAII,gBACgC,OAAlCJ,EAAII,cAAcC,cACpBL,EAAII,cAAcC,YAAc,EAC5BzT,EAAKmR,eAAed,qBACtB+C,EAAII,cAAcS,QAAUrU,GAE9BwT,EAAIc,gBAAkBlU,EAAKmU,eACzBf,EAAII,cACJ,WACAha,IAGA4Z,EAAIgB,WAAa,IACnBhB,EAAII,cAAcE,cAAgBnV,EAAEoV,OAElCP,EAAIgB,WAAa,GAAG,CACtBhB,EAAII,cAAcI,YAAcrV,EAAEoV,MAElC,IAAI7D,EAAU,KAGd,GAFAsD,EAAII,cAAca,sBAChBjB,EAAIkB,kBAAkB,gBACpBtU,EAAKmR,eAAejB,uBAAwB,CAC9C,IAAIqE,EACFvU,EAAKmR,eAAejB,uBACtBJ,EAAU,CAAC,EACX,IACE,IAAI+D,EAAQ3Y,EACZ,IAAsB,IAAlBqZ,EAAwB,CAC1B,IAAIC,EAAapB,EAAIqB,wBACrB,GAAID,EAAY,CACd,IACInb,EAAOkH,EADPmU,EAAMF,EAAWG,OAAO1c,MAAM,WAElC,IAAKiD,EAAI,EAAGA,EAAIwZ,EAAI/Z,OAAQO,IAE1B2Y,GADAxa,EAAQqb,EAAIxZ,GAAGjD,MAAM,OACNsD,QACfgF,EAAQlH,EAAMa,KAAK,MACnB4V,EAAQ+D,GAAUtT,CAEtB,CACF,MACE,IAAKrF,EAAI,EAAGA,EAAIqZ,EAAc5Z,OAAQO,IAEpC4U,EADA+D,EAASU,EAAcrZ,IACLkY,EAAIkB,kBAAkBT,EAG9C,CAAE,MAAOpZ,GAGT,CACF,CACA,IAAIma,EAAO,KACX,GAAI5U,EAAKmR,eAAehB,oBACtB,IACEyE,EAAOxB,EAAIyB,YACb,CAAE,MAAOpa,GAET,CAEF,IAAIqa,EAAW,MACXF,GAAQ9E,KACVgF,EAAW,CAAC,EACRF,IAEA5U,EAAK+U,kBACH3B,EAAII,cAAca,uBAGpBS,EAASF,KAAO5U,EAAKgV,UAAUJ,GAE/BE,EAASF,KAAOA,GAGhB9E,IACFgF,EAAShF,QAAUA,IAGnBgF,IACF1B,EAAII,cAAcsB,SAAWA,GAE/B,IACE,IAAIG,EAAO7B,EAAI8B,OACfD,EAAgB,OAATA,EAAgB,IAAMA,EAC7B7B,EAAII,cAAcC,YAAcwB,EAChC7B,EAAIc,gBAAgBhL,MAClBlJ,EAAKgE,UAAUmR,gBAAgBF,GACjCjV,EAAKoV,kBAAkBhC,EAAII,cAC7B,CAAE,MAAO/Y,GAET,CACF,CAEJ,CAuBA,OArBA0Y,EAAS,SAAUC,GACnBD,EAAS,UAAWC,GACpBD,EAAS,aAAcC,GAGrB,uBAAwBA,GACxB7U,EAAE0I,WAAWmM,EAAIiC,oBAEjB9b,EAAQ6Z,EAAK,sBAAsB,SAAUC,GAC3C,OAAOrT,EAAKsC,QAAQ0H,KAClBqJ,OACA7Z,EACAwa,EAEJ,IAEAZ,EAAIiC,mBAAqBrB,EAEvBZ,EAAII,eAAiBxT,EAAKsV,oBAC5BlC,EAAII,cAAc3a,OAAQ,IAAII,OAAQJ,OAEjCwa,EAAK1L,MAAMvP,KAAMqP,UAC1B,CACF,GACArP,KAAK2Y,aACL,UAEJ,CAEI,UAAW3Y,KAAK6Y,SAClB1X,EACEnB,KAAK6Y,QACL,SACA,SAAUoC,GAER,OAAO,SAAUtE,EAAIwG,GAGnB,IADA,IAAI/Z,EAAO,IAAIwT,MAAMvH,UAAU9M,QACtBO,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,EAAKD,IAC1CM,EAAKN,GAAKuM,UAAUvM,GAEtB,IAEIoE,EAFA8S,EAAQ5W,EAAK,GACb5D,EAAS,MAET2b,EAAcpB,EAAaC,GAC3B7T,EAAEwC,OAAOqR,EAAO,WAAamB,EAC/BjU,EAAMiU,EAAcnB,EAAMlV,WAAakV,EAC9BA,IACT9S,EAAM8S,EAAM9S,IACR8S,EAAMxa,SACRA,EAASwa,EAAMxa,SAGf4D,EAAK,IAAMA,EAAK,GAAG5D,SACrBA,EAAS4D,EAAK,GAAG5D,QAEnB,IAAIkT,EAAW,CACblT,OAAQA,EACR0H,IAAKA,EACLmU,YAAa,KACbC,cAAenV,EAAEoV,MACjBC,YAAa,MAEf,GAAIpY,EAAK,IAAMA,EAAK,GAAGsU,QAAS,CAG9B,IAAI0F,EAAa1F,EAAQtU,EAAK,GAAGsU,SAEjChF,EAASiJ,qBAAuByB,EAAW3F,IAAI,gBAE3C7P,EAAKmR,eAAef,wBACtBtF,EAASgJ,gBAAkB9T,EAAKyV,aAC9BD,EACAxV,EAAKmR,eAAef,uBAG1B,CAoBA,OAlBIpQ,EAAKmR,eAAed,qBAClB7U,EAAK,IAAMA,EAAK,GAAGoZ,KACrB9J,EAASmJ,QAAUzY,EAAK,GAAGoZ,KAE3BpZ,EAAK,KACJ+C,EAAEwC,OAAOvF,EAAK,GAAI,WACnBA,EAAK,GAAGoZ,OAER9J,EAASmJ,QAAUzY,EAAK,GAAGoZ,OAG/B5U,EAAKmU,eAAerJ,EAAU,aAAStR,GACnCwG,EAAKsV,oBACPxK,EAASjS,OAAQ,IAAII,OAAQJ,OAKxBwa,EAAK1L,MAAMvP,KAAMoD,GAAMka,MAAK,SAAUC,GAC3C7K,EAAS8I,YAAcrV,EAAEoV,MACzB7I,EAAS2I,YAAckC,EAAKT,OAC5BpK,EAASuJ,sBAAwBsB,EAAK7F,QAAQD,IAAI,gBAClD,IAAIC,EAAU,KACV9P,EAAKmR,eAAejB,yBACtBJ,EAAU9P,EAAKyV,aACbE,EAAK7F,QACL9P,EAAKmR,eAAejB,yBAGxB,IAAI0E,EAAO,KAiCX,OAhCI5U,EAAKmR,eAAehB,qBACG,mBAAdwF,EAAKC,OAIdhB,EAAOe,EAAKE,QAAQD,SAGpB9F,GAAW8E,KACb9J,EAASgK,SAAW,CAAC,EACjBF,IAEuB,mBAAdA,EAAKc,KACdd,EAAKc,MAAK,SAAUE,GAEhBA,GACA5V,EAAK+U,kBAAkBjK,EAASuJ,uBAEhCvJ,EAASgK,SAASF,KAAO5U,EAAKgV,UAAUY,GAExC9K,EAASgK,SAASF,KAAOgB,CAE7B,IAEA9K,EAASgK,SAASF,KAAOA,GAGzB9E,IACFhF,EAASgK,SAAShF,QAAUA,IAGhC9P,EAAKoV,kBAAkBtK,GAChB6K,CACT,GACF,CACF,GACAvd,KAAK2Y,aACL,UAGN,EAEA9M,EAAapH,UAAUsX,eAAiB,SACtCrJ,EACAgL,EACAC,GAQA,OALEjL,EAASmJ,SACT7b,KAAK2c,kBAAkBjK,EAASiJ,wBAEhCjJ,EAASmJ,QAAU7b,KAAK4c,UAAUlK,EAASmJ,UAEtC7b,KAAK4L,UAAUmQ,eAAerJ,EAAUgL,EAASC,EAC1D,EAEA9R,EAAapH,UAAUkY,kBAAoB,SAAUiB,GACnD,SAAOA,GACLzX,EAAEwC,OAAOiV,EAAa,WACtBA,EAAYxJ,cAAcyJ,SAAS,QAGvC,EAEAhS,EAAapH,UAAUmY,UAAY,SAAUkB,GAC3C,OAAOC,KAAK7V,UAAU+D,EAAM8R,KAAK1d,MAAMyd,GAAO9d,KAAK6G,QAAQmM,aAC7D,EAEAnH,EAAapH,UAAU4Y,aAAe,SAAUW,EAAW7B,GACzD,IAAI8B,EAAa,CAAC,EAClB,IACE,IAAInb,EACJ,IAAsB,IAAlBqZ,GACF,GAAiC,mBAAtB6B,EAAUE,QAInB,IAFA,IAAI9B,EAAa4B,EAAUE,UACvBC,EAAgB/B,EAAWgC,QACvBD,EAAcE,MACpBJ,EAAWE,EAAchW,MAAM,IAAMgW,EAAchW,MAAM,GACzDgW,EAAgB/B,EAAWgC,YAI/B,IAAKtb,EAAI,EAAGA,EAAIqZ,EAAc5Z,OAAQO,IAAK,CACzC,IAAI2Y,EAASU,EAAcrZ,GAC3Bmb,EAAWxC,GAAUuC,EAAUvG,IAAIgE,EACrC,CAEJ,CAAE,MAAOpZ,GAET,CACA,OAAO4b,CACT,EAEApS,EAAapH,UAAUyY,gBAAkB,WACvC,OACEld,KAAK+Y,eAAeb,uBACpBlY,KAAK+Y,eAAeZ,uBACpBnY,KAAK+Y,eAAeX,mBAExB,EAEAvM,EAAapH,UAAUuY,kBAAoB,SAAUtK,GACnD,IAAIoK,EAASpK,EAAS2I,YAEtB,GACGyB,GAAU,KAAO9c,KAAK+Y,eAAeb,uBACrC4E,GAAU,KAAO9c,KAAK+Y,eAAeZ,uBAC1B,IAAX2E,GAAgB9c,KAAK+Y,eAAeX,oBACrC,CACA,IAAI9X,EAAQ,IAAIO,MAAM,mCAAqCic,GAC3Dxc,EAAMG,MAAQiS,EAASjS,MACvBT,KAAKkK,QAAQ5J,MAAMA,EAAO,CAAEge,WAAY,GAC1C,CACF,EAEAzS,EAAapH,UAAU4V,oBAAsB,WAC3C,GAAM,YAAara,KAAK6Y,SAAW7Y,KAAK6Y,QAAQvZ,QAAQ4P,IAIxD,IADA,IAAI0J,EACG5Y,KAAK2Y,aAAkB,IAAEpW,QAC9BqW,EAAI5Y,KAAK2Y,aAAkB,IAAExV,QAC7BnD,KAAK6Y,QAAQvZ,QAAQsZ,EAAE,IAAMA,EAAE,EAEnC,EAEA/M,EAAapH,UAAU6V,kBAAoB,WACzC,GAAM,YAAata,KAAK6Y,SAAW7Y,KAAK6Y,QAAQvZ,QAAQ4P,IAAxD,CAIA,IAAItH,EAAO5H,KACPue,EAAIve,KAAK6Y,QAAQvZ,QAkBjBM,EAAU,CAAC,QAAS,OAAQ,OAAQ,QAAS,OACjD,IACE,IAAK,IAAIkD,EAAI,EAAGC,EAAMnD,EAAQ2C,OAAQO,EAAIC,EAAKD,IAC7C0b,EAAY5e,EAAQkD,GAExB,CAAE,MAAOT,GACPrC,KAAKyZ,WAAWa,kBAAoB,CAAEha,MAAO+B,EAAEC,QACjD,CA5BA,CAKA,SAASkc,EAAYhf,GAGnB,IAAIyb,EAAOsD,EAAE/e,GACTif,EAAcF,EACdzN,EAAmB,SAAXtR,EAAoB,UAAYA,EAC5C+e,EAAE/e,GAAU,WACV,IAAI4D,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,WAClC/M,EAAU6D,EAAEoR,mBAAmBnU,GACnCwE,EAAKgE,UAAU8S,WAAWpc,EAASwO,GAC/BmK,GACF0D,SAASla,UAAU8K,MAAMxK,KAAKkW,EAAMwD,EAAarb,EAErD,EACAwE,EAAK+Q,aAAkB,IAAE3V,KAAK,CAACxD,EAAQyb,GACzC,CASF,EAEApP,EAAapH,UAAU8V,gBAAkB,YACjC,qBAAsBva,KAAK6Y,SAAW,gBAAiB7Y,KAAK6Y,UAGlE7Y,KAAK4e,gBAAgB,MACvB,EAEA/S,EAAapH,UAAU+V,cAAgB,WACrC,GAAM,qBAAsBxa,KAAK6Y,SAAW,gBAAiB7Y,KAAK6Y,QAAlE,CAGA,IAAIgG,EAAe7e,KAAK8e,YAAYC,KAAK/e,MACrCgf,EAAchf,KAAKif,WAAWF,KAAK/e,MACvCA,KAAKkf,YAAY,MAAOlf,KAAK6Y,QAAS,QAAS,UAAWgG,GAAc,GACxE7e,KAAKkf,YACH,MACAlf,KAAK6Y,QACL,OACA,aACAmG,GACA,EAVF,CAYF,EAEAnT,EAAapH,UAAUqa,YAAc,SAAUjJ,GAC7C,IACE,IAAIxT,EAAIuV,EAAQhC,oBAAoBC,EAAK7V,KAAK8Y,WAC1CqG,EAAS9c,GAAKA,EAAEkS,QAChB6K,EACFxH,EAAQzB,mBAAmB9T,EAAG,MAC9BuV,EAAQzB,mBAAmB9T,EAAG,UAE9B8c,IACCC,GACCxH,EAAQzB,mBAAmB9T,EAAG,QAAS,CAAC,SAAU,YAEpDrC,KAAKqf,gBAAgB,QAAShd,GACrBuV,EAAQzB,mBAAmB9T,EAAG,QAAS,CAAC,WAAY,WAC7DrC,KAAKqf,gBAAgB,QAAShd,EAAGA,EAAE8F,MAAO9F,EAAEid,QAEhD,CAAE,MAAOpN,GAET,CACF,EAEArG,EAAapH,UAAUwa,WAAa,SAAUpJ,GAC5C,IACE,IAAIxT,EAAIuV,EAAQhC,oBAAoBC,EAAK7V,KAAK8Y,WAC1CzW,GAAKA,EAAEkS,UACLqD,EAAQzB,mBAAmB9T,EAAG,YAChCrC,KAAKqf,gBAAgB,QAAShd,EAAGA,EAAE8F,OAEnCyP,EAAQzB,mBAAmB9T,EAAG,WAC9BA,EAAEwE,SACFxE,EAAEwE,QAAQtE,OAEVvC,KAAKuf,yBAAyBld,GAE9BuV,EAAQzB,mBAAmB9T,EAAG,WAC7BuV,EAAQzB,mBAAmB9T,EAAG,QAAS,CACtC,SACA,SACA,SACA,WACA,WAGFrC,KAAKqf,gBAAgB,QAAShd,EAAGA,EAAE8F,OAGzC,CAAE,MAAO+J,GAET,CACF,EAEArG,EAAapH,UAAU8a,yBAA2B,SAAUzK,GAC1D,GAAIA,EAAK0K,SACP,IAAK,IAAI1c,EAAI,EAAGA,EAAIgS,EAAKjO,QAAQtE,OAAQO,IACnCgS,EAAKjO,QAAQ/D,GAAG2c,UAClBzf,KAAKqf,gBAAgB,QAASvK,EAAMA,EAAKjO,QAAQ/D,GAAGqF,YAG/C2M,EAAK4K,eAAiB,GAAK5K,EAAKjO,QAAQiO,EAAK4K,gBACtD1f,KAAKqf,gBAAgB,QAASvK,EAAMA,EAAKjO,QAAQiO,EAAK4K,eAAevX,MAEzE,EAEA0D,EAAapH,UAAU4a,gBAAkB,SACvC3B,EACAtH,EACAjO,EACAwX,GAEA,QAAcve,IAAV+G,EACF,GACEnI,KAAKgZ,sBAC+B,aAApCpB,EAAQ1D,eAAekC,GAEvBjO,EAAQ,iBACH,CACL,IAAIkR,EAAczB,EAAQ/C,gBAAgBuB,GACtCpW,KAAKiZ,kBACHjZ,KAAKiZ,kBAAkBI,KACzBlR,EAAQ,cAEDnI,KAAKkZ,qBAAqBG,KACnClR,EAAQ,aAEZ,CAEF,IAAIyX,EAAgBhI,EAAQ3C,qBAC1B2C,EAAQpC,YAAYY,IAEtBpW,KAAK4L,UAAUiU,WAAWnC,EAASkC,EAAezX,EAAOwX,EAC3D,EAEA9T,EAAapH,UAAUgW,uBAAyB,WAC9C,IAAIlO,EAASvM,KAAK6Y,QAAQtM,SACFA,GAAUA,EAAOuT,KAAOvT,EAAOuT,IAAItT,UAIzDxM,KAAK6Y,QAAQkH,SACb/f,KAAK6Y,QAAQkH,QAAQC,WAIvBtH,EAAQ1Y,KAAK2Y,aAAc,aAC7B,EAEA9M,EAAapH,UAAUiW,qBAAuB,WAC5C,IAAInO,EAASvM,KAAK6Y,QAAQtM,OAO1B,KANwBA,GAAUA,EAAOuT,KAAOvT,EAAOuT,IAAItT,UAIzDxM,KAAK6Y,QAAQkH,SACb/f,KAAK6Y,QAAQkH,QAAQC,UACvB,CAGA,IAAIpY,EAAO5H,KACXmB,EACEnB,KAAK6Y,QACL,cACA,SAAUoC,GACR,OAAO,WACL,IAAIgF,EAAUrY,EAAKgS,UAAUE,KAC7BlS,EAAKsY,gBAAgBtY,EAAKiS,UAAWoG,GACjChF,GACFA,EAAK1L,MAAMvP,KAAMqP,UAErB,CACF,GACArP,KAAK2Y,aACL,cAGFxX,EACEnB,KAAK6Y,QAAQkH,QACb,aACA,SAAU9E,GACR,OAAO,WACL,IAAI/T,EAAMmI,UAAU9M,OAAS,EAAI8M,UAAU,QAAKjO,EAIhD,OAHI8F,GACFU,EAAKsY,gBAAgBtY,EAAKiS,UAAW3S,EAAM,IAEtC+T,EAAK1L,MAAMvP,KAAMqP,UAC1B,CACF,GACArP,KAAK2Y,aACL,aA/BF,CAiCF,EAEA9M,EAAapH,UAAUyb,gBAAkB,SAAUC,EAAMC,GACvD,IAAIC,EAAa1I,EAAUtX,MAAML,KAAK4Z,UAAUE,MAC5CwG,EAAW3I,EAAUtX,MAAM+f,GAC3BG,EAAa5I,EAAUtX,MAAM8f,GACjCngB,KAAK6Z,UAAYuG,EAEfC,EAAW3Z,WAAa4Z,EAAS5Z,UACjC2Z,EAAWzW,OAAS0W,EAAS1W,OAE7BwW,EAAKE,EAAS/Z,MAAQ+Z,EAASE,MAAQ,KAGvCH,EAAW3Z,WAAa6Z,EAAW7Z,UACnC2Z,EAAWzW,OAAS2W,EAAW3W,OAE/BuW,EAAOI,EAAWha,MAAQga,EAAWC,MAAQ,KAE/CxgB,KAAK4L,UAAU6U,kBAAkBN,EAAMC,EACzC,EAEAvU,EAAapH,UAAUkW,yBAA2B,YAC1C,qBAAsB3a,KAAK6Y,SAAW,SAAU7Y,KAAK8Y,aAGvD9Y,KAAK6Y,QAAQxB,iBACfrX,KAAK4e,gBAAgB,gBAErBlG,EAAQ1Y,KAAK2Y,aAAc,gBAE/B,EAEA9M,EAAapH,UAAUmW,uBAAyB,WAC9C,GAAM,qBAAsB5a,KAAK6Y,SAAW,SAAU7Y,KAAK8Y,UAG3D,GAAI9Y,KAAK6Y,QAAQxB,iBACfrX,KAAKkf,YACH,eACAlf,KAAK6Y,QACL,cACAzX,EACA,WACEpB,KAAK4L,UAAU8U,0BAA0B,SAC3C,EAAE3B,KAAK/e,OACP,GAEFA,KAAKkf,YACH,eACAlf,KAAK6Y,QACL,eACAzX,EACA,WACEpB,KAAK4L,UAAU8U,0BAA0B,UAC3C,EAAE3B,KAAK/e,OACP,OAEG,CACL,IAAI4H,EAAO5H,KACXmB,EACEnB,KAAK8Y,UAAU0D,KACf,YACA,SAAUvB,GACR,OAAO,WACLrT,EAAKgE,UAAU8U,0BAA0B,UACrCzF,GACFA,EAAK1L,MAAMvP,KAAMqP,UAErB,CACF,GACArP,KAAK2Y,aACL,gBAEFxX,EACEnB,KAAK8Y,UAAU0D,KACf,aACA,SAAUvB,GACR,OAAO,WACLrT,EAAKgE,UAAU8U,0BAA0B,WACrCzF,GACFA,EAAK1L,MAAMvP,KAAMqP,UAErB,CACF,GACArP,KAAK2Y,aACL,eAEJ,CACF,EAEA9M,EAAapH,UAAUkc,eAAiB,SAAUC,GAChD,IAAIte,EACF,0CAEAse,EAASC,WAFT,wBAKAD,EAASE,kBALT,yBAQAF,EAASG,mBACT,KAEEH,EAASI,aACX1e,GACE,aACAse,EAASI,WADT,WAIAJ,EAAS5e,WAJT,UAOA4e,EAAS3e,aACT,MAGJK,GAAW,mBAAqBse,EAASK,eAEzCjhB,KAAK4L,UAAU8S,WAAWpc,EAAS,SACnCtC,KAAKkhB,eAAe5e,EACtB,EAEAuJ,EAAapH,UAAUyc,eAAiB,SAAU5e,GAC5CtC,KAAK+Y,eAAeN,8BACtBzY,KAAKkK,QAAQ5J,MAAMgC,EAEvB,EAEAuJ,EAAapH,UAAUoW,kCAAoC,WACnD,qBAAsB7a,KAAK8Y,WAIjC9Y,KAAK4e,gBAAgB,wBACvB,EAEA/S,EAAapH,UAAUqW,gCAAkC,WACvD,GAAM,qBAAsB9a,KAAK8Y,UAAjC,CAIA,IAAIqI,EAAanhB,KAAK2gB,eAAe5B,KAAK/e,MAC1CA,KAAKkf,YACH,wBACAlf,KAAK8Y,UACL,0BACA,KACAqI,GACA,EATF,CAWF,EAEAtV,EAAapH,UAAUya,YAAc,SACnCkC,EACA5c,EACAiO,EACA4O,EACA/K,EACAgL,GAEI9c,EAAI6S,kBACN7S,EAAI6S,iBAAiB5E,EAAM6D,EAASgL,GACpCthB,KAAK0Z,cAAc0H,GAASpe,MAAK,WAC/BwB,EAAI0S,oBAAoBzE,EAAM6D,EAASgL,EACzC,KACSD,IACT7c,EAAI+c,YAAYF,EAAS/K,GACzBtW,KAAK0Z,cAAc0H,GAASpe,MAAK,WAC/BwB,EAAIgd,YAAYH,EAAS/K,EAC3B,IAEJ,EAEAzK,EAAapH,UAAUma,gBAAkB,SAAUwC,GAEjD,KAAOphB,KAAK0Z,cAAc0H,GAAS7e,QAC7BvC,KAAK0Z,cAAc0H,GAASje,OAChC+N,EAEJ,EAMAzI,EAAOC,QAAUmD,C,6BC/9BjB,IAAI1F,EAAI,EAAQ,KACZmF,EAAc,EAAQ,KACtBP,EAAS,EAAQ,KAkKrB,SAAS0W,EAAetS,EAAMtI,EAASY,GACrC,IAAInF,EAAU6M,EAAK7M,QACfof,EAASvS,EAAKuS,OAEbpf,IACHA,EAAU,6CAEZ,IAAIO,EAAS,CACX2Z,KAAMla,GAGJof,IACF7e,EAAO8e,MAAQxb,EAAEqC,MAAMkZ,IAGzBvb,EAAEyb,IAAIzS,EAAM,YAAa,CAAE7M,QAASO,IACpC4E,EAAS,KAAM0H,EACjB,CAEA,SAAS0S,EAAc1S,GAErB,IAAI1O,EAAQ0O,EAAKuB,UAAUjQ,MAS3B,OAPEA,GACiB,IAAjBA,EAAM8B,QACN4M,EAAK0B,qBACL1B,EAAK0B,oBAAoBpQ,QAEzBA,EAAQ0O,EAAK0B,oBAAoBpQ,OAE5BA,CACT,CAkCA,SAASqhB,EAAW3S,EAAMuB,EAAW7J,GACnC,IAAIwS,EAAclK,GAAQA,EAAK3H,KAAK6R,YAChCqI,EAASvS,GAAQA,EAAKuS,OACtBjhB,EAAQohB,EAAc1S,GAEtB4S,EAAQzW,EAAY0W,gBAAgBtR,EAAUpO,SAG9C2f,EAAQ,CACVC,UAAW,CACTC,MAJYC,EAAW1R,EAAWqR,EAAM,GAAIlb,GAK5CvE,QAJUyf,EAAM,KAYpB,GAJI1I,IACF4I,EAAMC,UAAU7I,YAAcA,GAG5B5Y,EAAO,CAKT,IAAI4hB,EACAC,EACAzF,EACA0F,EACAza,EACA0a,EACA1f,EAAG2f,EAGP,IAbqB,IAAjBhiB,EAAM8B,SACR0f,EAAMC,UAAUzhB,MAAQiQ,EAAUgS,SAClCT,EAAMC,UAAUS,IAAMzc,OAAOwK,EAAUkS,eAUzCX,EAAMY,OAAS,GACV/f,EAAI,EAAGA,EAAIrC,EAAM8B,SAAUO,EAE9Bwf,EAAQ,CACNQ,UAFFT,EAAa5hB,EAAMqC,IAEIoE,IAAMf,EAAE4c,YAAYV,EAAWnb,KAAO,YAC3DqJ,OAAQ8R,EAAW/gB,MAAQ,KAC3B9B,OACG6iB,EAAWW,MAA4B,MAApBX,EAAWW,KAE3BX,EAAWW,KADX,cAENxS,MAAO6R,EAAWY,QAEhBpc,EAAQqc,eACVZ,EAAMpb,IAAMmb,EAAWnb,KAGvBob,EAAM9iB,QACN8iB,EAAM9iB,OAAO2jB,UACbb,EAAM9iB,OAAO2jB,SAAS,sBAKxBtG,EAAO0F,EAAMza,EAAO,MACpB0a,EAAgBH,EAAWzZ,QAAUyZ,EAAWzZ,QAAQrG,OAAS,KAE/DkgB,EAAMW,KAAKC,MAAMb,EAAgB,GACjCD,EAAMF,EAAWzZ,QAAQjH,MAAM,EAAG8gB,GAClC5F,EAAOwF,EAAWzZ,QAAQ6Z,GAC1B3a,EAAOua,EAAWzZ,QAAQjH,MAAM8gB,IAG9B5F,IACFyF,EAAMzF,KAAOA,IAGX0F,GAAOza,KACTwa,EAAM1Z,QAAU,CAAC,EACb2Z,GAAOA,EAAIhgB,SACb+f,EAAM1Z,QAAQ2Z,IAAMA,GAElBza,GAAQA,EAAKvF,SACf+f,EAAM1Z,QAAQd,KAAOA,IAIrBua,EAAWjf,OACbkf,EAAMlf,KAAOif,EAAWjf,MAG1B6e,EAAMY,OAAO7f,KAAKsf,IAIpBL,EAAMY,OAAOS,UAET5B,IACFO,EAAMN,MAAQxb,EAAEqC,MAAMkZ,GAE1B,CAEA,OAAOO,CACT,CAEA,SAASG,EAAW1R,EAAWqR,EAAOlb,GACpC,OAAI6J,EAAU4I,KACL5I,EAAU4I,KACRzS,EAAQmb,gBACVD,EAEA,WAEX,CAaAtZ,EAAOC,QAAU,CACfkE,mBAvVF,SAA4BuC,EAAMtI,EAASY,GACzC,GAAI0H,EAAKoU,KAA4C,iBAArCjY,EAAYkY,MAAMrU,EAAKoU,KAAKjK,KAAyB,CACnE,IAAImK,EAAgB,IAAI5iB,MACxB4iB,EAAcnK,KAAOnK,EAAKoU,IAAIjK,KAC9BmK,EAAcnhB,QAAU6M,EAAKoU,IAAIjhB,QACjCmhB,EAAchjB,MAAQ0O,EAAKoU,IAAI9iB,MAC/BgjB,EAAcC,OAASvU,EAAKoU,IAC5BpU,EAAKoU,IAAME,CACb,CACAhc,EAAS,KAAM0H,EACjB,EA8UEtC,oBA5UF,SAA6BsC,EAAMtI,EAASY,GAE1C,GADA0H,EAAK3H,KAAO2H,EAAK3H,MAAQ,CAAC,EACtB2H,EAAKoU,IACP,IACEpU,EAAKuB,UACHvB,EAAKoU,IAAII,kBACTrY,EAAYjL,MAAM8O,EAAKoU,IAAKpU,EAAKmP,YAE/BzX,EAAQ+c,iBAoBlB,SAAyBzU,GACvB,IAAI0U,EAAQ,GACRN,EAAMpU,EAAKoU,IAEfM,EAAM7gB,KAAKugB,GAEX,KAAOA,EAAIG,QAAUH,EAAIO,OACvBP,EAAMA,EAAIG,QAAUH,EAAIO,MACxBD,EAAM7gB,KAAKugB,GAGbpd,EAAEyd,gBAAgBzU,EAAM0U,EAC1B,CA/BQD,CAAgBzU,EAEpB,CAAE,MAAO9M,GACP0I,EAAOzK,MAAM,wCAAyC+B,GACtD,IACE8M,EAAK7M,QACH6M,EAAKoU,IAAIjhB,SACT6M,EAAKoU,IAAIlK,aACTlK,EAAK7M,SACL4D,OAAOiJ,EAAKoU,IAChB,CAAE,MAAOQ,GACP5U,EAAK7M,QAAU4D,OAAOiJ,EAAKoU,MAAQrd,OAAO6d,EAC5C,QACO5U,EAAKoU,GACd,CAEF9b,EAAS,KAAM0H,EACjB,EAmTErC,4BAnSF,SAAqCqC,EAAMtI,EAASY,GAC7C0H,EAAK7M,SAAY6M,EAAKuB,WAAcvB,EAAKuS,QAC5Cja,EAAS,IAAI5G,MAAM,0CAA2C,MAEhE4G,EAAS,KAAM0H,EACjB,EA+REpC,YA7RF,SAAqBoC,EAAMtI,EAASY,GAClC,IAAIuc,EACDnd,EAAQa,SAAWb,EAAQa,QAAQsc,aAAgBnd,EAAQmd,YAC9D7U,EAAK3H,KAAOrB,EAAEqC,MAAM2G,EAAK3H,KAAM,CAC7Bwc,YAAaA,EACblT,MAAO3B,EAAK2B,MACZtH,SAAU3C,EAAQ2C,SAClBya,SAAU,UACVC,UAAW,aACXC,SAAU,aACVC,OAAQ,CAAC,EACT9U,KAAMH,EAAKG,KACX5C,SAAU,CACR4M,KAAM,qBACN7S,QAASI,EAAQJ,SAEnBib,OAAQvS,EAAKuS,SAEfja,EAAS,KAAM0H,EACjB,EA2QEnC,eAzQF,SAAwBjN,GACtB,OAAO,SAAUoP,EAAMtI,EAASY,GAC9B,IAAI4c,EAAc,CAAC,EAEftkB,GAAUA,EAAO0B,WACnB4iB,EAAYnd,IAAMnH,EAAO0B,SAASqY,KAClCuK,EAAYC,aAAevkB,EAAO0B,SAAS+E,QAG7C,IAAI+d,EAAe,aACd1d,EAAQ2M,WAEoB,IAAtB3M,EAAQ2M,YACjB+Q,GAAgB,cAFhBA,EAAe,KAIbA,IAAcF,EAAYG,QAAUD,GAEpC1f,OAAO4f,KAAKJ,GAAa9hB,OAAS,GACpC4D,EAAEyb,IAAIzS,EAAM,eAAgBkV,GAG9B5c,EAAS,KAAM0H,EACjB,CACF,EAmPElC,cAjPF,SAAuBlN,GACrB,OAAO,SAAUoP,EAAMtI,EAASY,GAC9B,IAAK1H,EACH,OAAO0H,EAAS,KAAM0H,GAExB,IAAIuV,EAAM3kB,EAAO4kB,WAAa,CAAC,EAC3BC,EAAM7kB,EAAO8kB,QAAU,CAAC,EAC5B1e,EAAEyb,IAAIzS,EAAM,cAAe,CACzB2V,WAAY3V,EAAK4V,UAAYhlB,EAAOyK,kBACpCua,UAAW3B,KAAK4B,MAAM7V,EAAK4V,UAAY,KACvCE,WAAY,CACVC,QAASR,EAAIS,UACbhB,SAAUO,EAAIP,SACdiB,eAAgBV,EAAIW,cACpBR,OAAQ,CACNS,MAAOV,EAAIU,MACX5P,OAAQkP,EAAIlP,WAIlBjO,EAAS,KAAM0H,EACjB,CACF,EA4NEjC,cA1NF,SAAuBnN,GACrB,OAAO,SAAUoP,EAAMtI,EAASY,GAC9B,IAAK1H,IAAWA,EAAO4kB,UACrB,OAAOld,EAAS,KAAM0H,GAKxB,IAHA,IAEIoW,EAFAC,EAAU,GACVC,EAAa1lB,EAAO4kB,UAAUa,SAAW,GAEpC1iB,EAAI,EAAG4iB,EAAID,EAAWljB,OAAQO,EAAI4iB,IAAK5iB,EAC9CyiB,EAAME,EAAW3iB,GACjB0iB,EAAQxiB,KAAK,CAAEsW,KAAMiM,EAAIjM,KAAMD,YAAakM,EAAIlM,cAElDlT,EAAEyb,IAAIzS,EAAM,iCAAkCqW,GAC9C/d,EAAS,KAAM0H,EACjB,CACF,EA4MEhC,QA1MF,SAAiBgC,EAAMtI,EAASY,GAC1B0H,EAAKuB,UACHvB,EAAKuB,UAAUiV,WA2CvB,SAA2BxW,EAAMtI,EAASY,GAKxC,IAJA,IAAIke,EAAaxW,EAAKuB,UAAUiV,WAC5BC,EAAS,GAETC,EAAmBF,EAAWpjB,OACzBO,EAAI,EAAGA,EAAI+iB,EAAkB/iB,IAAK,CACzC,IAAImf,EAAQH,EAAW3S,EAAMwW,EAAW7iB,GAAI+D,GAC5C+e,EAAO5iB,KAAKif,EACd,CAEA9b,EAAEyb,IAAIzS,EAAM,YAAa,CAAE2W,YAAaF,IACxCne,EAAS,KAAM0H,EACjB,CAtDM4W,CAAkB5W,EAAMtI,EAASY,GAwDvC,SAAsB0H,EAAMtI,EAASY,GACnC,IAAIhH,EAAQohB,EAAc1S,GAE1B,GAAI1O,EAAO,CACT,IAAIwhB,EAAQH,EAAW3S,EAAMA,EAAKuB,UAAW7J,GAC7CV,EAAEyb,IAAIzS,EAAM,YAAa,CAAE8S,MAAOA,IAClCxa,EAAS,KAAM0H,EACjB,KAAO,CACL,IAAIuB,EAAYvB,EAAKuB,UACjBqR,EAAQzW,EAAY0W,gBAAgBtR,EAAUpO,SAC9CyS,EAAYqN,EAAW1R,EAAWqR,EAAM,GAAIlb,GAC5CvE,EAAUyf,EAAM,GAEpB5S,EAAK7M,QAAUyS,EAAY,KAAOzS,EAClCmf,EAAetS,EAAMtI,EAASY,EAChC,CACF,CAtEMue,CAAa7W,EAAMtI,EAASY,GAG9Bga,EAAetS,EAAMtI,EAASY,EAElC,EAiME8F,YApBF,SAAqB0Y,GACnB,OAAO,SAAU9W,EAAMtI,EAASY,GAC9B,GAAIwe,EAAS,CACX,IAAIjT,EAAcnM,EAAQmM,aAAe,GACrCkT,EAAarf,EAAQqf,YAAc,GACvC/W,EAAK3H,KAAOye,EAAQ9W,EAAK3H,KAAMwL,EAAakT,EAC9C,CACAze,EAAS,KAAM0H,EACjB,CACF,E,6BCxVA,IAAIhJ,EAAI,EAAQ,KACZggB,EAAmB,EAAQ,KAC3BC,EAAiB,EAAQ,IAoB7B,SAASnb,EAAUjE,GACjBhH,KAAKgH,WAAaA,CACpB,CAEAiE,EAAUxG,UAAUgT,IAAM,SACxBtQ,EACAN,EACAwf,EACA5e,EACA6e,GAEK7e,GAAatB,EAAE0I,WAAWpH,KAC7BA,EAAW,WAAa,GAE1BtB,EAAEogB,8BAA8Bpf,EAAaN,EAASwf,GAEtD,IACInf,EAAMf,EAAEqgB,UAAU3f,GACtB7G,KAAKymB,iBACHtf,EACAD,EAJW,MAMX,KACAO,EACA6e,EACAzf,EAAQoC,QACRpC,EAAQC,UAEZ,EAEAmE,EAAUxG,UAAUqD,KAAO,SACzBX,EACAN,EACAa,EACAD,EACA6e,GAMA,GAJK7e,GAAatB,EAAE0I,WAAWpH,KAC7BA,EAAW,WAAa,IAGrBC,EACH,OAAOD,EAAS,IAAI5G,MAAM,8BAG5B,IAAImH,EAMJ,IAJEA,EADEhI,KAAKgH,WACWhH,KAAKgH,WAAWiB,SAASP,GAEzBvB,EAAE+B,UAAUR,IAEZpH,MAClB,OAAOmH,EAASO,EAAgB1H,OAGlC,IAAIomB,EAAY1e,EAAgBG,MAE5BjB,EAAMf,EAAEqgB,UAAU3f,GACtB7G,KAAKymB,iBACHtf,EACAD,EAJW,OAMXwf,EACAjf,EACA6e,EACAzf,EAAQoC,QACRpC,EAAQC,UAEZ,EAEAmE,EAAUxG,UAAU2D,gBAAkB,SACpCjB,EACAN,EACAwB,EACAZ,EACA6e,GAEK7e,GAAatB,EAAE0I,WAAWpH,KAC7BA,EAAW,WAAa,GAG1B,IACIP,EAAMf,EAAEqgB,UAAU3f,GACtB7G,KAAKymB,iBACHtf,EACAD,EAJW,OAMXmB,EACAZ,EACA6e,EACAzf,EAAQoC,QACRpC,EAAQC,UAEZ,EAMAmE,EAAUxG,UAAUgiB,iBAAmB,WACrC,IAAIvd,EACgB,oBAAVnJ,QAAyBA,aACjB,IAAR6H,GAAuBA,EAC7B+e,EAAczd,GAAWA,EAAQ0d,MAAQ1d,EAAQ0d,KAAK3G,QACtD7c,EAAOwT,MAAMnS,UAAU9C,MAAMoD,KAAKsK,WAEtC,GAAIsX,GAAqC,YAAtBA,EAAYE,MAAqB,CAClD,IAAIC,EAAWH,EAAYI,QACvBnf,EAAO5H,KACX8mB,EAASE,KAAI,WACXpf,EAAKqf,aAAa1X,WAAMnO,EAAWgC,EACrC,GACF,MACEpD,KAAKinB,aAAa1X,WAAMnO,EAAWgC,EAEvC,EAEA6H,EAAUxG,UAAUwiB,aAAe,SACjC9f,EACAD,EACA1H,EACAgI,EACAC,EACA6e,EACArd,EACAnC,GAEA,GAA4B,oBAAjBogB,aACT,OAmBJ,SAAuBpJ,EAAMrW,IACR,IAAIyf,cACVrX,gBACXiO,GACA,SAAUqJ,GAEV,IACA,SAAU5D,GACR9b,EAAS,IAAI5G,MAAM0iB,GACrB,GAEJ,CA9BW6D,CAAc5f,EAAMC,GAGX,UAAdX,EACFqf,EAAiBhf,EAAaD,EAAK1H,EAAQgI,EAAMC,EAAUwB,GAE3Dmd,EACEjf,EACAD,EACA1H,EACAgI,EACAC,EACA6e,EACArd,EAGN,EAgBAR,EAAOC,QAAUuC,C,6BCtLjB,IAAIF,EAAS,EAAQ,KACjB5E,EAAI,EAAQ,KAmChBsC,EAAOC,QAjCP,SAA0BvB,EAAaD,EAAK1H,EAAQgI,EAAMC,EAAUwB,GAClE,IAAIoe,EACAC,EAEAnhB,EAAEohB,eAAete,KACnBoe,EAAa,IAAIG,gBACjBF,EAAYzf,YAAW,WACrBwf,EAAWI,OACb,GAAGxe,IAGLG,MAAMlC,EAAK,CACT1H,OAAQA,EACRkY,QAAS,CACP,eAAgB,mBAChB,yBAA0BvQ,EAC1BugB,OAAQL,GAAcA,EAAWK,QAEnClL,KAAMhV,IAEL8V,MAAK,SAAUZ,GAEd,OADI4K,GAAWK,aAAaL,GACrB5K,EAASoB,MAClB,IACCR,MAAK,SAAU9V,GACdC,EAAS,KAAMD,EACjB,IACCogB,OAAM,SAAUtnB,GACfyK,EAAOzK,MAAMA,EAAMgC,SACnBmF,EAASnH,EACX,GACJ,C,4BChCA,IAAI6F,EAAI,EAAQ,KACZ4E,EAAS,EAAQ,KAqKrB,SAAS8c,EAAmBvlB,EAASua,GACnC,IAAI0G,EAAM,IAAI1iB,MAAMyB,GAEpB,OADAihB,EAAI1G,KAAOA,GAAQ,YACZ0G,CACT,CAEA9a,EAAOC,QAzKP,SACEvB,EACAD,EACA1H,EACAgI,EACAC,EACA6e,EACArd,GAEA,IAAI4S,EAMJ,KAJEA,EADEyK,EACQA,IA+Gd,WAGE,IAcIwB,EACAhlB,EAfAilB,EAAY,CACd,WACE,OAAO,IAAI1e,cACb,EACA,WACE,OAAO,IAAI2e,cAAc,iBAC3B,EACA,WACE,OAAO,IAAIA,cAAc,iBAC3B,EACA,WACE,OAAO,IAAIA,cAAc,oBAC3B,GAIEC,EAAeF,EAAUxlB,OAC7B,IAAKO,EAAI,EAAGA,EAAImlB,EAAcnlB,IAE5B,IACEglB,EAAUC,EAAUjlB,KACpB,KACF,CAAE,MAAOT,GAET,CAGF,OAAOylB,CACT,CA5IcI,IAIV,OAAOzgB,EAAS,IAAI5G,MAAM,6BAE5B,IACE,IACE,IAAIoc,EAAqB,WACvB,IACE,GAAIA,GAA6C,IAAvBpB,EAAQG,WAAkB,CAClDiB,OAAqB7b,EAErB,IAAI+mB,EAAgBhiB,EAAEiiB,UAAUvM,EAAQY,cACxC,IAgIQvL,EAhIO2K,IAiIb3K,EAAE4L,QAAuB,MAAb5L,EAAE4L,OA/Hd,YADArV,EAAS0gB,EAAc7nB,MAAO6nB,EAAchgB,OAEvC,GAiInB,SAA0B+I,GACxB,OAAOA,GAAK/K,EAAEwC,OAAOuI,EAAE4L,OAAQ,WAAa5L,EAAE4L,QAAU,KAAO5L,EAAE4L,OAAS,GAC5E,CAnIuBuL,CAAiBxM,GAAU,CACpC,GAAuB,MAAnBA,EAAQiB,OAAgB,CAE1B,IAAIxa,EACF6lB,EAAchgB,OAASggB,EAAchgB,MAAM7F,QAC7CyI,EAAOzK,MAAMgC,EACf,CAEAmF,EAAS,IAAI5G,MAAMqF,OAAO2V,EAAQiB,SACpC,KAAO,CAMLrV,EAASogB,EADP,+DAEJ,CACF,CACF,CAAE,MAAOS,GAIP,IAAIpW,EAEFA,EADEoW,GAAMA,EAAG7nB,MACL6nB,EAEA,IAAIznB,MAAMynB,GAElB7gB,EAASyK,EACX,CAgGR,IAAoBhB,CA/Fd,EAEA2K,EAAQ0M,KAAK/oB,EAAQ0H,GAAK,GACtB2U,EAAQ2M,mBACV3M,EAAQ2M,iBAAiB,eAAgB,oBACzC3M,EAAQ2M,iBAAiB,yBAA0BrhB,IAGjDhB,EAAEohB,eAAete,KACnB4S,EAAQ5S,QAAUA,GAGpB4S,EAAQoB,mBAAqBA,EAC7BpB,EAAQ4M,KAAKjhB,EACf,CAAE,MAAOkhB,GAEP,GAA8B,oBAAnBC,eAAgC,CAKzC,IAAK5oB,SAAWA,OAAO0B,SACrB,OAAOgG,EACL,IAAI5G,MACF,4DAOqC,UAAzCd,OAAO0B,SAASqY,KAAKhW,UAAU,EAAG,IACV,UAAxBoD,EAAIpD,UAAU,EAAG,KAEjBoD,EAAM,OAASA,EAAIpD,UAAU,IAG/B,IAAI8kB,EAAiB,IAAID,eACzBC,EAAeC,WAAa,WAAa,EACzCD,EAAeE,UAAY,WAGzBrhB,EAASogB,EAFC,oBACC,aAEb,EACAe,EAAelS,QAAU,WACvBjP,EAAS,IAAI5G,MAAM,wBACrB,EACA+nB,EAAeG,OAAS,WACtB,IAAIZ,EAAgBhiB,EAAEiiB,UAAUQ,EAAenM,cAC/ChV,EAAS0gB,EAAc7nB,MAAO6nB,EAAchgB,MAC9C,EACAygB,EAAeL,KAAK/oB,EAAQ0H,GAAK,GACjC0hB,EAAeH,KAAKjhB,EACtB,MACEC,EAAS,IAAI5G,MAAM,+CAEvB,CACF,CAAE,MAAOkjB,GACPtc,EAASsc,EACX,CACF,C,uBCzCAtb,EAAOC,QAAU,CACfrI,MApFF,SAAe6G,GACb,IAcIpE,EAAGkmB,EAdHnmB,EAAS,CACX6D,SAAU,KACVuiB,KAAM,KACNrf,KAAM,KACNrD,KAAM,KACNia,KAAM,KACN1G,KAAM5S,EACNZ,SAAU,KACVK,KAAM,KACN+C,SAAU,KACVlD,OAAQ,KACR0iB,MAAO,MAmBT,IAdW,KADXpmB,EAAIoE,EAAIlG,QAAQ,QAEd6B,EAAO6D,SAAWQ,EAAIpD,UAAU,EAAGhB,GACnCkmB,EAAOlmB,EAAI,GAEXkmB,EAAO,GAIE,KADXlmB,EAAIoE,EAAIlG,QAAQ,IAAKgoB,MAEnBnmB,EAAOomB,KAAO/hB,EAAIpD,UAAUklB,EAAMlmB,GAClCkmB,EAAOlmB,EAAI,IAIF,KADXA,EAAIoE,EAAIlG,QAAQ,IAAKgoB,IACP,CAEZ,IAAW,KADXlmB,EAAIoE,EAAIlG,QAAQ,IAAKgoB,IAcnB,OAXW,KADXlmB,EAAIoE,EAAIlG,QAAQ,IAAKgoB,IAEnBnmB,EAAO+G,KAAO1C,EAAIpD,UAAUklB,IAE5BnmB,EAAO+G,KAAO1C,EAAIpD,UAAUklB,EAAMlmB,GAClCD,EAAO2d,KAAOtZ,EAAIpD,UAAUhB,IAE9BD,EAAOyD,SAAWzD,EAAO+G,KAAK/J,MAAM,KAAK,GACzCgD,EAAO8D,KAAO9D,EAAO+G,KAAK/J,MAAM,KAAK,GACjCgD,EAAO8D,OACT9D,EAAO8D,KAAOwiB,SAAStmB,EAAO8D,KAAM,KAE/B9D,EAEPA,EAAO+G,KAAO1C,EAAIpD,UAAUklB,EAAMlmB,GAClCD,EAAOyD,SAAWzD,EAAO+G,KAAK/J,MAAM,KAAK,GACzCgD,EAAO8D,KAAO9D,EAAO+G,KAAK/J,MAAM,KAAK,GACjCgD,EAAO8D,OACT9D,EAAO8D,KAAOwiB,SAAStmB,EAAO8D,KAAM,KAEtCqiB,EAAOlmB,CAEX,MACED,EAAO+G,KAAO1C,EAAIpD,UAAUklB,EAAMlmB,GAClCD,EAAOyD,SAAWzD,EAAO+G,KAAK/J,MAAM,KAAK,GACzCgD,EAAO8D,KAAO9D,EAAO+G,KAAK/J,MAAM,KAAK,GACjCgD,EAAO8D,OACT9D,EAAO8D,KAAOwiB,SAAStmB,EAAO8D,KAAM,KAEtCqiB,EAAOlmB,EAWT,IAPW,KADXA,EAAIoE,EAAIlG,QAAQ,IAAKgoB,IAEnBnmB,EAAO0D,KAAOW,EAAIpD,UAAUklB,IAE5BnmB,EAAO0D,KAAOW,EAAIpD,UAAUklB,EAAMlmB,GAClCD,EAAO2d,KAAOtZ,EAAIpD,UAAUhB,IAG1BD,EAAO0D,KAAM,CACf,IAAI6iB,EAAYvmB,EAAO0D,KAAK1G,MAAM,KAClCgD,EAAO6G,SAAW0f,EAAU,GAC5BvmB,EAAOqmB,MAAQE,EAAU,GACzBvmB,EAAO2D,OAAS3D,EAAOqmB,MAAQ,IAAMrmB,EAAOqmB,MAAQ,IACtD,CACA,OAAOrmB,CACT,E,uBC/DA,SAASwmB,EAAyB/S,EAAS7R,EAAW8R,GACpD,GACE9R,EAAU4N,gBACV5N,EAAU4N,eAAe,oBACzB,CAEA,IADA,IAAIiX,EAAsB7kB,EAAU4S,iBAElCiS,EAAoBC,gBACpBD,EAAoBrS,eAEpBqS,EAAsBA,EAAoBC,eAE5C,IAAIC,EAAQ,SAAUjX,EAAO9K,EAAUgiB,GACrCH,EAAoBvkB,KAAK/E,KAAMuS,EAAO+D,EAAQ1E,KAAKnK,GAAWgiB,EAChE,EACAD,EAAMD,eAAiBD,EACvBE,EAAMvS,cAAgBV,EACtB9R,EAAU4S,iBAAmBmS,EAG7B,IADA,IAAIE,EAAyBjlB,EAAUyS,oBAErCwS,EAAuBC,mBACvBD,EAAuBzS,eAEvByS,EAAyBA,EAAuBC,kBAElD,IAAIC,EAAW,SAAUrX,EAAO9K,EAAUgiB,GACxCC,EAAuB3kB,KACrB/E,KACAuS,EACC9K,GAAYA,EAASwK,kBAAqBxK,EAC3CgiB,EAEJ,EACAG,EAASD,kBAAoBD,EAC7BE,EAAS3S,cAAgBV,EACzB9R,EAAUyS,oBAAsB0S,CAClC,CACF,CAEAnhB,EAAOC,QA3DP,SAAqB3I,EAAQuW,EAASC,GACpC,GAAKxW,EAAL,CAIA,IAII+C,EAAGzD,EAJH2L,EACF,4YAA4YnL,MAC1Y,KAGJ,IAAKiD,EAAI,EAAGA,EAAIkI,EAAQzI,SAAUO,EAG5B/C,EAFJV,EAAS2L,EAAQlI,KAEK/C,EAAOV,GAAQoF,WACnC4kB,EAAyB/S,EAASvW,EAAOV,GAAQoF,UAAW8R,EAXhE,CAcF,C,uBCjBA9N,EAAOC,QAAU,CACfjC,QAAS,SACT+C,SAAU,8BACVyJ,SAAU,QACVC,YAAa,QACbnC,mBAAoB,QACpB8Y,SAAU,EACVC,YAAa,G,6BCPf,IAAIC,EAAmB,EAAQ,KAG3BC,EAAmB,IAAI5Q,OACzB,6DAOF,SAAS6Q,IACP,OAAO,IACT,CAEA,SAASC,EAAM7H,GACb,IAAI7a,EAAO,CAAC,EAYZ,OAVAA,EAAK2iB,YAAc9H,EAEnB7a,EAAKN,IAAMmb,EAAWtgB,SACtByF,EAAKlG,KAAO+gB,EAAWrgB,WACvBwF,EAAKwb,KAAOX,EAAWxgB,aACvB2F,EAAKyb,OAASZ,EAAWpgB,aACzBuF,EAAKpE,KAAOif,EAAWjf,KAEvBoE,EAAKoB,QAdE,KAgBApB,CACT,CAEA,SAASgc,EAAMtB,EAAWkI,GAqBxB,MAAO,CACL3pB,MArBF,WACE,IAAI4pB,EAAc,GAElBD,EAAOA,GAAQ,EAEf,IACEC,EAAcN,EAAiB1pB,MAAM6hB,EACvC,CAAE,MAAO7f,GACPgoB,EAAc,EAChB,CAIA,IAFA,IAAI5pB,EAAQ,GAEHqC,EAAIsnB,EAAMtnB,EAAIunB,EAAY9nB,OAAQO,IACzCrC,EAAMuC,KAAK,IAAIknB,EAAMG,EAAYvnB,KAGnC,OAAOrC,CACT,CAGS6pB,GACPhoB,QAAS4f,EAAU5f,QACnBgX,KAAMiR,EAAuBrI,GAC7BQ,SAAUR,EAAUzhB,MACpBmiB,aAAcV,EAElB,CA2CA,SAASqI,EAAuBjqB,GAC9B,IAAIgZ,EAAOhZ,EAAMgZ,MAAQhZ,EAAMgZ,KAAK/W,QAAUjC,EAAMgZ,KAChDkR,EACFlqB,EAAMmqB,YAAYnR,MAClBhZ,EAAMmqB,YAAYnR,KAAK/W,QACvBjC,EAAMmqB,YAAYnR,KAEpB,OAAKA,GAASkR,EAID,UAATlR,EACKkR,EAEFlR,EANEA,GAAQkR,CAOnB,CAEA/hB,EAAOC,QAAU,CACfgiB,kBAjHF,WACE,MANqB,GAOvB,EAgHE1I,gBAxCF,SAAyB2I,GACvB,IAAKA,IAAWA,EAAOjqB,MACrB,MAAO,CAAC,wDAAyD,IAEnE,IAAIkqB,EAAgBD,EAAOjqB,MAAMspB,GAC7Ba,EAAW,YAUf,OARID,IACFC,EAAWD,EAAcA,EAAcroB,OAAS,GAKhDooB,GAJAA,EAASA,EAAOxpB,SACbypB,EAAcA,EAAcroB,OAAS,IAAM,IAAMsoB,EAAW,IAC7D,KAEc1pB,QAAQ,mBAAoB,KAEvC,CAAC0pB,EAAUF,EACpB,EAyBEV,cAAeA,EACf5pB,MA9DF,SAAegC,EAAG+nB,GAChB,IAAI7G,EAAMlhB,EAEV,GAAIkhB,EAAIG,QAAUH,EAAIO,MAAO,CAE3B,IADA,IAAI6B,EAAa,GACVpC,GACLoC,EAAW3iB,KAAK,IAAIwgB,EAAMD,EAAK6G,IAC/B7G,EAAMA,EAAIG,QAAUH,EAAIO,MAExBsG,EAAO,EAKT,OADAzE,EAAW,GAAGA,WAAaA,EACpBA,EAAW,EACpB,CACE,OAAO,IAAInC,EAAMD,EAAK6G,EAE1B,EA6CE5G,MAAOA,EACP0G,MAAOA,E,uBC3HT,IAAIY,EAASjmB,OAAOJ,UAAU4N,eAC1B0Y,EAAQlmB,OAAOJ,UAAUK,SAEzBkmB,EAAgB,SAAuBxmB,GACzC,IAAKA,GAA2B,oBAApBumB,EAAMhmB,KAAKP,GACrB,OAAO,EAGT,IAYIoQ,EAZAqW,EAAoBH,EAAO/lB,KAAKP,EAAK,eACrC0mB,EACF1mB,EAAIimB,aACJjmB,EAAIimB,YAAYhmB,WAChBqmB,EAAO/lB,KAAKP,EAAIimB,YAAYhmB,UAAW,iBAEzC,GAAID,EAAIimB,cAAgBQ,IAAsBC,EAC5C,OAAO,EAMT,IAAKtW,KAAOpQ,GAIZ,YAAsB,IAARoQ,GAAuBkW,EAAO/lB,KAAKP,EAAKoQ,EACxD,EAkCAnM,EAAOC,QAhCP,SAASF,IACP,IAAI1F,EACFqoB,EACAC,EACA3N,EACAnE,EACAzW,EAAS,CAAC,EACVod,EAAU,KACV1d,EAAS8M,UAAU9M,OAErB,IAAKO,EAAI,EAAGA,EAAIP,EAAQO,IAEtB,GAAe,OADfmd,EAAU5Q,UAAUvM,IAKpB,IAAKwW,KAAQ2G,EACXkL,EAAMtoB,EAAOyW,GAETzW,KADJuoB,EAAOnL,EAAQ3G,MAET8R,GAAQJ,EAAcI,IACxB3N,EAAQ0N,GAAOH,EAAcG,GAAOA,EAAM,CAAC,EAC3CtoB,EAAOyW,GAAQ9Q,EAAMiV,EAAO2N,SACH,IAATA,IAChBvoB,EAAOyW,GAAQ8R,IAKvB,OAAOvoB,CACT,C,6BC5DA,IAAIsD,EAAI,EAAQ,KAWhB,SAASklB,EAASvd,EAAOjH,GACvB7G,KAAK8N,MAAQA,EACb9N,KAAK6G,QAAUA,EACf7G,KAAKkL,WAAa,GAClBlL,KAAKyZ,WAAa,CAAC,CACrB,CAQA4R,EAAS5mB,UAAU6D,UAAY,SAAUzB,GACvC7G,KAAK8N,OAAS9N,KAAK8N,MAAMxF,UAAUzB,GACnC,IAAI0B,EAAavI,KAAK6G,QAEtB,OADA7G,KAAK6G,QAAUV,EAAEqC,MAAMD,EAAY1B,GAC5B7G,IACT,EAaAqrB,EAAS5mB,UAAUkI,aAAe,SAAU2e,GAI1C,OAHInlB,EAAE0I,WAAWyc,IACftrB,KAAKkL,WAAWlI,KAAKsoB,GAEhBtrB,IACT,EAeAqrB,EAAS5mB,UAAUyK,IAAM,SAAUC,EAAM1H,GAKvC,GAJKA,GAAatB,EAAE0I,WAAWpH,KAC7BA,EAAW,WAAa,IAGrBzH,KAAK6G,QAAQuM,QAChB,OAAO3L,EAAS,IAAI5G,MAAM,2BAG5Bb,KAAK8N,MAAMyd,eAAepc,GAC1B,IAAIsU,EAAgBtU,EAAKoU,IACzBvjB,KAAKwrB,iBACHrc,EACA,SAAUoU,EAAKzgB,GACb,GAAIygB,EAEF,OADAvjB,KAAK8N,MAAM2d,kBAAkBtc,GACtB1H,EAAS8b,EAAK,MAEvBvjB,KAAK8N,MAAM4d,QAAQ5oB,EAAG2E,EAAUgc,EAAetU,EACjD,EAAE4P,KAAK/e,MAEX,EAaAqrB,EAAS5mB,UAAU+mB,iBAAmB,SAAUrc,EAAM1H,GACpD,IAAIkkB,GAAkB,EAClBC,EAAmB5rB,KAAKkL,WAAW3I,OACnC2I,EAAalL,KAAKkL,WAClBrE,EAAU7G,KAAK6G,QAEfglB,EAAK,SAAUtI,EAAKzgB,GAClBygB,EACF9b,EAAS8b,EAAK,QAIhBoI,IAEuBC,EAKvB1gB,EAAWygB,GAAgB7oB,EAAG+D,EAASglB,GAJrCpkB,EAAS,KAAM3E,EAKnB,EAEA+oB,EAAG,KAAM1c,EACX,EAEA1G,EAAOC,QAAU2iB,C,4BCzHjB,IAAIllB,EAAI,EAAQ,KAuDhB,SAAS2lB,EAAY7J,EAAO8J,EAAMC,GAChC,IAAK/J,EACH,OAAQ+J,EAGV,IAMWlJ,EAAU5b,EANjB2b,EAASZ,EAAMY,OAEnB,IAAKA,GAA4B,IAAlBA,EAAOtgB,OACpB,OAAQypB,EAMV,IAFA,IAAIC,EAAaF,EAAKxpB,OAClB2pB,EAAcrJ,EAAOtgB,OAChBO,EAAI,EAAGA,EAAIopB,EAAappB,IAAK,CAIpC,GAFAggB,EADQD,EAAO/f,GACEggB,UAEZ3c,EAAEwC,OAAOma,EAAU,UACtB,OAAQkJ,EAGV,IAAK,IAAIjmB,EAAI,EAAGA,EAAIkmB,EAAYlmB,IAI9B,GAHAmB,EAAM6kB,EAAKhmB,GACA,IAAIqT,OAAOlS,GAET8C,KAAK8Y,GAChB,OAAO,CAGb,CACA,OAAO,CACT,CAEA,SAASqJ,EAAahd,EAAMqI,EAAU4U,EAAarhB,GAEjD,IAKIghB,EAAMnG,EALNoG,GAAQ,EACQ,cAAhBI,IACFJ,GAAQ,GAIV,IAME,GALAD,EAAOC,EAAQxU,EAAS6U,cAAgB7U,EAAS8U,aACjD1G,EAASzf,EAAEsR,IAAItI,EAAM,qBAAuB,CAAChJ,EAAEsR,IAAItI,EAAM,gBAIpD4c,GAAwB,IAAhBA,EAAKxpB,OAChB,OAAQypB,EAEV,GAAsB,IAAlBpG,EAAOrjB,SAAiBqjB,EAAO,GACjC,OAAQoG,EAIV,IADA,IAAIO,EAAe3G,EAAOrjB,OACjBO,EAAI,EAAGA,EAAIypB,EAAczpB,IAChC,GAAIgpB,EAAYlG,EAAO9iB,GAAIipB,EAAMC,GAC/B,OAAO,CAGb,CAAE,MACA3pB,GAGI2pB,EACFxU,EAAS6U,cAAgB,KAEzB7U,EAAS8U,aAAe,KAE1B,IAAIE,EAAWR,EAAQ,gBAAkB,eASzC,OARAjhB,EAAOzK,MACL,4CACEksB,EACA,4BACAA,EACA,IACFnqB,IAEM2pB,CACV,CACA,OAAO,CACT,CAqEAvjB,EAAOC,QAAU,CACfsF,WA7MF,SAAoBmB,EAAMqI,GACxB,IAAI1G,EAAQ3B,EAAK2B,MACb2b,EAAWtmB,EAAEumB,OAAO5b,IAAU,EAC9BoC,EAAcsE,EAAStE,YAG3B,QAAIuZ,GAFiBtmB,EAAEumB,OAAOxZ,IAAgB,GAMhD,EAoMEhF,gBAlMF,SAAyBnD,GACvB,OAAO,SAAUoE,EAAMqI,GACrB,IAAImV,IAAexd,EAAK6B,mBACjB7B,EAAK6B,YACZ,IAAI5N,EAAO+L,EAAKwC,qBACTxC,EAAKwC,cACZ,IACMxL,EAAE0I,WAAW2I,EAASoV,iBACxBpV,EAASoV,eAAeD,EAAYvpB,EAAM+L,EAE9C,CAAE,MAAO9M,GACPmV,EAASoV,eAAiB,KAC1B7hB,EAAOzK,MAAM,+CAAgD+B,EAC/D,CACA,IACE,GACE8D,EAAE0I,WAAW2I,EAASvJ,cACtBuJ,EAASvJ,YAAY0e,EAAYvpB,EAAM+L,GAEvC,OAAO,CAEX,CAAE,MAAO9M,GACPmV,EAASvJ,YAAc,KACvBlD,EAAOzK,MAAM,qDAAsD+B,EACrE,CACA,OAAO,CACT,CACF,EAwKE8L,oBAtKF,SAA6BpD,GAC3B,OAAO,SAAUoE,EAAMqI,GACrB,OAAQ2U,EAAahd,EAAMqI,EAAU,YAAazM,EACpD,CACF,EAmKEqD,gBAjKF,SAAyBrD,GACvB,OAAO,SAAUoE,EAAMqI,GACrB,OAAO2U,EAAahd,EAAMqI,EAAU,WAAYzM,EAClD,CACF,EA8JEsD,iBAxEF,SAA0BtD,GACxB,OAAO,SAAUoE,EAAMqI,GACrB,IAAI1U,EAAGiD,EAAG8mB,EAAiB9pB,EAAuB+pB,EAAiBC,EAEnE,IAIE,IAHmB,IACnBF,EAAkBrV,EAASqV,kBAEwB,IAA3BA,EAAgBtqB,OACtC,OAAO,EAKT,GAFAwqB,EAgCN,SAA0B5d,GACxB,IAAIqN,EAAOrN,EAAKqN,KACZuQ,EAAW,GAKf,GAAIvQ,EAAKsJ,YAEP,IADA,IAAIH,EAAanJ,EAAKsJ,YACbhjB,EAAI,EAAGA,EAAI6iB,EAAWpjB,OAAQO,IAAK,CAC1C,IAAImf,EAAQ0D,EAAW7iB,GACvBiqB,EAAS/pB,KAAKmD,EAAEsR,IAAIwK,EAAO,qBAC7B,CAEEzF,EAAKyF,OACP8K,EAAS/pB,KAAKmD,EAAEsR,IAAI+E,EAAM,4BAExBA,EAAKla,SACPyqB,EAAS/pB,KAAKmD,EAAEsR,IAAI+E,EAAM,iBAE5B,OAAOuQ,CACT,CArDiBC,CAAiB7d,GAEJ,IAApB4d,EAASxqB,OACX,OAAO,EAIT,IADAQ,EAAM8pB,EAAgBtqB,OACjBO,EAAI,EAAGA,EAAIC,EAAKD,IAGnB,IAFAgqB,EAAkB,IAAI1T,OAAOyT,EAAgB/pB,GAAI,MAE5CiD,EAAI,EAAGA,EAAIgnB,EAASxqB,OAAQwD,IAG/B,GAFmB+mB,EAAgB9iB,KAAK+iB,EAAShnB,IAG/C,OAAO,CAIf,CAAE,MACA1D,GAGAmV,EAASqV,gBAAkB,KAC3B9hB,EAAOzK,MACL,oGAEJ,CAEA,OAAO,CACT,CACF,E,6BCrLA,IAAI6F,EAAI,EAAQ,KAehB,SAAS8mB,EAAMC,EAAahhB,EAAKnB,EAAQlE,GACvC7G,KAAKktB,YAAcA,EACnBltB,KAAKkM,IAAMA,EACXlM,KAAK+K,OAASA,EACd/K,KAAK6G,QAAUA,EACf7G,KAAKoL,WAAa,GAClBpL,KAAKmtB,aAAe,GACpBntB,KAAKotB,gBAAkB,GACvBptB,KAAKqtB,WAAa,GAClBrtB,KAAKstB,YAAc,KACnBttB,KAAKutB,aAAe,KACpBvtB,KAAKwtB,eAAiB,IACxB,CAOAP,EAAMxoB,UAAU6D,UAAY,SAAUzB,GACpC7G,KAAKkM,KAAOlM,KAAKkM,IAAI5D,UAAUzB,GAC/B,IAAI0B,EAAavI,KAAK6G,QAEtB,OADA7G,KAAK6G,QAAUV,EAAEqC,MAAMD,EAAY1B,GAC5B7G,IACT,EAWAitB,EAAMxoB,UAAUsJ,aAAe,SAAU0f,GAIvC,OAHItnB,EAAE0I,WAAW4e,IACfztB,KAAKoL,WAAWpI,KAAKyqB,GAEhBztB,IACT,EAEAitB,EAAMxoB,UAAU8mB,eAAiB,SAAUpc,GACzCnP,KAAKmtB,aAAanqB,KAAKmM,EACzB,EAEA8d,EAAMxoB,UAAUgnB,kBAAoB,SAAUtc,GAC5C,IAAIue,EAAM1tB,KAAKmtB,aAAansB,QAAQmO,IACvB,IAATue,GACF1tB,KAAKmtB,aAAaQ,OAAOD,EAAK,EAElC,EAYAT,EAAMxoB,UAAUinB,QAAU,SACxBvc,EACA1H,EACAgc,EACAmK,GAEKnmB,GAAatB,EAAE0I,WAAWpH,KAC7BA,EAAW,WAEX,GAEF,IAAIomB,EAAkB7tB,KAAK8tB,iBAAiB3e,GAC5C,GAAI0e,EAAgBE,KAGlB,OAFA/tB,KAAKyrB,kBAAkBmC,QACvBnmB,EAASomB,EAAgBtK,KAK3B,GAFAvjB,KAAKguB,UAAU7e,EAAMsU,GACrBzjB,KAAKyrB,kBAAkBmC,GAClB5tB,KAAK6G,QAAQwM,SAAlB,CAIArT,KAAKotB,gBAAgBpqB,KAAKmM,GAC1B,IACEnP,KAAKiuB,gBACH9e,EACA,SAAUoU,EAAKhG,GACbvd,KAAKkuB,uBAAuB/e,GAC5B1H,EAAS8b,EAAKhG,EAChB,EAAEwB,KAAK/e,MAEX,CAAE,MAAOqC,GACPrC,KAAKkuB,uBAAuB/e,GAC5B1H,EAASpF,EACX,CAbA,MAFEoF,EAAS,IAAI5G,MAAM,qBAgBvB,EAQAosB,EAAMxoB,UAAU0pB,KAAO,SAAU1mB,GAC1BtB,EAAE0I,WAAWpH,KAGlBzH,KAAKutB,aAAe9lB,EAChBzH,KAAKouB,mBAGLpuB,KAAKwtB,iBACPxtB,KAAKwtB,eAAiBa,cAAcruB,KAAKwtB,iBAE3CxtB,KAAKwtB,eAAiBc,YACpB,WACEtuB,KAAKouB,gBACP,EAAErP,KAAK/e,MACP,MAEJ,EASAitB,EAAMxoB,UAAUqpB,iBAAmB,SAAU3e,GAE3C,IADA,IAAInL,EAAI,KACClB,EAAI,EAAGC,EAAM/C,KAAKoL,WAAW7I,OAAQO,EAAIC,EAAKD,IAErD,KADAkB,EAAIhE,KAAKoL,WAAWtI,GAAGqM,EAAMnP,KAAK6G,gBACdzF,IAAV4C,EAAEuf,IACV,MAAO,CAAEwK,MAAM,EAAMxK,IAAKvf,EAAEuf,KAGhC,MAAO,CAAEwK,MAAM,EAAOxK,IAAK,KAC7B,EASA0J,EAAMxoB,UAAUwpB,gBAAkB,SAAU9e,EAAM1H,GAChD,IAAI8mB,EAAoBvuB,KAAKktB,YAAYsB,WAAWrf,GAChDof,EAAkBC,WACpBxuB,KAAKkM,IAAI3E,SACP4H,EACA,SAAUoU,EAAKhG,GACTgG,EACFvjB,KAAKyuB,YAAYlL,EAAKpU,EAAM1H,GAE5BA,EAAS8b,EAAKhG,EAElB,EAAEwB,KAAK/e,OAEAuuB,EAAkBjuB,MAC3BmH,EAAS8mB,EAAkBjuB,OAE3BN,KAAKkM,IAAI3E,SAASgnB,EAAkB7mB,QAASD,EAEjD,EAGA,IAAIinB,EAAmB,CACrB,aACA,YACA,kBACA,YACA,eACA,eACA,QACA,aAWFzB,EAAMxoB,UAAUgqB,YAAc,SAAUlL,EAAKpU,EAAM1H,GACjD,IAAIknB,GAAc,EAClB,GAAI3uB,KAAK6G,QAAQ+nB,cAAe,CAC9B,IAAK,IAAI9rB,EAAI,EAAGC,EAAM2rB,EAAiBnsB,OAAQO,EAAIC,EAAKD,IACtD,GAAIygB,EAAI1G,OAAS6R,EAAiB5rB,GAAI,CACpC6rB,GAAc,EACd,KACF,CAEEA,GAAexoB,EAAEohB,eAAevnB,KAAK6G,QAAQgoB,cAC/C1f,EAAK2f,QAAU3f,EAAK2f,QAAU3f,EAAK2f,QAAU,EAAI,EAC7C3f,EAAK2f,QAAU9uB,KAAK6G,QAAQgoB,aAC9BF,GAAc,GAGpB,CACIA,EACF3uB,KAAK+uB,iBAAiB5f,EAAM1H,GAE5BA,EAAS8b,EAEb,EASA0J,EAAMxoB,UAAUsqB,iBAAmB,SAAU5f,EAAM1H,GACjDzH,KAAKqtB,WAAWrqB,KAAK,CAAEmM,KAAMA,EAAM1H,SAAUA,IAExCzH,KAAKstB,cACRttB,KAAKstB,YAAcgB,YACjB,WACE,KAAOtuB,KAAKqtB,WAAW9qB,QAAQ,CAC7B,IAAIysB,EAAchvB,KAAKqtB,WAAWlqB,QAClCnD,KAAKiuB,gBAAgBe,EAAY7f,KAAM6f,EAAYvnB,SACrD,CACF,EAAEsX,KAAK/e,MACPA,KAAK6G,QAAQ+nB,eAGnB,EAUA3B,EAAMxoB,UAAUypB,uBAAyB,SAAU/e,GACjD,IAAIue,EAAM1tB,KAAKotB,gBAAgBpsB,QAAQmO,IAC1B,IAATue,IACF1tB,KAAKotB,gBAAgBO,OAAOD,EAAK,GACjC1tB,KAAKouB,iBAET,EAEAnB,EAAMxoB,UAAUupB,UAAY,SAAUxmB,EAAMic,GAC1C,GAAIzjB,KAAK+K,QAAU/K,KAAK6G,QAAQsM,QAAS,CACvC,IAAI7Q,EAAUmhB,EAGd,GADAnhB,GADAA,EAAUA,GAAW6D,EAAEsR,IAAIjQ,EAAM,kCACZrB,EAAEsR,IAAIjQ,EAAM,wCAG/B,YADAxH,KAAK+K,OAAOzK,MAAMgC,IAGpBA,EAAU6D,EAAEsR,IAAIjQ,EAAM,uBAEpBxH,KAAK+K,OAAOmE,IAAI5M,EAEpB,CACF,EAEA2qB,EAAMxoB,UAAU2pB,eAAiB,WAC/B,SACEjoB,EAAE0I,WAAW7O,KAAKutB,eACW,IAA7BvtB,KAAKmtB,aAAa5qB,QACc,IAAhCvC,KAAKotB,gBAAgB7qB,UAEjBvC,KAAKwtB,iBACPxtB,KAAKwtB,eAAiBa,cAAcruB,KAAKwtB,iBAE3CxtB,KAAKutB,gBACE,EAGX,EAEA9kB,EAAOC,QAAUukB,C,6BC3SjB,IAAI9mB,EAAI,EAAQ,KAOhB,SAAS8oB,EAAYpoB,GACnB7G,KAAKkvB,UAAY/oB,EAAEoV,MACnBvb,KAAKmvB,QAAU,EACfnvB,KAAKovB,cAAgB,EACrBpvB,KAAKikB,SAAW,KAChBjkB,KAAKqvB,gBAAkB,CAAC,EACxBrvB,KAAKsvB,gBAAgBzoB,EACvB,CA8FA,SAAS0oB,EAAUpgB,EAAMqgB,EAAOL,GAC9B,OAAQhgB,EAAKsgB,iBAAmBD,GAAS,GAAKL,EAAUK,CAC1D,CAEA,SAASE,EACPzL,EACApd,EACAvG,EACAkuB,EACAmB,EACAC,EACAC,GAEA,IAAInoB,EAAU,KAad,OAZIpH,IACFA,EAAQ,IAAIO,MAAMP,IAEfA,GAAUkuB,IACb9mB,EAWJ,SACEuc,EACApd,EACA8oB,EACAC,EACAC,GAEA,IAEIC,EAFA9L,EACFnd,EAAQmd,aAAgBnd,EAAQa,SAAWb,EAAQa,QAAQsc,YAG3D8L,EADED,EACI,+DAEA,sDAER,IAAI1gB,EAAO,CACTqN,KAAM,CACJla,QAAS,CACPka,KAAMsT,EACNnO,MAAO,CACLkI,SAAU8F,EACVI,eAAgBH,KAItBzL,SAAU,aACVH,YAAaA,EACbtX,SAAU,CACRjG,QACGI,EAAQ6F,UAAY7F,EAAQ6F,SAASjG,SAAYI,EAAQJ,UAG/C,YAAbwd,GACF9U,EAAK8U,SAAW,UAChB9U,EAAK+U,UAAY,aACjB/U,EAAKzC,SAAS4M,KAAO,sBACC,WAAb2K,GACT9U,EAAK+U,UAAYrd,EAAQqd,WAAa,UACtC/U,EAAKzC,SAAS4M,KAAOzS,EAAQ6F,SAAS4M,MAChB,iBAAb2K,IACT9U,EAAK+U,UAAYrd,EAAQqd,WAAa,eACtC/U,EAAKzC,SAAS4M,KAAOzS,EAAQ6F,SAAS4M,MAExC,OAAOnK,CACT,CAvDc6gB,CACR/L,EACApd,EACA8oB,EACAC,EACAC,IAGG,CAAEvvB,MAAOA,EAAOkuB,WAAYA,EAAY9mB,QAASA,EAC1D,CAvHAunB,EAAYgB,eAAiB,CAC3Bf,UAAW/oB,EAAEoV,MACbsO,cAAUzoB,EACV2uB,oBAAgB3uB,GAWlB6tB,EAAYxqB,UAAU6qB,gBAAkB,SAAUzoB,QACtBzF,IAAtByF,EAAQqoB,YACVD,EAAYgB,eAAef,UAAYroB,EAAQqoB,gBAExB9tB,IAArByF,EAAQgjB,WACVoF,EAAYgB,eAAepG,SAAWhjB,EAAQgjB,eAEjBzoB,IAA3ByF,EAAQkpB,iBACVd,EAAYgB,eAAeF,eAAiBlpB,EAAQkpB,eAExD,EAiBAd,EAAYxqB,UAAU+pB,WAAa,SAAUrf,EAAMoM,GAEjD,IAAI2U,GADJ3U,EAAMA,GAAOpV,EAAEoV,OACSvb,KAAKkvB,WACzBgB,EAAc,GAAKA,GAAe,OACpClwB,KAAKkvB,UAAY3T,EACjBvb,KAAKovB,cAAgB,GAGvB,IAAIO,EAAkBV,EAAYgB,eAAepG,SAC7CsG,EAAwBlB,EAAYgB,eAAeF,eAEvD,GAAIR,EAAUpgB,EAAMwgB,EAAiB3vB,KAAKmvB,SACxC,OAAOO,EACL1vB,KAAKikB,SACLjkB,KAAKqvB,gBACLM,EAAkB,sBAClB,GAEG,GAAIJ,EAAUpgB,EAAMghB,EAAuBnwB,KAAKovB,eACrD,OAAOM,EACL1vB,KAAKikB,SACLjkB,KAAKqvB,gBACLc,EAAwB,6BACxB,GAGJnwB,KAAKmvB,UACLnvB,KAAKovB,gBAEL,IAAIZ,GAAce,EAAUpgB,EAAMwgB,EAAiB3vB,KAAKmvB,SACpDU,EAAYrB,EAGhB,OAFAA,EACEA,IAAee,EAAUpgB,EAAMghB,EAAuBnwB,KAAKovB,eACtDM,EACL1vB,KAAKikB,SACLjkB,KAAKqvB,gBACL,KACAb,EACAmB,EACAQ,EACAN,EAEJ,EAEAZ,EAAYxqB,UAAU2rB,mBAAqB,SAAUnM,EAAUpd,GAC7D7G,KAAKikB,SAAWA,EAChBjkB,KAAKqvB,gBAAkBxoB,CACzB,EA+EA4B,EAAOC,QAAUumB,C,6BCvLjB,IAAIA,EAAc,EAAQ,KACtBhC,EAAQ,EAAQ,KAChB5B,EAAW,EAAQ,KACnBllB,EAAI,EAAQ,KAShB,SAASwE,EAAQ9D,EAASqF,EAAKnB,EAAQa,EAAWqY,GAChDjkB,KAAK6G,QAAUV,EAAEqC,MAAM3B,GACvB7G,KAAK+K,OAASA,EACdJ,EAAQuiB,YAAYoC,gBAAgBtvB,KAAK6G,SACzC8D,EAAQuiB,YAAYkD,mBAAmBnM,EAAUjkB,KAAK6G,SACtD7G,KAAKkM,IAAMA,EACXlM,KAAK8N,MAAQ,IAAImf,EAAMtiB,EAAQuiB,YAAahhB,EAAKnB,EAAQ/K,KAAK6G,SAG9D,IAAIwpB,EAASrwB,KAAK6G,QAAQwpB,QAAU,KAChCC,EAAeD,IACjBrwB,KAAKqwB,OAASA,EAEdrwB,KAAK6G,QAAQwpB,OAAS,6BACtBrwB,KAAK6G,QAAQ4E,mBAAmB4kB,OAAS,8BAEzCrwB,KAAKqwB,OAAS,KAGhBrwB,KAAK0M,SAAW,IAAI2e,EAASrrB,KAAK8N,MAAO9N,KAAK6G,SAC9C7G,KAAK4L,UAAYA,EACjB2kB,EAAmB1pB,GACnB7G,KAAKiP,UAAY,KACjBjP,KAAKwwB,cAAgB,MACvB,CAiMA,SAASD,EAAmB1pB,GACtBA,EAAQ4pB,kBACV5vB,MAAM4vB,gBAAkB5pB,EAAQ4pB,gBAEpC,CAOA,SAASH,EAAeD,GACtB,IAAKA,EACH,OAAO,EAGT,IAAKA,EAAOK,OAAiC,mBAAjBL,EAAOK,MACjC,OAAO,EAGT,IAAIA,EAAQL,EAAOK,QAEnB,SAAKA,IAAUA,EAAMC,QAAkC,mBAAjBD,EAAMC,OAK9C,CArNAhmB,EAAQuiB,YAAc,IAAI+B,EALL,CACnBpF,SAAU,EACVkG,eAAgB,KAKlBplB,EAAQlG,UAAUpF,OAAS,SAAUwH,GAEnC,OADA8D,EAAQuiB,YAAYoC,gBAAgBzoB,GAC7B7G,IACT,EAEA2K,EAAQlG,UAAU6D,UAAY,SAAUzB,EAASmI,GAC/C,IAAIzG,EAAavI,KAAK6G,QAClBa,EAAU,CAAC,EACXsH,IACFtH,EAAU,CAAEA,QAASsH,IAGvBhP,KAAK6G,QAAUV,EAAEqC,MAAMD,EAAY1B,EAASa,GAG5C,IAAI2oB,EAASrwB,KAAK6G,QAAQwpB,QAAU,KAmBpC,OAlBIC,EAAeD,IACjBrwB,KAAKqwB,OAASA,EAEdrwB,KAAK6G,QAAQwpB,OAAS,6BACtBrwB,KAAK6G,QAAQ4E,mBAAmB4kB,OAAS,8BAEzCrwB,KAAKqwB,OAAS,KAGhBrwB,KAAK0M,UAAY1M,KAAK0M,SAASpE,UAAUtI,KAAK6G,SAC9C7G,KAAK4L,WAAa5L,KAAK4L,UAAUtD,UAAUtI,KAAK6G,SAChD0pB,EAAmB1pB,GACnB7G,KAAKX,OAAOW,KAAK6G,SAEbypB,EAAezpB,EAAQwpB,UACzBrwB,KAAKqwB,OAASxpB,EAAQwpB,QAGjBrwB,IACT,EAEA2K,EAAQlG,UAAUyK,IAAM,SAAUC,GAChC,IAAI2B,EAAQ9Q,KAAK4wB,mBACjB,OAAO5wB,KAAK6wB,KAAK/f,EAAO3B,EAC1B,EAEAxE,EAAQlG,UAAU+K,MAAQ,SAAUL,GAClCnP,KAAK6wB,KAAK,QAAS1hB,EACrB,EAEAxE,EAAQlG,UAAUgL,KAAO,SAAUN,GACjCnP,KAAK6wB,KAAK,OAAQ1hB,EACpB,EAEAxE,EAAQlG,UAAUiL,KAAO,SAAUP,GACjCnP,KAAK6wB,KAAK,UAAW1hB,EACvB,EAEAxE,EAAQlG,UAAUkL,QAAU,SAAUR,GACpCnP,KAAK6wB,KAAK,UAAW1hB,EACvB,EAEAxE,EAAQlG,UAAUnE,MAAQ,SAAU6O,GAClCnP,KAAK6wB,KAAK,QAAS1hB,EACrB,EAEAxE,EAAQlG,UAAUmL,SAAW,SAAUT,GACrCnP,KAAK6wB,KAAK,WAAY1hB,EACxB,EAEAxE,EAAQlG,UAAU0pB,KAAO,SAAU1mB,GACjCzH,KAAK8N,MAAMqgB,KAAK1mB,EAClB,EAEAkD,EAAQlG,UAAU6N,aAAe,SAAUG,EAAMC,EAAU5B,GACzD,OAAO9Q,KAAK4L,WAAa5L,KAAK4L,UAAU0G,aAAaG,EAAMC,EAAU5B,EACvE,EAEAnG,EAAQlG,UAAUkO,wBAA0B,SAAUC,GACpD,OAAO5S,KAAK4L,WAAa5L,KAAK4L,UAAU+G,wBAAwBC,EAClE,EAEAjI,EAAQlG,UAAUoO,YAAc,SAAUD,GACxC,OAAO5S,KAAK4L,WAAa5L,KAAK4L,UAAUiH,YAAYD,EACtD,EAEAjI,EAAQlG,UAAUsD,iBAAmB,SAAUoH,GAC7C,OAAOnP,KAAKkM,IAAInE,iBAAiBoH,EACnC,EAEAxE,EAAQlG,UAAUoL,gBAAkB,SAAUxH,GAC5CrI,KAAKkM,IAAI9D,gBAAgBC,EAC3B,EAIAsC,EAAQlG,UAAUosB,KAAO,SAAUC,EAAc3hB,GAC/C,IAAI1H,EAKJ,GAJI0H,EAAK1H,WACPA,EAAW0H,EAAK1H,gBACT0H,EAAK1H,UAEVzH,KAAK6G,QAAQ4M,uBAAyBzT,KAAK+wB,iBAAiB5hB,IAC9D,GAAI1H,EAAU,CACZ,IAAInH,EAAQ,IAAIO,MAAM,0BACtBP,EAAM6O,KAAOA,EACb1H,EAASnH,EACX,OAGF,IACEN,KAAKgxB,gBAAgB7hB,GACrBA,EAAK2B,MAAQ3B,EAAK2B,OAASggB,EAC3B9wB,KAAK4L,WAAa5L,KAAK4L,UAAUqlB,oBAAoB9hB,GACrDA,EAAK+hB,gBACFlxB,KAAK4L,WAAa5L,KAAK4L,UAAUulB,cAAiB,GACrDnxB,KAAK0M,SAASwC,IAAIC,EAAM1H,EAC1B,CAAE,MAAOpF,GACHoF,GACFA,EAASpF,GAEXrC,KAAK+K,OAAOzK,MAAM+B,EACpB,CACF,EAEAsI,EAAQlG,UAAUmsB,iBAAmB,WACnC,OAAO5wB,KAAK6G,QAAQoM,UAAY,OAClC,EAEAtI,EAAQlG,UAAUssB,iBAAmB,SAAU5hB,GAC7C,IAAKA,EAAK6B,YACR,OAAO,EAET,IAAIogB,EA8CN,SAA0BjiB,GACxB,IAAI7M,EAAU6M,EAAK7M,SAAW,GAC1B7B,GAAS0O,EAAKoU,KAAO,CAAC,GAAG9iB,OAASyF,OAAOiJ,EAAKoU,KAClD,OAAOjhB,EAAU,KAAO7B,CAC1B,CAlDiB4wB,CAAiBliB,GAChC,OAAInP,KAAKwwB,gBAAkBY,IAG3BpxB,KAAKiP,UAAYE,EAAKoU,IACtBvjB,KAAKwwB,cAAgBY,GACd,EACT,EAEAzmB,EAAQlG,UAAUusB,gBAAkB,SAAU7hB,GAG5C,GAAInP,KAAKqwB,OAAQ,CAEf,IAAIiB,EAAOtxB,KAAKqwB,OAAOK,QAAQC,SAE/B,GAwEJ,SAAsBW,GACpB,IAAKA,IAASA,EAAK1oB,SAAmC,mBAAjB0oB,EAAK1oB,QACxC,OAAO,EAGT,IAAI2oB,EAAcD,EAAK1oB,UAEvB,IACG2oB,IACAA,EAAYC,WACZD,EAAYE,WACmB,mBAAzBF,EAAYC,UACc,mBAA1BD,EAAYE,UAEnB,OAAO,EAGT,OAAO,CACT,CA1FQC,CAAaJ,GAAO,CACtBA,EAAKK,OAAO,qBAAsBxiB,EAAKG,MACvCgiB,EAAKK,OAAO,qBAAqB,GACjCL,EAAKK,OAAO,SAAS,GACrBL,EAAKK,OACH,mBACA,uCAAuCxiB,EAAKG,QAE9CgiB,EAAKK,OACH,yBACA,6CAA6CxiB,EAAKG,QAIpD,IAAIsiB,EAAoBN,EAAK1oB,UAAU4oB,WACnCK,EAAqBP,EAAK1oB,UAAU6oB,YAEpCtiB,EAAKuS,QACPvS,EAAKuS,OAAOoQ,oBAAsBF,EAClCziB,EAAKuS,OAAOqQ,qBAAuBF,GAEnC1iB,EAAKuS,OAAS,CACZoQ,oBAAqBF,EACrBG,qBAAsBF,EAG5B,CACF,CACF,EAgEAppB,EAAOC,QAAUiC,C,6BC1RjB,IAAIxE,EAAI,EAAQ,KACZ6rB,EAAW,EAAQ,KAsDvB,SAASC,EAAUztB,EAAK+B,GACtB,IAAIke,EAAOle,EAAK1G,MAAM,KAClBmpB,EAAOvE,EAAKliB,OAAS,EACzB,IACE,IAAK,IAAIO,EAAI,EAAGA,GAAKkmB,IAAQlmB,EACvBA,EAAIkmB,EACNxkB,EAAMA,EAAIigB,EAAK3hB,IAEf0B,EAAIigB,EAAK3hB,IAAMqD,EAAE+rB,QAGvB,CAAE,MAAO7vB,GAET,CACF,CAsBAoG,EAAOC,QAxFP,SAAelB,EAAMwL,EAAakT,GAGhC,GAFAlT,EAAcA,GAAe,GAEzBkT,EACF,IAAK,IAAIpjB,EAAI,EAAGA,EAAIojB,EAAW3jB,SAAUO,EACvCmvB,EAAUzqB,EAAM0e,EAAWpjB,IAI/B,IAAIqvB,EA2DN,SAA8Bnf,GAG5B,IAFA,IACIof,EADAtb,EAAM,GAEDhU,EAAI,EAAGA,EAAIkQ,EAAYzQ,SAAUO,EACxCsvB,EAAM,iBAAmBpf,EAAYlQ,GAAK,8BAC1CgU,EAAI9T,KAAK,IAAIoW,OAAOgZ,EAAK,MAE3B,OAAOtb,CACT,CAnEiBub,CAAqBrf,GAChCsf,EAoEN,SAAmCtf,GAGjC,IAFA,IACIof,EADAtb,EAAM,GAEDhU,EAAI,EAAGA,EAAIkQ,EAAYzQ,SAAUO,EACxCsvB,EAAM,gBAAkBpf,EAAYlQ,GAAK,6BACzCgU,EAAI9T,KAAK,IAAIoW,OAAO,IAAMgZ,EAAM,eAAgB,QAElD,OAAOtb,CACT,CA5EiByb,CAA0Bvf,GAEzC,SAASwf,EAAiBC,EAAQC,GAChC,OAAOA,EAAYvsB,EAAE+rB,QACvB,CAmCA,OAAOF,EAASxqB,GAZhB,SAASmrB,EAAS1sB,EAAGrB,EAAGguB,GACtB,IAAIC,EAZN,SAAqB5sB,EAAGrB,GACtB,IAAI9B,EACJ,IAAKA,EAAI,EAAGA,EAAIqvB,EAAS5vB,SAAUO,EACjC,GAAIqvB,EAASrvB,GAAGkH,KAAK/D,GAAI,CACvBrB,EAAIuB,EAAE+rB,SACN,KACF,CAEF,OAAOttB,CACT,CAGakuB,CAAY7sB,EAAGrB,GAC1B,OAAIiuB,IAASjuB,EACPuB,EAAEwC,OAAO/D,EAAG,WAAauB,EAAEwC,OAAO/D,EAAG,SAChCotB,EAASptB,EAAG+tB,EAAUC,GAzBnC,SAAuBhuB,GACrB,IAAI9B,EACJ,GAAIqD,EAAEwC,OAAO/D,EAAG,UACd,IAAK9B,EAAI,EAAGA,EAAIwvB,EAAS/vB,SAAUO,EACjC8B,EAAIA,EAAEzD,QAAQmxB,EAASxvB,GAAI0vB,GAG/B,OAAO5tB,CACT,CAmBWmuB,CAAcF,GAEdA,CAEX,GAGF,C,6BCrDA,IAAI1sB,EAAI,EAAQ,KAEZ6sB,EAAa,IAEjB,SAAStnB,EAAU7E,GACjB7G,KAAK8N,MAAQ,GACb9N,KAAK6G,QAAUV,EAAEqC,MAAM3B,GACvB,IAAIosB,EAAqBjzB,KAAK6G,QAAQosB,oBAAsBD,EAC5DhzB,KAAKkzB,aAAe9P,KAAK+P,IAAI,EAAG/P,KAAKgQ,IAAIH,EAAoBD,GAC/D,CA4NA,SAASK,EAAS5gB,EAAM3B,GACtB,GAAIA,EACF,OAAOA,EAMT,MAJmB,CACjBxQ,MAAO,QACPgzB,OAAQ,QAEU7gB,IAAS,MAC/B,CAnOA/G,EAAUjH,UAAU6D,UAAY,SAAUzB,GACxC,IAAI0B,EAAavI,KAAK6G,QACtB7G,KAAK6G,QAAUV,EAAEqC,MAAMD,EAAY1B,GACnC,IAAIosB,EAAqBjzB,KAAK6G,QAAQosB,oBAAsBD,EACxDO,EAAenQ,KAAK+P,IAAI,EAAG/P,KAAKgQ,IAAIH,EAAoBD,IACxDQ,EAAc,EACdxzB,KAAK8N,MAAMvL,OAASgxB,IACtBC,EAAcxzB,KAAK8N,MAAMvL,OAASgxB,GAEpCvzB,KAAKkzB,aAAeK,EACpBvzB,KAAK8N,MAAM6f,OAAO,EAAG6F,EACvB,EAEA9nB,EAAUjH,UAAU0sB,WAAa,WAC/B,IAAIsC,EAAS7c,MAAMnS,UAAU9C,MAAMoD,KAAK/E,KAAK8N,MAAO,GACpD,GAAI3H,EAAE0I,WAAW7O,KAAK6G,QAAQ6sB,iBAC5B,IAEE,IADA,IAAI5wB,EAAI2wB,EAAOlxB,OACRO,KACD9C,KAAK6G,QAAQ6sB,gBAAgBD,EAAO3wB,KACtC2wB,EAAO9F,OAAO7qB,EAAG,EAGvB,CAAE,MAAOT,GACPrC,KAAK6G,QAAQ6sB,gBAAkB,IACjC,CAEF,OAAOD,CACT,EAEA/nB,EAAUjH,UAAU6c,QAAU,SAC5B7O,EACAC,EACA5B,EACA6M,EACAoH,GAEA,IAAI1iB,EAAI,CACNyO,MAAOuiB,EAAS5gB,EAAM3B,GACtB2B,KAAMA,EACNkhB,aAAc5O,GAAa5e,EAAEoV,MAC7BiB,KAAM9J,EACNxQ,OAAQ,UAENyb,IACFtb,EAAEiN,KAAOqO,GAGX,IACE,GACExX,EAAE0I,WAAW7O,KAAK6G,QAAQ6sB,kBAC1B1zB,KAAK6G,QAAQ6sB,gBAAgBrxB,GAE7B,OAAO,CAEX,CAAE,MAAO6P,GACPlS,KAAK6G,QAAQ6sB,gBAAkB,IACjC,CAGA,OADA1zB,KAAKgD,KAAKX,GACHA,CACT,EAEAqJ,EAAUjH,UAAU6N,aAAe,SACjCG,EACAC,EACA5B,EACA6M,GAEA,OAAO3d,KAAKshB,QAAQ7O,EAAMC,EAAU5B,EAAO6M,EAC7C,EAEAjS,EAAUjH,UAAUmvB,aAAe,SACjCrQ,EACAzS,EACA6M,EACAoH,GAEA,IAAIrS,EAAW,CACbpQ,QAASihB,EAAIjhB,SAAW4D,OAAOqd,IAKjC,OAHIA,EAAI9iB,QACNiS,EAASjS,MAAQ8iB,EAAI9iB,OAEhBT,KAAKshB,QAAQ,QAAS5O,EAAU5B,EAAO6M,EAAaoH,EAC7D,EAEArZ,EAAUjH,UAAUia,WAAa,SAC/Bpc,EACAwO,EACA6M,EACAoH,GAEA,OAAO/kB,KAAKshB,QACV,MACA,CACEhf,QAASA,GAEXwO,EACA6M,EACAoH,EAEJ,EAEArZ,EAAUjH,UAAUsX,eAAiB,SACnCrJ,EACAgL,EACAC,EACAkW,GAEAnW,EAAUA,GAAW,MACrBhL,EAASgL,QAAUhL,EAASgL,SAAWA,EACnCmW,IACFnhB,EAASmJ,QAAUgY,GAErB,IAAI/iB,EAAQ9Q,KAAK+c,gBAAgBrK,EAAS2I,aAC1C,OAAOrb,KAAKshB,QAAQ,UAAW5O,EAAU5B,EAAO6M,EAClD,EAEAjS,EAAUjH,UAAUsY,gBAAkB,SAAU+W,GAC9C,OAAIA,GAAc,KAAOA,EAAa,IAC7B,OAEU,IAAfA,GAAoBA,GAAc,IAC7B,QAEF,MACT,EAEApoB,EAAUjH,UAAUob,WAAa,SAC/BnC,EACAtH,EACAjO,EACAmX,EACA3B,GAEA,IAAIjL,EAAW,CACbgL,QAASA,EACTtH,QAASA,GAQX,YANchV,IAAV+G,IACFuK,EAASvK,MAAQA,QAEH/G,IAAZke,IACF5M,EAAS4M,QAAUA,GAEdtf,KAAKshB,QAAQ,MAAO5O,EAAU,OAAQiL,EAC/C,EAEAjS,EAAUjH,UAAUgc,kBAAoB,SAAUN,EAAMC,EAAIzC,GAC1D,OAAO3d,KAAKshB,QACV,aACA,CAAEnB,KAAMA,EAAMC,GAAIA,GAClB,OACAzC,EAEJ,EAEAjS,EAAUjH,UAAUkO,wBAA0B,SAAUC,GACtD,OAAO5S,KAAKshB,QACV,aACA,CAAE5D,QAAS,oBACX,YACAtc,EACAwR,GAAMA,EAAGlI,UAMb,EACAgB,EAAUjH,UAAUoO,YAAc,SAAUD,GAC1C,OAAO5S,KAAKshB,QACV,aACA,CAAE5D,QAAS,QACX,YACAtc,EACAwR,GAAMA,EAAGlI,UAMb,EAEAgB,EAAUjH,UAAUic,0BAA4B,SAAUjO,EAAMkL,GAC9D,OAAO3d,KAAK+b,eAAe,CAAEgY,OAAQthB,GAAQ,eAAgBkL,EAC/D,EAGAjS,EAAUjH,UAAUwsB,oBAAsB,SAAU9hB,GAClD,GAAKnP,KAAK6G,QAAQ0M,wBAGlB,OAAIpE,EAAKoU,IACAvjB,KAAK4zB,aAAazkB,EAAKoU,IAAKpU,EAAK2B,MAAO3B,EAAKG,KAAMH,EAAK4V,WAE7D5V,EAAK7M,QACAtC,KAAK0e,WAAWvP,EAAK7M,QAAS6M,EAAK2B,MAAO3B,EAAKG,KAAMH,EAAK4V,WAE/D5V,EAAKuS,OACA1hB,KAAKshB,QACV,MACAnS,EAAKuS,OACLvS,EAAK2B,MACL3B,EAAKG,KACLH,EAAK4V,gBANT,CASF,EAEArZ,EAAUjH,UAAUzB,KAAO,SAAUX,GACnCrC,KAAK8N,MAAM9K,KAAKX,GACZrC,KAAK8N,MAAMvL,OAASvC,KAAKkzB,cAC3BlzB,KAAK8N,MAAM3K,OAEf,EAaAsF,EAAOC,QAAUgD,C,6BChPjB,IAAIvF,EAAI,EAAQ,KAqGhB,SAAS6tB,EAAkBntB,EAASyS,GAC9BnT,EAAE0I,WAAWhI,EAAQyS,MACvBzS,EAAQyS,GAAQzS,EAAQyS,GAAMxU,WAElC,CAoDA2D,EAAOC,QAAU,CACfkF,cA5JF,SAAuBuB,EAAMtI,EAASY,GACpC,IAAID,EAAO2H,EAAK3H,KAEZ2H,EAAK6B,cACPxJ,EAAKwJ,aAAc,GAEjB7B,EAAKwC,gBACPnK,EAAKmK,cAAgBxC,EAAKwC,eAE5BlK,EAAS,KAAMD,EACjB,EAmJEgG,kBAjJF,SAA2B2B,EAAMtI,EAASY,GACxC,IAAIwsB,EAAiBptB,EAAQa,SAAW,CAAC,EACrCusB,EAAezX,aACVyX,EAAezX,KAGxBrN,EAAK3H,KAAOrB,EAAEqC,MAAM2G,EAAK3H,KAAMysB,GAC/BxsB,EAAS,KAAM0H,EACjB,EA0IE9B,iBAxIF,SAA0B8B,EAAMtI,EAASY,GACnC0H,EAAK+hB,iBACP/qB,EAAEyb,IAAIzS,EAAM,sBAAuBA,EAAK+hB,iBAE1CzpB,EAAS,KAAM0H,EACjB,EAoIE/B,oBAlIF,SAA6B+B,EAAMtI,EAASY,GAC1C,GAAK0H,EAAK7M,QAAV,CAIA,IAAI4xB,EAAY,0BACZjS,EAAQ9b,EAAEsR,IAAItI,EAAM+kB,GAKxB,GAJKjS,IACHiS,EAAY,kBACZjS,EAAQ9b,EAAEsR,IAAItI,EAAM+kB,IAElBjS,EAAO,CACT,IAAMA,EAAMC,YAAaD,EAAMC,UAAU7I,YAGvC,OAFAlT,EAAEyb,IAAIzS,EAAM+kB,EAAY,yBAA0B/kB,EAAK7M,cACvDmF,EAAS,KAAM0H,GAGjB,IAAIwS,EAAQxb,EAAEsR,IAAItI,EAAM+kB,EAAY,WAAa,CAAC,EAC9CC,EAAWhuB,EAAEqC,MAAMmZ,EAAO,CAAErf,QAAS6M,EAAK7M,UAC9C6D,EAAEyb,IAAIzS,EAAM+kB,EAAY,SAAUC,EACpC,CACA1sB,EAAS,KAAM0H,EAjBf,MAFE1H,EAAS,KAAM0H,EAoBnB,EA6GE1B,cA3GF,SAAuB1C,GACrB,OAAO,SAAUoE,EAAMtI,EAASY,GAC9B,IAAI2sB,EAAUjuB,EAAEqC,MAAM2G,GAClBuN,EAAW,KACf,IACMvW,EAAE0I,WAAWhI,EAAQykB,aACvB5O,EAAW7V,EAAQykB,UAAU8I,EAAQ5sB,KAAM2H,GAE/C,CAAE,MAAO9M,GAOP,OANAwE,EAAQykB,UAAY,KACpBvgB,EAAOzK,MACL,gFACA+B,QAEFoF,EAAS,KAAM0H,EAEjB,CACIhJ,EAAEkuB,UAAU3X,GACdA,EAASY,MACP,SAAUgX,GACJA,IACFF,EAAQ5sB,KAAO8sB,GAEjB7sB,EAAS,KAAM2sB,EACjB,IACA,SAAU9zB,GACRmH,EAASnH,EAAO6O,EAClB,IAGF1H,EAAS,KAAM2sB,EAEnB,CACF,EA2EE9mB,mBAzEF,SAA4B6B,EAAMtI,EAASY,GACzC,IAAKZ,EAAQyM,WACX,OAAO7L,EAAS,KAAM0H,GAExB,IACIuS,EAASvb,EAAEsR,IAAItI,EAAM,gBAAkB,CAAC,EAC5CuS,EAAgB,eAAI7a,EACpBsI,EAAK3H,KAAKka,OAASA,EACnBja,EAAS,KAAM0H,EACjB,EAiEEzB,qBAzDF,SAA8ByB,EAAMtI,EAASY,GAC3C,IAAI8sB,EAAoB1tB,EAAQ4E,mBAGhCuoB,EAAkBO,EAAmB,aACrCP,EAAkBO,EAAmB,eACrCP,EAAkBO,EAAmB,yBAE9BA,EAAkBptB,YACzBgI,EAAK3H,KAAKkF,SAAS8nB,mBAAqBD,EACxC9sB,EAAS,KAAM0H,EACjB,EA+CExB,kBA7CF,SAA2BwB,EAAMtI,EAASY,GACxC,IAAIgS,EAAatT,EAAEqC,MACjB2G,EAAKzC,SAASnB,OAAOmB,SAAS+M,WAC9BtK,EAAKsK,YAWP,GARItT,EAAEsR,IAAItI,EAAM,sBACdsK,EAAWgb,cAAe,GAGxBtlB,EAAK6B,cACPyI,EAAWib,YAAcvlB,EAAK6B,aAG5B7B,EAAKoU,IACP,IACE9J,EAAWkb,UAAY,CACrBryB,QAAS6M,EAAKoU,IAAIjhB,QAClBgX,KAAMnK,EAAKoU,IAAIjK,KACfsb,iBAAkBzlB,EAAKoU,IAAIkH,aAAetb,EAAKoU,IAAIkH,YAAYnR,KAC/DwJ,SAAU3T,EAAKoU,IAAIxhB,SACnBT,KAAM6N,EAAKoU,IAAIvhB,WACfihB,OAAQ9T,EAAKoU,IAAIthB,aACjBxB,MAAO0O,EAAKoU,IAAI9iB,MAEpB,CAAE,MAAO4B,GACPoX,EAAWkb,UAAY,CAAEE,OAAQ3uB,OAAO7D,GAC1C,CAGF8M,EAAK3H,KAAKkF,SAAS+M,WAAatT,EAAEqC,MAChC2G,EAAK3H,KAAKkF,SAAS+M,WACnBA,GAEFhS,EAAS,KAAM0H,EACjB,E,6BC3JA,IAAIhJ,EAAI,EAAQ,KACZ6rB,EAAW,EAAQ,KAEvB,SAASrP,EAAIjb,EAAST,GACpB,MAAO,CAACS,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAAS6tB,EAAajS,EAAQkS,GAC5B,IAAIhyB,EAAM8f,EAAOtgB,OACjB,OAAIQ,EAAc,EAARgyB,EACDlS,EAAOlhB,MAAM,EAAGozB,GAAOxwB,OAAOse,EAAOlhB,MAAMoB,EAAMgyB,IAEnDlS,CACT,CAEA,SAASmS,EAAettB,EAAST,EAAY8tB,GAC3CA,OAAyB,IAAVA,EAAwB,GAAKA,EAC5C,IACIlS,EADArG,EAAO9U,EAAQF,KAAKgV,KAExB,GAAIA,EAAKsJ,YAEP,IADA,IAAIjC,EAAQrH,EAAKsJ,YACRhjB,EAAI,EAAGA,EAAI+gB,EAAMthB,OAAQO,IAEhC+f,EAASiS,EADTjS,EAASgB,EAAM/gB,GAAG+f,OACYkS,GAC9BlR,EAAM/gB,GAAG+f,OAASA,OAEXrG,EAAKyF,QAEdY,EAASiS,EADTjS,EAASrG,EAAKyF,MAAMY,OACUkS,GAC9BvY,EAAKyF,MAAMY,OAASA,GAEtB,MAAO,CAACnb,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASguB,EAAmBlyB,EAAKmyB,GAC/B,OAAKA,GAGDA,EAAI3yB,OAASQ,EACRmyB,EAAIvzB,MAAM,EAAGoB,EAAM,GAAGwB,OAAO,OAH7B2wB,CAMX,CAEA,SAASC,EAAgBpyB,EAAK2E,EAAST,GAarC,OADAS,EAAUsqB,EAAStqB,GAXnB,SAAS0tB,EAAUnvB,EAAGrB,EAAGguB,GACvB,OAAQzsB,EAAEkvB,SAASzwB,IACjB,IAAK,SACH,OAAOqwB,EAAmBlyB,EAAK6B,GACjC,IAAK,SACL,IAAK,QACH,OAAOotB,EAASptB,EAAGwwB,EAAWxC,GAChC,QACE,OAAOhuB,EAEb,IAEO,CAAC8C,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASquB,EAAkBC,GASzB,OARIA,EAAUrT,mBACLqT,EAAUrT,UAAU7I,YAC3Bkc,EAAUrT,UAAU5f,QAAU2yB,EAC5B,IACAM,EAAUrT,UAAU5f,UAGxBizB,EAAU1S,OAASiS,EAAaS,EAAU1S,OAAQ,GAC3C0S,CACT,CAEA,SAASC,EAAQ9tB,EAAST,GACxB,IAAIuV,EAAO9U,EAAQF,KAAKgV,KACxB,GAAIA,EAAKsJ,YAEP,IADA,IAAIjC,EAAQrH,EAAKsJ,YACRhjB,EAAI,EAAGA,EAAI+gB,EAAMthB,OAAQO,IAChC+gB,EAAM/gB,GAAKwyB,EAAkBzR,EAAM/gB,SAE5B0Z,EAAKyF,QACdzF,EAAKyF,MAAQqT,EAAkB9Y,EAAKyF,QAEtC,MAAO,CAACva,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASwuB,EAAgB/tB,EAASguB,GAChC,OAAOvvB,EAAEwvB,YAAYjuB,GAAWguB,CAClC,CAyBAjtB,EAAOC,QAAU,CACfT,SAxBF,SAAkBP,EAAST,EAAYyuB,GACrCA,OAA6B,IAAZA,EAA0B,OAAaA,EAWxD,IAVA,IAQIE,EAAUC,EAAShzB,EARnBizB,EAAa,CACfnT,EACAqS,EACAG,EAAgBpW,KAAK,KAAM,MAC3BoW,EAAgBpW,KAAK,KAAM,KAC3BoW,EAAgBpW,KAAK,KAAM,KAC3ByW,GAIMI,EAAWE,EAAW3yB,SAI5B,GAFAuE,GADAmuB,EAAUD,EAASluB,EAAST,IACV,IAClBpE,EAASgzB,EAAQ,IACNv1B,QAAUm1B,EAAgB5yB,EAAOsF,MAAOutB,GACjD,OAAO7yB,EAGX,OAAOA,CACT,EAME8f,IAAKA,EACLqS,eAAgBA,EAChBG,gBAAiBA,EACjBF,mBAAoBA,E,6BCvHtB,IAAIzsB,EAAQ,EAAQ,KAEhButB,EAAc,CAAC,EAgDnB,SAASptB,EAAOqtB,EAAG7Y,GACjB,OAAOA,IAAMkY,EAASW,EACxB,CAKA,SAASX,EAASW,GAChB,IAAI1c,SAAc0c,EAClB,MAAa,WAAT1c,EACKA,EAEJ0c,EAGDA,aAAan1B,MACR,QAEF,CAAC,EAAEiE,SACPC,KAAKixB,GACLt1B,MAAM,iBAAiB,GACvB0T,cARM,MASX,CAOA,SAASvF,EAAWgD,GAClB,OAAOlJ,EAAOkJ,EAAG,WACnB,CAOA,SAASokB,EAAiBpkB,GACxB,IACIqkB,EAAkBvX,SAASla,UAAUK,SACtCC,KAAKF,OAAOJ,UAAU4N,gBACtBlR,QAHgB,sBAGM,QACtBA,QAAQ,yDAA0D,SACjEg1B,EAAa/c,OAAO,IAAM8c,EAAkB,KAChD,OAAOE,EAASvkB,IAAMskB,EAAWnsB,KAAK6H,EACxC,CAOA,SAASukB,EAASjuB,GAChB,IAAIsK,SAActK,EAClB,OAAgB,MAATA,IAA0B,UAARsK,GAA4B,YAARA,EAC/C,CAoEA,SAAS4jB,IACP,IAAIC,EAAI/a,IASR,MARW,uCAAuCpa,QAChD,SACA,SAAUod,GACR,IAAIrN,GAAKolB,EAAoB,GAAhBlT,KAAKmT,UAAiB,GAAK,EAExC,OADAD,EAAIlT,KAAKC,MAAMiT,EAAI,KACL,MAAN/X,EAAYrN,EAAS,EAAJA,EAAW,GAAKpM,SAAS,GACpD,GAGJ,CAyBA,IAAI0xB,EAAkB,CACpBC,YAAY,EACZ7hB,IAAK,CACH,SACA,WACA,YACA,WACA,OACA,WACA,OACA,OACA,WACA,OACA,YACA,OACA,QACA,UAEF8hB,EAAG,CACDpd,KAAM,WACNqd,OAAQ,6BAEVA,OAAQ,CACNC,OACE,0IACFC,MACE,qMAiFN,SAAS3uB,EAAU1D,EAAKsyB,GACtB,IAAI3uB,EAAO7H,EACX,IACE6H,EAAQ4tB,EAAY7tB,UAAU1D,EAChC,CAAE,MAAOuyB,GACP,GAAID,GAAUjoB,EAAWioB,GACvB,IACE3uB,EAAQ2uB,EAAOtyB,EACjB,CAAE,MAAOwyB,GACP12B,EAAQ02B,CACV,MAEA12B,EAAQy2B,CAEZ,CACA,MAAO,CAAEz2B,MAAOA,EAAO6H,MAAOA,EAChC,CA8EA,SAAS8uB,EAAalsB,EAAQ8G,GAC5B,OAAO,SAAU0R,EAAKhG,GACpB,IACE1L,EAAE0R,EAAKhG,EACT,CAAE,MAAOlb,GACP0I,EAAOzK,MAAM+B,EACf,CACF,CACF,CAEA,SAAS60B,EAAiB1yB,GA+BxB,OA5BA,SAASiZ,EAAMjZ,EAAKouB,GAClB,IAAIzqB,EACFmR,EACA6d,EACAt0B,EAAS,CAAC,EAEZ,IACE,IAAKyW,KAAQ9U,GACX2D,EAAQ3D,EAAI8U,MAEE3Q,EAAOR,EAAO,WAAaQ,EAAOR,EAAO,UACjDyqB,EAAK/U,SAAS1V,GAChBtF,EAAOyW,GAAQ,+BAAiC+b,EAASltB,KAEzDgvB,EAAUvE,EAAKjxB,SACPqB,KAAKmF,GACbtF,EAAOyW,GAAQmE,EAAMtV,EAAOgvB,IAKhCt0B,EAAOyW,GAAQnR,CAEnB,CAAE,MAAO9F,GACPQ,EAAS,+BAAiCR,EAAEC,OAC9C,CACA,OAAOO,CACT,CACO4a,CAAMjZ,EA9BF,CAACA,GA+Bd,CAiIA,IAAI4yB,EAAkB,CACpB,MACA,UACA,MACA,aACA,QACA,UAEEC,EAAmB,CAAC,WAAY,QAAS,UAAW,OAAQ,SAEhE,SAASC,EAAchb,EAAK4Y,GAC1B,IAAK,IAAIjvB,EAAI,EAAGA,EAAIqW,EAAI/Z,SAAU0D,EAChC,GAAIqW,EAAIrW,KAAOivB,EACb,OAAO,EAIX,OAAO,CACT,CAiHA,SAAS3Z,IACP,OAAI9Q,KAAK8Q,KACC9Q,KAAK8Q,OAEP,IAAI9Q,IACd,CAgEAhC,EAAOC,QAAU,CACf6d,8BAxgBF,SAAuCpf,EAAaN,EAASwf,IAC3DA,EAASA,GAAU,CAAC,GACbtd,aAAe5B,EACtB,IACIlB,EADAsxB,EAAc,GAElB,IAAKtxB,KAAKogB,EACJxhB,OAAOJ,UAAU4N,eAAetN,KAAKshB,EAAQpgB,IAC/CsxB,EAAYv0B,KAAK,CAACiD,EAAGogB,EAAOpgB,IAAInE,KAAK,MAGzC,IAAIonB,EAAQ,IAAMqO,EAAYC,OAAO11B,KAAK,MAE1C+E,EAAUA,GAAW,CAAC,GACdN,KAAOM,EAAQN,MAAQ,GAC/B,IAEIvC,EAFAyzB,EAAK5wB,EAAQN,KAAKvF,QAAQ,KAC1B02B,EAAI7wB,EAAQN,KAAKvF,QAAQ,MAEjB,IAARy2B,KAAqB,IAAPC,GAAYA,EAAID,IAChCzzB,EAAI6C,EAAQN,KACZM,EAAQN,KAAOvC,EAAEF,UAAU,EAAG2zB,GAAMvO,EAAQ,IAAMllB,EAAEF,UAAU2zB,EAAK,KAExD,IAAPC,GACF1zB,EAAI6C,EAAQN,KACZM,EAAQN,KAAOvC,EAAEF,UAAU,EAAG4zB,GAAKxO,EAAQllB,EAAEF,UAAU4zB,IAEvD7wB,EAAQN,KAAOM,EAAQN,KAAO2iB,CAGpC,EA6eEnW,WAzUF,SAAoB3P,EAAM2H,EAAQ2B,EAAUirB,EAAaC,GAOvD,IANA,IAAIt1B,EAASihB,EAAK7B,EAAQja,EAAUoU,EAChCgc,EACAC,EAAY,GAEZC,EAAW,GAENj1B,EAAI,EAAG4iB,EAAItiB,EAAKb,OAAQO,EAAI4iB,IAAK5iB,EAAG,CAG3C,IAAIk1B,EAAM3C,EAFVwC,EAAMz0B,EAAKN,IAIX,OADAi1B,EAAS/0B,KAAKg1B,GACNA,GACN,IAAK,YACH,MACF,IAAK,SACH11B,EAAUw1B,EAAU90B,KAAK60B,GAAQv1B,EAAUu1B,EAC3C,MACF,IAAK,WACHpwB,EAAWwvB,EAAalsB,EAAQ8sB,GAChC,MACF,IAAK,OACHC,EAAU90B,KAAK60B,GACf,MACF,IAAK,QACL,IAAK,eACL,IAAK,YACHtU,EAAMuU,EAAU90B,KAAK60B,GAAQtU,EAAMsU,EACnC,MACF,IAAK,SACL,IAAK,QACH,GACEA,aAAeh3B,OACU,oBAAjBo3B,cAAgCJ,aAAeI,aACvD,CACA1U,EAAMuU,EAAU90B,KAAK60B,GAAQtU,EAAMsU,EACnC,KACF,CACA,GAAIF,GAAuB,WAARK,IAAqBnc,EAAS,CAC/C,IAAK,IAAI9V,EAAI,EAAGhD,EAAM40B,EAAYp1B,OAAQwD,EAAIhD,IAAOgD,EACnD,QAA4B3E,IAAxBy2B,EAAIF,EAAY5xB,IAAmB,CACrC8V,EAAUgc,EACV,KACF,CAEF,GAAIhc,EACF,KAEJ,CACA6F,EAASoW,EAAU90B,KAAK60B,GAAQnW,EAASmW,EACzC,MACF,QACE,GACEA,aAAeh3B,OACU,oBAAjBo3B,cAAgCJ,aAAeI,aACvD,CACA1U,EAAMuU,EAAU90B,KAAK60B,GAAQtU,EAAMsU,EACnC,KACF,CACAC,EAAU90B,KAAK60B,GAErB,CAGInW,IAAQA,EAASwV,EAAiBxV,IAElCoW,EAAUv1B,OAAS,IAChBmf,IAAQA,EAASwV,EAAiB,CAAC,IACxCxV,EAAOoW,UAAYZ,EAAiBY,IAGtC,IAAI3oB,EAAO,CACT7M,QAASA,EACTihB,IAAKA,EACL7B,OAAQA,EACRqD,UAAWxJ,IACX9T,SAAUA,EACViF,SAAUA,EACV+M,WA1Ee,CAAC,EA2EhBnK,KAAM+mB,KAaR,OAGF,SAA2BlnB,EAAMuS,GAC3BA,QAA2BtgB,IAAjBsgB,EAAO5Q,QACnB3B,EAAK2B,MAAQ4Q,EAAO5Q,aACb4Q,EAAO5Q,OAEZ4Q,QAAgCtgB,IAAtBsgB,EAAOpD,aACnBnP,EAAKmP,WAAaoD,EAAOpD,kBAClBoD,EAAOpD,WAElB,CAtBE4Z,CAAkB/oB,EAAMuS,GAEpBiW,GAAe9b,IACjB1M,EAAK0M,QAAUA,GAEb+b,IACFzoB,EAAKyoB,cAAgBA,GAEvBzoB,EAAKwC,cAAgBvO,EACrB+L,EAAKsK,WAAW0e,mBAAqBJ,EAC9B5oB,CACT,EA6OEyU,gBAhOF,SAAyBzU,EAAMipB,GAC7B,IAAI1W,EAASvS,EAAK3H,KAAKka,QAAU,CAAC,EAC9B2W,GAAe,EAEnB,IACE,IAAK,IAAIv1B,EAAI,EAAGA,EAAIs1B,EAAO71B,SAAUO,EAC/Bs1B,EAAOt1B,GAAGuP,eAAe,oBAC3BqP,EAASlZ,EAAMkZ,EAAQwV,EAAiBkB,EAAOt1B,GAAGw1B,iBAClDD,GAAe,GAKfA,IACFlpB,EAAK3H,KAAKka,OAASA,EAEvB,CAAE,MAAOrf,GACP8M,EAAKsK,WAAW8e,cAAgB,WAAal2B,EAAEC,OACjD,CACF,EA8MEkQ,qBAxLF,SAA8BpP,GAI5B,IAHA,IAAIqP,EAAMC,EAAU5B,EAChB+mB,EAEK/0B,EAAI,EAAG4iB,EAAItiB,EAAKb,OAAQO,EAAI4iB,IAAK5iB,EAAG,CAI3C,OADUuyB,EAFVwC,EAAMz0B,EAAKN,KAIT,IAAK,UACE2P,GAAQ6kB,EAAcF,EAAiBS,GAC1CplB,EAAOolB,GACG/mB,GAASwmB,EAAcD,EAAkBQ,KACnD/mB,EAAQ+mB,GAEV,MACF,IAAK,SACHnlB,EAAWmlB,EAKjB,CAOA,MANY,CACVplB,KAAMA,GAAQ,SACdC,SAAUA,GAAY,CAAC,EACvB5B,MAAOA,EAIX,EA2JE0nB,SAnEF,SAAkB3E,EAAargB,GAC7B,GAAKqgB,GAAgBA,EAAqB,UAAmB,IAAdrgB,EAA/C,CAGA,IAAIilB,EAAQ5E,EAAqB,QACjC,GAAKrgB,EAGH,IACE,IAAIvS,EACJ,IAA4B,IAAxBw3B,EAAMz3B,QAAQ,MAChBC,EAAQw3B,EAAM54B,MAAM,MACdC,MACNmB,EAAM+B,KAAK,KACXy1B,EAAQx3B,EAAMa,KAAK,UACd,IAA4B,IAAxB22B,EAAMz3B,QAAQ,MAEvB,IADAC,EAAQw3B,EAAM54B,MAAM,MACV0C,OAAS,EAAG,CACpB,IAAIm2B,EAAYz3B,EAAMU,MAAM,EAAG,GAC3Bg3B,EAAWD,EAAU,GAAG13B,QAAQ,MAClB,IAAd23B,IACFD,EAAU,GAAKA,EAAU,GAAG50B,UAAU,EAAG60B,IAG3CF,EAAQC,EAAUn0B,OADH,4BACoBzC,KAAK,IAC1C,OAEA22B,EAAQ,IAEZ,CAAE,MAAOp2B,GACPo2B,EAAQ,IACV,MAzBAA,EAAQ,KA2BV5E,EAAqB,QAAI4E,CA9BzB,CA+BF,EAkCElhB,mBAvGF,SAA4BnU,GAC1B,IAAIN,EAAGC,EAAK80B,EACRh1B,EAAS,GACb,IAAKC,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,IAAOD,EAAG,CAE3C,OAAQuyB,EADRwC,EAAMz0B,EAAKN,KAET,IAAK,UAEH+0B,GADAA,EAAM3vB,EAAU2vB,IACNv3B,OAASu3B,EAAI1vB,OACf5F,OAAS,MACfs1B,EAAMA,EAAI/uB,OAAO,EAAG,KAAO,OAE7B,MACF,IAAK,OACH+uB,EAAM,OACN,MACF,IAAK,YACHA,EAAM,YACN,MACF,IAAK,SACHA,EAAMA,EAAI/yB,WAGdjC,EAAOG,KAAK60B,EACd,CACA,OAAOh1B,EAAOf,KAAK,IACrB,EA8EE0kB,UAhfF,SAAmBoS,EAAGlyB,GAWpB,KAVAA,EAAWA,GAAYkyB,EAAElyB,WACRkyB,EAAEjyB,OACF,KAAXiyB,EAAEjyB,KACJD,EAAW,QACS,MAAXkyB,EAAEjyB,OACXD,EAAW,WAGfA,EAAWA,GAAY,UAElBkyB,EAAEtyB,SACL,OAAO,KAET,IAAIzD,EAAS6D,EAAW,KAAOkyB,EAAEtyB,SAOjC,OANIsyB,EAAEjyB,OACJ9D,EAASA,EAAS,IAAM+1B,EAAEjyB,MAExBiyB,EAAEryB,OACJ1D,GAAkB+1B,EAAEryB,MAEf1D,CACT,EA2dE4U,IApJF,SAAajT,EAAK+B,GAChB,GAAK/B,EAAL,CAGA,IAAIigB,EAAOle,EAAK1G,MAAM,KAClBgD,EAAS2B,EACb,IACE,IAAK,IAAI1B,EAAI,EAAGC,EAAM0hB,EAAKliB,OAAQO,EAAIC,IAAOD,EAC5CD,EAASA,EAAO4hB,EAAK3hB,GAEzB,CAAE,MAAOT,GACPQ,OAASzB,CACX,CACA,OAAOyB,CAVP,CAWF,EAuIE2I,cAnCF,SAAuByU,EAASjG,EAAOtS,EAASqD,GAC9C,IAAIlI,EAAS2F,EAAMyX,EAASjG,EAAOtS,GAEnC,OADA7E,EAUF,SAAiCgE,EAASkE,GACpClE,EAAQgyB,gBAAkBhyB,EAAQylB,eACpCzlB,EAAQylB,aAAezlB,EAAQgyB,cAC/BhyB,EAAQgyB,mBAAgBz3B,EACxB2J,GAAUA,EAAOmE,IAAI,mDAEnBrI,EAAQiyB,gBAAkBjyB,EAAQwlB,gBACpCxlB,EAAQwlB,cAAgBxlB,EAAQiyB,cAChCjyB,EAAQiyB,mBAAgB13B,EACxB2J,GAAUA,EAAOmE,IAAI,oDAEvB,OAAOrI,CACT,CAtBWkyB,CAAwBl2B,EAAQkI,IACpCiP,GAASA,EAAMgf,sBAGhBhf,EAAMhH,cACRnQ,EAAOmQ,aAAeiN,EAAQjN,aAAe,IAAIzO,OAAOyV,EAAMhH,cAHvDnQ,CAMX,EA0BE+N,QA7nBF,SAAiBvO,GAEf,OAAOsG,EAAOtG,EAAG,UAAYsG,EAAOtG,EAAG,YACzC,EA2nBEklB,eA9pBF,SAAwBjkB,GACtB,OAAO0C,OAAOvC,SAASH,EACzB,EA6pBEuL,WAAYA,EACZoqB,WA3oBF,SAAoBn2B,GAClB,IAAI2P,EAAO4iB,EAASvyB,GACpB,MAAgB,WAAT2P,GAA8B,UAATA,CAC9B,EAyoBEwjB,iBAAkBA,EAClBG,SAAUA,EACV8C,SA7qBF,SAAkB/wB,GAChB,MAAwB,iBAAVA,GAAsBA,aAAiBjC,MACvD,EA4qBEyC,OAAQA,EACR0rB,UA3nBF,SAAmBrwB,GACjB,OAAOoyB,EAASpyB,IAAM2E,EAAO3E,EAAEsZ,KAAM,WACvC,EA0nBE8K,UApbF,SAAmB+Q,GACjB,IAAIhxB,EAAO7H,EACX,IACE6H,EAAQ4tB,EAAY11B,MAAM84B,EAC5B,CAAE,MAAO92B,GACP/B,EAAQ+B,CACV,CACA,MAAO,CAAE/B,MAAOA,EAAO6H,MAAOA,EAChC,EA6aEukB,OAvmBW,CACXld,MAAO,EACPC,KAAM,EACNE,QAAS,EACTrP,MAAO,EACPsP,SAAU,GAmmBVe,uBA5aF,SACErO,EACA4E,EACAqJ,EACAC,EACAlQ,EACA84B,EACAC,EACA/tB,GAEA,IAAI7J,EAAW,CACbyF,IAAKA,GAAO,GACZ5F,KAAMiP,EACN0S,OAAQzS,GAEV/O,EAASuhB,KAAO1X,EAAYof,kBAAkBjpB,EAASyF,IAAKzF,EAASH,MACrEG,EAASmH,QAAU0C,EAAY2e,cAAcxoB,EAASyF,IAAKzF,EAASH,MACpE,IAAIwY,EACkB,oBAAbzN,UACPA,UACAA,SAAS5K,UACT4K,SAAS5K,SAASqY,KAChBwf,EACgB,oBAAXv5B,QACPA,QACAA,OAAO4kB,WACP5kB,OAAO4kB,UAAUQ,UACnB,MAAO,CACLiU,KAAMA,EACN92B,QAAShC,EAAQ4F,OAAO5F,GAASgC,GAAW+2B,EAC5CnyB,IAAK4S,EACLrZ,MAAO,CAACgB,GACR63B,UAAWA,EAEf,EA2YE9wB,MAAOA,EACP+S,IAAKA,EACL2W,OA7nBF,WACE,MAAO,UACT,EA4nBE6D,YAAaA,EACbhT,YArmBF,SAAqB7b,GACnB,IAAIqyB,EA4CN,SAAkB51B,GAChB,IAAKgF,EAAOhF,EAAK,UACf,OAOF,IAJA,IAAI61B,EAAIhD,EACJiD,EAAID,EAAE7C,OAAO6C,EAAE/C,WAAa,SAAW,SAASv1B,KAAKyC,GACrD+1B,EAAM,CAAC,EAEF52B,EAAI,EAAG4iB,EAAI8T,EAAE5kB,IAAIrS,OAAQO,EAAI4iB,IAAK5iB,EACzC42B,EAAIF,EAAE5kB,IAAI9R,IAAM22B,EAAE32B,IAAM,GAU1B,OAPA42B,EAAIF,EAAE9C,EAAEpd,MAAQ,CAAC,EACjBogB,EAAIF,EAAE5kB,IAAI,KAAKzT,QAAQq4B,EAAE9C,EAAEC,QAAQ,SAAUgD,EAAIC,EAAIC,GAC/CD,IACFF,EAAIF,EAAE9C,EAAEpd,MAAMsgB,GAAMC,EAExB,IAEOH,CACT,CAjEqBI,CAAS5yB,GAC5B,OAAKqyB,GAKuB,KAAxBA,EAAaQ,SACfR,EAAar3B,OAASq3B,EAAar3B,OAAOf,QAAQ,IAAK,KAGzD+F,EAAMqyB,EAAar3B,OAAOf,QAAQ,IAAMo4B,EAAarQ,MAAO,KARnD,WAUX,EAylBEtH,IAvJF,SAAapd,EAAK+B,EAAM4B,GACtB,GAAK3D,EAAL,CAGA,IAAIigB,EAAOle,EAAK1G,MAAM,KAClBkD,EAAM0hB,EAAKliB,OACf,KAAIQ,EAAM,GAGV,GAAY,IAARA,EAIJ,IAGE,IAFA,IAAIi3B,EAAOx1B,EAAIigB,EAAK,KAAO,CAAC,EACxBwV,EAAcD,EACTl3B,EAAI,EAAGA,EAAIC,EAAM,IAAKD,EAC7Bk3B,EAAKvV,EAAK3hB,IAAMk3B,EAAKvV,EAAK3hB,KAAO,CAAC,EAClCk3B,EAAOA,EAAKvV,EAAK3hB,IAEnBk3B,EAAKvV,EAAK1hB,EAAM,IAAMoF,EACtB3D,EAAIigB,EAAK,IAAMwV,CACjB,CAAE,MAAO53B,GACP,MACF,MAdEmC,EAAIigB,EAAK,IAAMtc,CAPjB,CAsBF,EA+HEqG,UAvyBF,SAAmBzC,GACb8C,EAAWknB,EAAY7tB,YAAc2G,EAAWknB,EAAY11B,SAkIxDsI,EA9HMoV,KA8HI,eA5HZhS,GACEkqB,EAAiBlY,KAAK7V,aACxB6tB,EAAY7tB,UAAY6V,KAAK7V,WAE3B+tB,EAAiBlY,KAAK1d,SACxB01B,EAAY11B,MAAQ0d,KAAK1d,SAIvBwO,EAAWkP,KAAK7V,aAClB6tB,EAAY7tB,UAAY6V,KAAK7V,WAE3B2G,EAAWkP,KAAK1d,SAClB01B,EAAY11B,MAAQ0d,KAAK1d,SAI1BwO,EAAWknB,EAAY7tB,YAAe2G,EAAWknB,EAAY11B,QAChE0L,GAAgBA,EAAagqB,GAEjC,EA6wBE7tB,UAAWA,EACXytB,YA7dF,SAAqBuE,GAanB,IAHA,IAAIC,EAAQ,EACR53B,EAAS23B,EAAO33B,OAEXO,EAAI,EAAGA,EAAIP,EAAQO,IAAK,CAC/B,IAAI+Z,EAAOqd,EAAOE,WAAWt3B,GACzB+Z,EAAO,IAETsd,GAAgB,EACPtd,EAAO,KAEhBsd,GAAgB,EACPtd,EAAO,QAEhBsd,GAAgB,EAEpB,CAEA,OAAOA,CACT,EAkcE9E,SAAUA,EACVgB,MAAOA,E,uBC5xBT,SAASgE,EAAc/gB,GAIrB,MAHoB,iBAATA,IACTA,EAAOpT,OAAOoT,IAETA,EAAKlF,aACd,CAoBA,SAASkmB,EAAa5iB,GACpB1X,KAAKuB,IAAM,CAAC,EAERmW,aAAmB4iB,EACrB5iB,EAAQ6iB,SAAQ,SAAUpyB,EAAOmR,GAC/BtZ,KAAKw6B,OAAOlhB,EAAMnR,EACpB,GAAGnI,MACM4W,MAAM6jB,QAAQ/iB,GACvBA,EAAQ6iB,SAAQ,SAAU9e,GACxBzb,KAAKw6B,OAAO/e,EAAO,GAAIA,EAAO,GAChC,GAAGzb,MACM0X,GACT7S,OAAO61B,oBAAoBhjB,GAAS6iB,SAAQ,SAAUjhB,GACpDtZ,KAAKw6B,OAAOlhB,EAAM5B,EAAQ4B,GAC5B,GAAGtZ,KAEP,CAEAs6B,EAAa71B,UAAU+1B,OAAS,SAAUlhB,EAAMnR,GAC9CmR,EAAO+gB,EAAc/gB,GACrBnR,EAtCF,SAAwBA,GAItB,MAHqB,iBAAVA,IACTA,EAAQjC,OAAOiC,IAEVA,CACT,CAiCUwyB,CAAexyB,GACvB,IAAIyyB,EAAW56B,KAAKuB,IAAI+X,GACxBtZ,KAAKuB,IAAI+X,GAAQshB,EAAWA,EAAW,KAAOzyB,EAAQA,CACxD,EAEAmyB,EAAa71B,UAAUgT,IAAM,SAAU6B,GAErC,OADAA,EAAO+gB,EAAc/gB,GACdtZ,KAAK66B,IAAIvhB,GAAQtZ,KAAKuB,IAAI+X,GAAQ,IAC3C,EAEAghB,EAAa71B,UAAUo2B,IAAM,SAAUvhB,GACrC,OAAOtZ,KAAKuB,IAAI8Q,eAAegoB,EAAc/gB,GAC/C,EAEAghB,EAAa71B,UAAU81B,QAAU,SAAU9yB,EAAUqzB,GACnD,IAAK,IAAIxhB,KAAQtZ,KAAKuB,IAChBvB,KAAKuB,IAAI8Q,eAAeiH,IAC1B7R,EAAS1C,KAAK+1B,EAAS96B,KAAKuB,IAAI+X,GAAOA,EAAMtZ,KAGnD,EAEAs6B,EAAa71B,UAAUyZ,QAAU,WAC/B,IAAI6c,EAAQ,GAIZ,OAHA/6B,KAAKu6B,SAAQ,SAAUpyB,EAAOmR,GAC5ByhB,EAAM/3B,KAAK,CAACsW,EAAMnR,GACpB,IAzDF,SAAqB4yB,GAQnB,MAPe,CACb3c,KAAM,WACJ,IAAIjW,EAAQ4yB,EAAM53B,QAClB,MAAO,CAAEkb,UAAgBjd,IAAV+G,EAAqBA,MAAOA,EAC7C,EAIJ,CAiDS6yB,CAAYD,EACrB,EAEAtyB,EAAOC,QAnFP,SAAiBgP,GACf,MAAuB,oBAAZujB,QACF,IAAIX,EAAa5iB,GAGnB,IAAIujB,QAAQvjB,EACrB,C,6BChBA,IAAI3L,EAAe,EAAQ,KAE3BtD,EAAOC,QAAUqD,C,uBCMjBtD,EAAOC,QARP,SAAiBlE,EAAK8U,EAAM2gB,EAAathB,EAAclG,GACrD,IAAIwI,EAAOzW,EAAI8U,GACf9U,EAAI8U,GAAQ2gB,EAAYhf,GACpBtC,GACFA,EAAalG,GAAMzP,KAAK,CAACwB,EAAK8U,EAAM2B,GAExC,C,6BCNA,IAAI9U,EAAI,EAAQ,KAoDhBsC,EAAOC,QAlDP,SAAkBlE,EAAKwe,EAAM4P,GAC3B,IAAI3sB,EAAGrB,EAAG9B,EAINo4B,EAHAC,EAAQh1B,EAAEwC,OAAOnE,EAAK,UACtBi2B,EAAUt0B,EAAEwC,OAAOnE,EAAK,SACxBigB,EAAO,GAMX,GAFAmO,EAAOA,GAAQ,CAAEpuB,IAAK,GAAI42B,OAAQ,IAE9BD,EAAO,CAGT,GAFAD,EAAYtI,EAAKpuB,IAAIxD,QAAQwD,GAEzB22B,IAAwB,IAAfD,EAEX,OAAOtI,EAAKwI,OAAOF,IAActI,EAAKpuB,IAAI02B,GAG5CtI,EAAKpuB,IAAIxB,KAAKwB,GACd02B,EAAYtI,EAAKpuB,IAAIjC,OAAS,CAChC,CAEA,GAAI44B,EACF,IAAKl1B,KAAKzB,EACJK,OAAOJ,UAAU4N,eAAetN,KAAKP,EAAKyB,IAC5Cwe,EAAKzhB,KAAKiD,QAGT,GAAIw0B,EACT,IAAK33B,EAAI,EAAGA,EAAI0B,EAAIjC,SAAUO,EAC5B2hB,EAAKzhB,KAAKF,GAId,IAAID,EAASs4B,EAAQ,CAAC,EAAI,GACtBE,GAAO,EACX,IAAKv4B,EAAI,EAAGA,EAAI2hB,EAAKliB,SAAUO,EAE7B8B,EAAIJ,EADJyB,EAAIwe,EAAK3hB,IAETD,EAAOoD,GAAK+c,EAAK/c,EAAGrB,EAAGguB,GACvByI,EAAOA,GAAQx4B,EAAOoD,KAAOzB,EAAIyB,GAOnC,OAJIk1B,IAAUE,IACZzI,EAAKwI,OAAOF,GAAar4B,GAGnBw4B,EAAgB72B,EAAT3B,CACjB,C,UCssBA4F,EAAOC,QAnmBe,SAASqV,GAE7B,IAqCIud,EACAC,EACAC,EACAC,EAgOIC,EAaAj7B,EACAk7B,EACA/mB,EACAzM,EACAyzB,EAUA1B,EAsBA2B,EAcAC,EAnUJC,EAAe,kIAGnB,SAASlqB,EAAEvO,GAET,OAAOA,EAAI,GACP,IAAMA,EACNA,CACN,CAEA,SAAS04B,IACP,OAAOh8B,KAAKi8B,SACd,CA2BA,SAASC,EAAMhC,GAQb,OADA6B,EAAaI,UAAY,EAClBJ,EAAa/xB,KAAKkwB,GACrB,IAAOA,EAAO/4B,QAAQ46B,GAAc,SAAU7mB,GAC9C,IAAIqJ,EAAIid,EAAKtmB,GACb,MAAoB,iBAANqJ,EACVA,EACA,OAAS,OAASrJ,EAAEklB,WAAW,GAAGt1B,SAAS,KAAKnD,OAAO,EAC7D,IAAK,IACL,IAAOu4B,EAAS,GACpB,CAGA,SAASv2B,EAAIiR,EAAKwnB,GAIhB,IAAIt5B,EACAmD,EACArB,EACArC,EAEA85B,EADAC,EAAOhB,EAEPnzB,EAAQi0B,EAAOxnB,GAkBnB,OAdIzM,GAA0B,iBAAVA,GACQ,mBAAjBA,EAAMo0B,SACfp0B,EAAQA,EAAMo0B,OAAO3nB,IAMJ,mBAAR6mB,IACTtzB,EAAQszB,EAAI12B,KAAKq3B,EAAQxnB,EAAKzM,WAKjBA,GACb,IAAK,SACH,OAAO+zB,EAAM/zB,GAEf,IAAK,SAIH,OAAO1E,SAAS0E,GACZjC,OAAOiC,GACP,OAEN,IAAK,UACL,IAAK,OAMH,OAAOjC,OAAOiC,GAKhB,IAAK,SAKH,IAAKA,EACH,MAAO,OAUT,GALAmzB,GAAOC,EACPc,EAAU,GAIqC,mBAA3Cx3B,OAAOJ,UAAUK,SAASyK,MAAMpH,GAA6B,CAM/D,IADA5F,EAAS4F,EAAM5F,OACVO,EAAI,EAAGA,EAAIP,EAAQO,GAAK,EAC3Bu5B,EAAQv5B,GAAKa,EAAIb,EAAGqF,IAAU,OAYhC,OANAvD,EAAuB,IAAnBy3B,EAAQ95B,OACR,KACA+4B,EACA,MAAQA,EAAMe,EAAQv6B,KAAK,MAAQw5B,GAAO,KAAOgB,EAAO,IACxD,IAAMD,EAAQv6B,KAAK,KAAO,IAC9Bw5B,EAAMgB,EACC13B,CACT,CAIA,GAAI62B,GAAsB,iBAARA,EAEhB,IADAl5B,EAASk5B,EAAIl5B,OACRO,EAAI,EAAGA,EAAIP,EAAQO,GAAK,EACL,iBAAX24B,EAAI34B,KAEb8B,EAAIjB,EADJsC,EAAIw1B,EAAI34B,GACGqF,KAETk0B,EAAQr5B,KAAKk5B,EAAMj2B,IACbq1B,EACE,KACA,KACE12B,QAQhB,IAAKqB,KAAKkC,EACJtD,OAAOJ,UAAU4N,eAAetN,KAAKoD,EAAOlC,KAC9CrB,EAAIjB,EAAIsC,EAAGkC,KAETk0B,EAAQr5B,KAAKk5B,EAAMj2B,IACbq1B,EACE,KACA,KACE12B,GAelB,OANAA,EAAuB,IAAnBy3B,EAAQ95B,OACR,KACA+4B,EACA,MAAQA,EAAMe,EAAQv6B,KAAK,MAAQw5B,GAAO,KAAOgB,EAAO,IACxD,IAAMD,EAAQv6B,KAAK,KAAO,IAC9Bw5B,EAAMgB,EACC13B,EAEb,CApLqC,mBAA1B6F,KAAKhG,UAAU83B,SAExB9xB,KAAKhG,UAAU83B,OAAS,WAEtB,OAAO94B,SAASzD,KAAKi8B,WACjBj8B,KAAKw8B,iBAAmB,IAC1B3qB,EAAE7R,KAAKy8B,cAAgB,GAAK,IAC5B5qB,EAAE7R,KAAK08B,cAAgB,IACvB7qB,EAAE7R,KAAK28B,eAAiB,IACxB9qB,EAAE7R,KAAK48B,iBAAmB,IAC1B/qB,EAAE7R,KAAK68B,iBAAmB,IACxB,IACN,EAEA/2B,QAAQrB,UAAU83B,OAASP,EAC3Bh2B,OAAOvB,UAAU83B,OAASP,EAC1B91B,OAAOzB,UAAU83B,OAASP,GAwKE,mBAAnBje,EAAK7V,YACdszB,EAAO,CACL,KAAM,MACN,KAAM,MACN,KAAM,MACN,KAAM,MACN,KAAM,MACN,IAAM,MACN,KAAM,QAERzd,EAAK7V,UAAY,SAAUC,EAAO20B,EAAUC,GAQ1C,IAAIj6B,EAOJ,GANAw4B,EAAM,GACNC,EAAS,GAKY,iBAAVwB,EACT,IAAKj6B,EAAI,EAAGA,EAAIi6B,EAAOj6B,GAAK,EAC1By4B,GAAU,QAKc,iBAAVwB,IAChBxB,EAASwB,GAOX,GADAtB,EAAMqB,EACFA,GAAgC,mBAAbA,IACE,iBAAbA,GACoB,iBAApBA,EAASv6B,QACnB,MAAM,IAAI1B,MAAM,kBAMlB,OAAO8C,EAAI,GAAI,CAAC,GAAIwE,GACtB,GAMwB,mBAAf4V,EAAK1d,QACd0d,EAAK1d,OAsBCu7B,EAAU,CACZ,KAAM,KACN,IAAM,IACN,IAAK,IACL,EAAK,KACL,EAAK,KACL,EAAK,KACL,EAAK,KACL,EAAK,MAEH1B,EAAS,CACX8C,GAAI,WACFtB,EAAQ,IACV,EACAuB,UAAW,WACTroB,EAAMzM,EACNuzB,EAAQ,OACV,EACAwB,KAAM,WACJtoB,EAAMzM,EACNuzB,EAAQ,OACV,EACAyB,OAAQ,WACNzB,EAAQ,QACV,EACA0B,YAAa,WACX1B,EAAQ,QACV,EACA2B,OAAQ,WACN3B,EAAQ,QACV,GAEEG,EAAS,CACXmB,GAAI,WACFtB,EAAQ,IACV,EACAyB,OAAQ,WACNzB,EAAQ,QACV,EACA0B,YAAa,WACX1B,EAAQ,QACV,EACA2B,OAAQ,WACN3B,EAAQ,QACV,GAEEI,EAAS,CAOX,IAAK,CACHkB,GAAI,WACFv8B,EAAMuC,KAAK,CAAC04B,MAAO,OACnBC,EAAY,CAAC,EACbD,EAAQ,WACV,EACAyB,OAAQ,WACN18B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,SAAU9mB,IAAKA,IACxD+mB,EAAY,CAAC,EACbD,EAAQ,WACV,EACA0B,YAAa,WACX38B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,CAAC,EACbD,EAAQ,WACV,EACA2B,OAAQ,WACN58B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,CAAC,EACbD,EAAQ,WACV,GAEF,IAAK,CACHuB,UAAW,WACT,IAAIn9B,EAAMW,EAAMX,MAChBqI,EAAQwzB,EACRA,EAAY77B,EAAI67B,UAChB/mB,EAAM9U,EAAI8U,IACV8mB,EAAQ57B,EAAI47B,KACd,EACA4B,OAAQ,WACN,IAAIx9B,EAAMW,EAAMX,MAChB67B,EAAU/mB,GAAOzM,EACjBA,EAAQwzB,EACRA,EAAY77B,EAAI67B,UAChB/mB,EAAM9U,EAAI8U,IACV8mB,EAAQ57B,EAAI47B,KACd,GAEF,IAAK,CACHsB,GAAI,WACFv8B,EAAMuC,KAAK,CAAC04B,MAAO,OACnBC,EAAY,GACZD,EAAQ,aACV,EACAyB,OAAQ,WACN18B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,SAAU9mB,IAAKA,IACxD+mB,EAAY,GACZD,EAAQ,aACV,EACA0B,YAAa,WACX38B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,GACZD,EAAQ,aACV,EACA2B,OAAQ,WACN58B,EAAMuC,KAAK,CAAC24B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,GACZD,EAAQ,aACV,GAEF,IAAK,CACH0B,YAAa,WACX,IAAIt9B,EAAMW,EAAMX,MAChBqI,EAAQwzB,EACRA,EAAY77B,EAAI67B,UAChB/mB,EAAM9U,EAAI8U,IACV8mB,EAAQ57B,EAAI47B,KACd,EACA6B,OAAQ,WACN,IAAIz9B,EAAMW,EAAMX,MAChB67B,EAAU34B,KAAKmF,GACfA,EAAQwzB,EACRA,EAAY77B,EAAI67B,UAChB/mB,EAAM9U,EAAI8U,IACV8mB,EAAQ57B,EAAI47B,KACd,GAEF,IAAK,CACH8B,MAAO,WACL,GAAI34B,OAAOwN,eAAetN,KAAK42B,EAAW/mB,GACxC,MAAM,IAAI6oB,YAAY,kBAAoB7oB,EAAM,KAElD8mB,EAAQ,QACV,GAEF,IAAK,CACH4B,OAAQ,WACN3B,EAAU/mB,GAAOzM,EACjBuzB,EAAQ,MACV,EACA6B,OAAQ,WACN5B,EAAU34B,KAAKmF,GACfuzB,EAAQ,QACV,GAEF,KAAQ,CACNsB,GAAI,WACF70B,GAAQ,EACRuzB,EAAQ,IACV,EACAyB,OAAQ,WACNh1B,GAAQ,EACRuzB,EAAQ,QACV,EACA0B,YAAa,WACXj1B,GAAQ,EACRuzB,EAAQ,QACV,EACA2B,OAAQ,WACNl1B,GAAQ,EACRuzB,EAAQ,QACV,GAEF,MAAS,CACPsB,GAAI,WACF70B,GAAQ,EACRuzB,EAAQ,IACV,EACAyB,OAAQ,WACNh1B,GAAQ,EACRuzB,EAAQ,QACV,EACA0B,YAAa,WACXj1B,GAAQ,EACRuzB,EAAQ,QACV,EACA2B,OAAQ,WACNl1B,GAAQ,EACRuzB,EAAQ,QACV,GAEF,KAAQ,CACNsB,GAAI,WACF70B,EAAQ,KACRuzB,EAAQ,IACV,EACAyB,OAAQ,WACNh1B,EAAQ,KACRuzB,EAAQ,QACV,EACA0B,YAAa,WACXj1B,EAAQ,KACRuzB,EAAQ,QACV,EACA2B,OAAQ,WACNl1B,EAAQ,KACRuzB,EAAQ,QACV,IAeG,SAAUx5B,EAAQw7B,GAKvB,IAAI76B,EAhBkB2a,EAiBlBmgB,EAAK,iJAITjC,EAAQ,KAKRj7B,EAAQ,GAIR,IAIE,KACEoC,EAAS86B,EAAGz8B,KAAKgB,IAWbW,EAAO,GAITi5B,EAAOj5B,EAAO,IAAI64B,KAET74B,EAAO,IAKhBsF,GAAStF,EAAO,GAChBg5B,EAAOH,OA1DSle,EAgEO3a,EAAO,GAA9BsF,EA5DCqV,EAAKrc,QAAQ,yBAAyB,SAAUy8B,EAAQhlB,EAAG2F,GAChE,OAAO3F,EACH1S,OAAO23B,aAAa1U,SAASvQ,EAAG,KAChCgjB,EAAQrd,EACd,IAyDM2b,EAAOwB,MAOTx5B,EAASA,EAAOP,MAAMkB,EAAO,GAAGN,OAMpC,CAAE,MAAOF,GACPq5B,EAAQr5B,CACV,CAMA,GAAc,OAAVq5B,GAAmB,kBAAkB1xB,KAAK9H,GAC5C,MAAOw5B,aAAiB+B,YACpB/B,EACA,IAAI+B,YAAY,QAStB,MAA2B,mBAAZC,EACV,SAASI,EAAK1B,EAAQxnB,GACvB,IAAI3O,EACArB,EACAswB,EAAMkH,EAAOxnB,GACjB,GAAIsgB,GAAsB,iBAARA,EAChB,IAAKjvB,KAAKkC,EACJtD,OAAOJ,UAAU4N,eAAetN,KAAKmwB,EAAKjvB,UAElC7E,KADVwD,EAAIk5B,EAAK5I,EAAKjvB,IAEZivB,EAAIjvB,GAAKrB,SAEFswB,EAAIjvB,IAKnB,OAAOy3B,EAAQ34B,KAAKq3B,EAAQxnB,EAAKsgB,EACnC,CAjBE,CAiBA,CAAC,GAAI/sB,GAAQ,IACfA,CACJ,GAGN,C,GCvvBI41B,EAA2B,CAAC,GAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB78B,IAAjB88B,EACH,OAAOA,EAAax1B,QAGrB,IAAID,EAASs1B,EAAyBE,GAAY,CAGjDv1B,QAAS,CAAC,GAOX,OAHAy1B,EAAoBF,GAAUl5B,KAAK0D,EAAOC,QAASD,EAAQA,EAAOC,QAASs1B,GAGpEv1B,EAAOC,OACf,ECnB0Bs1B,CAAoB,I", "sources": ["webpack://rollbar/./node_modules/console-polyfill/index.js", "webpack://rollbar/./node_modules/error-stack-parser/error-stack-parser.js", "webpack://rollbar/./node_modules/error-stack-parser/node_modules/stackframe/stackframe.js", "webpack://rollbar/./src/api.js", "webpack://rollbar/./src/apiUtility.js", "webpack://rollbar/./src/browser/bundles/rollbar.js", "webpack://rollbar/./src/browser/core.js", "webpack://rollbar/./src/browser/defaults/scrubFields.js", "webpack://rollbar/./src/browser/detection.js", "webpack://rollbar/./src/browser/domUtility.js", "webpack://rollbar/./src/browser/globalSetup.js", "webpack://rollbar/./src/browser/logger.js", "webpack://rollbar/./src/browser/predicates.js", "webpack://rollbar/./src/browser/rollbar.js", "webpack://rollbar/./src/browser/telemetry.js", "webpack://rollbar/./src/browser/transforms.js", "webpack://rollbar/./src/browser/transport.js", "webpack://rollbar/./src/browser/transport/fetch.js", "webpack://rollbar/./src/browser/transport/xhr.js", "webpack://rollbar/./src/browser/url.js", "webpack://rollbar/./src/browser/wrapGlobals.js", "webpack://rollbar/./src/defaults.js", "webpack://rollbar/./src/errorParser.js", "webpack://rollbar/./src/merge.js", "webpack://rollbar/./src/notifier.js", "webpack://rollbar/./src/predicates.js", "webpack://rollbar/./src/queue.js", "webpack://rollbar/./src/rateLimiter.js", "webpack://rollbar/./src/rollbar.js", "webpack://rollbar/./src/scrub.js", "webpack://rollbar/./src/telemetry.js", "webpack://rollbar/./src/transforms.js", "webpack://rollbar/./src/truncation.js", "webpack://rollbar/./src/utility.js", "webpack://rollbar/./src/utility/headers.js", "webpack://rollbar/./src/utility/polyfillJSON.js", "webpack://rollbar/./src/utility/replace.js", "webpack://rollbar/./src/utility/traverse.js", "webpack://rollbar/./vendor/JSON-js/json3.js", "webpack://rollbar/webpack/bootstrap", "webpack://rollbar/webpack/startup"], "sourcesContent": ["// Console-polyfill. MIT license.\n// https://github.com/paulmillr/console-polyfill\n// Make it safe to do console.log() always.\n(function(global) {\n  'use strict';\n  if (!global.console) {\n    global.console = {};\n  }\n  var con = global.console;\n  var prop, method;\n  var dummy = function() {};\n  var properties = ['memory'];\n  var methods = ('assert,clear,count,debug,dir,dirxml,error,exception,group,' +\n     'groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,' +\n     'show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn').split(',');\n  while (prop = properties.pop()) if (!con[prop]) con[prop] = {};\n  while (method = methods.pop()) if (!con[method]) con[method] = dummy;\n  // Using `this` for web workers & supports Browserify / Webpack.\n})(typeof window === 'undefined' ? this : window);\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n    var objectProps = ['evalOrigin'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps, objectProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "'use strict';\n\nvar _ = require('./utility');\nvar helpers = require('./apiUtility');\n\nvar defaultOptions = {\n  hostname: 'api.rollbar.com',\n  path: '/api/1/item/',\n  search: null,\n  version: '1',\n  protocol: 'https:',\n  port: 443,\n};\n\n/**\n * Api is an object that encapsulates methods of communicating with\n * the Rollbar API.  It is a standard interface with some parts implemented\n * differently for server or browser contexts.  It is an object that should\n * be instantiated when used so it can contain non-global options that may\n * be different for another instance of RollbarApi.\n *\n * @param options {\n *    accessToken: the accessToken to use for posting items to rollbar\n *    endpoint: an alternative endpoint to send errors to\n *        must be a valid, fully qualified URL.\n *        The default is: https://api.rollbar.com/api/1/item\n *    proxy: if you wish to proxy requests provide an object\n *        with the following keys:\n *          host or hostname (required): foo.example.com\n *          port (optional): 123\n *          protocol (optional): https\n * }\n */\nfunction Api(options, transport, urllib, truncation, jsonBackup) {\n  this.options = options;\n  this.transport = transport;\n  this.url = urllib;\n  this.truncation = truncation;\n  this.jsonBackup = jsonBackup;\n  this.accessToken = options.accessToken;\n  this.transportOptions = _getTransport(options, urllib);\n}\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.postItem = function (data, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n  var self = this;\n\n  // ensure the network request is scheduled after the current tick.\n  setTimeout(function () {\n    self.transport.post(self.accessToken, transportOptions, payload, callback);\n  }, 0);\n};\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.buildJsonPayload = function (data, callback) {\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n\n  if (stringifyResult.error) {\n    if (callback) {\n      callback(stringifyResult.error);\n    }\n    return null;\n  }\n\n  return stringifyResult.value;\n};\n\n/**\n *\n * @param jsonPayload\n * @param callback\n */\nApi.prototype.postJsonPayload = function (jsonPayload, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  this.transport.postJsonPayload(\n    this.accessToken,\n    transportOptions,\n    jsonPayload,\n    callback,\n  );\n};\n\nApi.prototype.configure = function (options) {\n  var oldOptions = this.oldOptions;\n  this.options = _.merge(oldOptions, options);\n  this.transportOptions = _getTransport(this.options, this.url);\n  if (this.options.accessToken !== undefined) {\n    this.accessToken = this.options.accessToken;\n  }\n  return this;\n};\n\nfunction _getTransport(options, url) {\n  return helpers.getTransportFromOptions(options, defaultOptions, url);\n}\n\nmodule.exports = Api;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction buildPayload(accessToken, data, jsonBackup) {\n  if (!_.isType(data.context, 'string')) {\n    var contextResult = _.stringify(data.context, jsonBackup);\n    if (contextResult.error) {\n      data.context = \"Error: could not serialize 'context'\";\n    } else {\n      data.context = contextResult.value || '';\n    }\n    if (data.context.length > 255) {\n      data.context = data.context.substr(0, 255);\n    }\n  }\n  return {\n    access_token: accessToken,\n    data: data,\n  };\n}\n\nfunction getTransportFromOptions(options, defaults, url) {\n  var hostname = defaults.hostname;\n  var protocol = defaults.protocol;\n  var port = defaults.port;\n  var path = defaults.path;\n  var search = defaults.search;\n  var timeout = options.timeout;\n  var transport = detectTransport(options);\n\n  var proxy = options.proxy;\n  if (options.endpoint) {\n    var opts = url.parse(options.endpoint);\n    hostname = opts.hostname;\n    protocol = opts.protocol;\n    port = opts.port;\n    path = opts.pathname;\n    search = opts.search;\n  }\n  return {\n    timeout: timeout,\n    hostname: hostname,\n    protocol: protocol,\n    port: port,\n    path: path,\n    search: search,\n    proxy: proxy,\n    transport: transport,\n  };\n}\n\nfunction detectTransport(options) {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var transport = options.defaultTransport || 'xhr';\n  if (typeof gWindow.fetch === 'undefined') transport = 'xhr';\n  if (typeof gWindow.XMLHttpRequest === 'undefined') transport = 'fetch';\n  return transport;\n}\n\nfunction transportOptions(transport, method) {\n  var protocol = transport.protocol || 'https:';\n  var port =\n    transport.port ||\n    (protocol === 'http:' ? 80 : protocol === 'https:' ? 443 : undefined);\n  var hostname = transport.hostname;\n  var path = transport.path;\n  var timeout = transport.timeout;\n  var transportAPI = transport.transport;\n  if (transport.search) {\n    path = path + transport.search;\n  }\n  if (transport.proxy) {\n    path = protocol + '//' + hostname + path;\n    hostname = transport.proxy.host || transport.proxy.hostname;\n    port = transport.proxy.port;\n    protocol = transport.proxy.protocol || protocol;\n  }\n  return {\n    timeout: timeout,\n    protocol: protocol,\n    hostname: hostname,\n    path: path,\n    port: port,\n    method: method,\n    transport: transportAPI,\n  };\n}\n\nfunction appendPathToPath(base, path) {\n  var baseTrailingSlash = /\\/$/.test(base);\n  var pathBeginningSlash = /^\\//.test(path);\n\n  if (baseTrailingSlash && pathBeginningSlash) {\n    path = path.substring(1);\n  } else if (!baseTrailingSlash && !pathBeginningSlash) {\n    path = '/' + path;\n  }\n\n  return base + path;\n}\n\nmodule.exports = {\n  buildPayload: buildPayload,\n  getTransportFromOptions: getTransportFromOptions,\n  transportOptions: transportOptions,\n  appendPathToPath: appendPathToPath,\n};\n", "'use strict';\n\nvar rollbar = require('../rollbar');\n\nvar options = (typeof window !== 'undefined') && window._rollbarConfig;\nvar alias = options && options.globalAlias || 'Rollbar';\nvar shimRunning = (typeof window !== 'undefined') && window[alias] && typeof window[alias].shimId === 'function' && window[alias].shimId() !== undefined;\n\nif ((typeof window !== 'undefined') && !window._rollbarStartTime) {\n  window._rollbarStartTime = (new Date()).getTime();\n}\n\nif (!shimRunning && options) {\n  var Rollbar = new rollbar(options);\n  window[alias] = Rollbar;\n} else if (typeof window !== 'undefined') {\n  window.rollbar = rollbar;\n  window._rollbarDidLoad = true;\n} else if (typeof self !== 'undefined') {\n  self.rollbar = rollbar;\n  self._rollbarDidLoad = true;\n}\n\nmodule.exports = rollbar;\n", "'use strict';\n\nvar Client = require('../rollbar');\nvar _ = require('../utility');\nvar API = require('../api');\nvar logger = require('./logger');\nvar globals = require('./globalSetup');\n\nvar Transport = require('./transport');\nvar urllib = require('./url');\n\nvar transforms = require('./transforms');\nvar sharedTransforms = require('../transforms');\nvar predicates = require('./predicates');\nvar sharedPredicates = require('../predicates');\nvar errorParser = require('../errorParser');\n\nfunction Rollbar(options, client) {\n  this.options = _.handleOptions(defaultOptions, options, null, logger);\n  this.options._configuredOptions = options;\n  var Telemeter = this.components.telemeter;\n  var Instrumenter = this.components.instrumenter;\n  var polyfillJSON = this.components.polyfillJSON;\n  this.wrapGlobals = this.components.wrapGlobals;\n  this.scrub = this.components.scrub;\n  var truncation = this.components.truncation;\n\n  var transport = new Transport(truncation);\n  var api = new API(this.options, transport, urllib, truncation);\n  if (Telemeter) {\n    this.telemeter = new Telemeter(this.options);\n  }\n  this.client =\n    client || new Client(this.options, api, logger, this.telemeter, 'browser');\n  var gWindow = _gWindow();\n  var gDocument = typeof document != 'undefined' && document;\n  this.isChrome = gWindow.chrome && gWindow.chrome.runtime; // check .runtime to avoid Edge browsers\n  this.anonymousErrorsPending = 0;\n  addTransformsToNotifier(this.client.notifier, this, gWindow);\n  addPredicatesToQueue(this.client.queue);\n  this.setupUnhandledCapture();\n  if (Instrumenter) {\n    this.instrumenter = new Instrumenter(\n      this.options,\n      this.client.telemeter,\n      this,\n      gWindow,\n      gDocument,\n    );\n    this.instrumenter.instrument();\n  }\n  _.setupJSON(polyfillJSON);\n\n  // Used with rollbar-react for rollbar-react-native compatibility.\n  this.rollbar = this;\n}\n\nvar _instance = null;\nRollbar.init = function (options, client) {\n  if (_instance) {\n    return _instance.global(options).configure(options);\n  }\n  _instance = new Rollbar(options, client);\n  return _instance;\n};\n\nRollbar.prototype.components = {};\n\nRollbar.setComponents = function (components) {\n  Rollbar.prototype.components = components;\n};\n\nfunction handleUninitialized(maybeCallback) {\n  var message = 'Rollbar is not initialized';\n  logger.error(message);\n  if (maybeCallback) {\n    maybeCallback(new Error(message));\n  }\n}\n\nRollbar.prototype.global = function (options) {\n  this.client.global(options);\n  return this;\n};\nRollbar.global = function (options) {\n  if (_instance) {\n    return _instance.global(options);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n  this.options = _.handleOptions(oldOptions, options, payload, logger);\n  this.options._configuredOptions = _.handleOptions(\n    oldOptions._configuredOptions,\n    options,\n    payload,\n  );\n  this.client.configure(this.options, payloadData);\n  this.instrumenter && this.instrumenter.configure(this.options);\n  this.setupUnhandledCapture();\n  return this;\n};\nRollbar.configure = function (options, payloadData) {\n  if (_instance) {\n    return _instance.configure(options, payloadData);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.lastError = function () {\n  return this.client.lastError;\n};\nRollbar.lastError = function () {\n  if (_instance) {\n    return _instance.lastError();\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.log = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.log(item);\n  return { uuid: uuid };\n};\nRollbar.log = function () {\n  if (_instance) {\n    return _instance.log.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.debug = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.debug(item);\n  return { uuid: uuid };\n};\nRollbar.debug = function () {\n  if (_instance) {\n    return _instance.debug.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.info = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.info(item);\n  return { uuid: uuid };\n};\nRollbar.info = function () {\n  if (_instance) {\n    return _instance.info.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warn = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warn(item);\n  return { uuid: uuid };\n};\nRollbar.warn = function () {\n  if (_instance) {\n    return _instance.warn.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warning = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warning(item);\n  return { uuid: uuid };\n};\nRollbar.warning = function () {\n  if (_instance) {\n    return _instance.warning.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.error = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.error(item);\n  return { uuid: uuid };\n};\nRollbar.error = function () {\n  if (_instance) {\n    return _instance.error.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.critical = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.critical(item);\n  return { uuid: uuid };\n};\nRollbar.critical = function () {\n  if (_instance) {\n    return _instance.critical.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.client.buildJsonPayload(item);\n};\nRollbar.buildJsonPayload = function () {\n  if (_instance) {\n    return _instance.buildJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  return this.client.sendJsonPayload(jsonPayload);\n};\nRollbar.sendJsonPayload = function () {\n  if (_instance) {\n    return _instance.sendJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.setupUnhandledCapture = function () {\n  var gWindow = _gWindow();\n\n  if (!this.unhandledExceptionsInitialized) {\n    if (this.options.captureUncaught || this.options.handleUncaughtExceptions) {\n      globals.captureUncaughtExceptions(gWindow, this);\n      if (this.wrapGlobals && this.options.wrapGlobalEventHandlers) {\n        this.wrapGlobals(gWindow, this);\n      }\n      this.unhandledExceptionsInitialized = true;\n    }\n  }\n  if (!this.unhandledRejectionsInitialized) {\n    if (\n      this.options.captureUnhandledRejections ||\n      this.options.handleUnhandledRejections\n    ) {\n      globals.captureUnhandledRejections(gWindow, this);\n      this.unhandledRejectionsInitialized = true;\n    }\n  }\n};\n\nRollbar.prototype.handleUncaughtException = function (\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  context,\n) {\n  if (!this.options.captureUncaught && !this.options.handleUncaughtExceptions) {\n    return;\n  }\n\n  // Chrome will always send 5+ arguments and error will be valid or null, not undefined.\n  // If error is undefined, we have a different caller.\n  // Chrome also sends errors from web workers with null error, but does not invoke\n  // prepareStackTrace() for these. Test for empty url to skip them.\n  if (\n    this.options.inspectAnonymousErrors &&\n    this.isChrome &&\n    error === null &&\n    url === ''\n  ) {\n    return 'anonymous';\n  }\n\n  var item;\n  var stackInfo = _.makeUnhandledStackInfo(\n    message,\n    url,\n    lineno,\n    colno,\n    error,\n    'onerror',\n    'uncaught exception',\n    errorParser,\n  );\n  if (_.isError(error)) {\n    item = this._createItem([message, error, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else if (_.isError(url)) {\n    item = this._createItem([message, url, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else {\n    item = this._createItem([message, context]);\n    item.stackInfo = stackInfo;\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  this.client.log(item);\n};\n\n/**\n * Chrome only. Other browsers will ignore.\n *\n * Use Error.prepareStackTrace to extract information about errors that\n * do not have a valid error object in onerror().\n *\n * In tested version of Chrome, onerror is called first but has no way\n * to communicate with prepareStackTrace. Use a counter to let this\n * handler know which errors to send to Rollbar.\n *\n * In config options, set inspectAnonymousErrors to enable.\n */\nRollbar.prototype.handleAnonymousErrors = function () {\n  if (!this.options.inspectAnonymousErrors || !this.isChrome) {\n    return;\n  }\n\n  var r = this;\n  function prepareStackTrace(error, _stack) {\n    // eslint-disable-line no-unused-vars\n    if (r.options.inspectAnonymousErrors) {\n      if (r.anonymousErrorsPending) {\n        // This is the only known way to detect that onerror saw an anonymous error.\n        // It depends on onerror reliably being called before Error.prepareStackTrace,\n        // which so far holds true on tested versions of Chrome. If versions of Chrome\n        // are tested that behave differently, this logic will need to be updated\n        // accordingly.\n        r.anonymousErrorsPending -= 1;\n\n        if (!error) {\n          // Not likely to get here, but calling handleUncaughtException from here\n          // without an error object would throw off the anonymousErrorsPending counter,\n          // so return now.\n          return;\n        }\n\n        // Allow this to be tracked later.\n        error._isAnonymous = true;\n\n        // url, lineno, colno shouldn't be needed for these errors.\n        // If that changes, update this accordingly, using the unused\n        // _stack param as needed (rather than parse error.toString()).\n        r.handleUncaughtException(error.message, null, null, null, error);\n      }\n    }\n\n    // Workaround to ensure stack is preserved for normal errors.\n    return error.stack;\n  }\n\n  // https://v8.dev/docs/stack-trace-api\n  try {\n    Error.prepareStackTrace = prepareStackTrace;\n  } catch (e) {\n    this.options.inspectAnonymousErrors = false;\n    this.error('anonymous error handler failed', e);\n  }\n};\n\nRollbar.prototype.handleUnhandledRejection = function (reason, promise) {\n  if (\n    !this.options.captureUnhandledRejections &&\n    !this.options.handleUnhandledRejections\n  ) {\n    return;\n  }\n\n  var message = 'unhandled rejection was null or undefined!';\n  if (reason) {\n    if (reason.message) {\n      message = reason.message;\n    } else {\n      var reasonResult = _.stringify(reason);\n      if (reasonResult.value) {\n        message = reasonResult.value;\n      }\n    }\n  }\n  var context =\n    (reason && reason._rollbarContext) || (promise && promise._rollbarContext);\n\n  var item;\n  if (_.isError(reason)) {\n    item = this._createItem([message, reason, context]);\n  } else {\n    item = this._createItem([message, reason, context]);\n    item.stackInfo = _.makeUnhandledStackInfo(\n      message,\n      '',\n      0,\n      0,\n      null,\n      'unhandledrejection',\n      '',\n      errorParser,\n    );\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  item._originalArgs = item._originalArgs || [];\n  item._originalArgs.push(promise);\n  this.client.log(item);\n};\n\nRollbar.prototype.wrap = function (f, context, _before) {\n  try {\n    var ctxFn;\n    if (_.isFunction(context)) {\n      ctxFn = context;\n    } else {\n      ctxFn = function () {\n        return context || {};\n      };\n    }\n\n    if (!_.isFunction(f)) {\n      return f;\n    }\n\n    if (f._isWrap) {\n      return f;\n    }\n\n    if (!f._rollbar_wrapped) {\n      f._rollbar_wrapped = function () {\n        if (_before && _.isFunction(_before)) {\n          _before.apply(this, arguments);\n        }\n        try {\n          return f.apply(this, arguments);\n        } catch (exc) {\n          var e = exc;\n          if (e && window._rollbarWrappedError !== e) {\n            if (_.isType(e, 'string')) {\n              e = new String(e);\n            }\n            e._rollbarContext = ctxFn() || {};\n            e._rollbarContext._wrappedSource = f.toString();\n\n            window._rollbarWrappedError = e;\n          }\n          throw e;\n        }\n      };\n\n      f._rollbar_wrapped._isWrap = true;\n\n      if (f.hasOwnProperty) {\n        for (var prop in f) {\n          if (f.hasOwnProperty(prop) && prop !== '_rollbar_wrapped') {\n            f._rollbar_wrapped[prop] = f[prop];\n          }\n        }\n      }\n    }\n\n    return f._rollbar_wrapped;\n  } catch (e) {\n    // Return the original function if the wrap fails.\n    return f;\n  }\n};\nRollbar.wrap = function (f, context) {\n  if (_instance) {\n    return _instance.wrap(f, context);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.captureEvent = function () {\n  var event = _.createTelemetryEvent(arguments);\n  return this.client.captureEvent(event.type, event.metadata, event.level);\n};\nRollbar.captureEvent = function () {\n  if (_instance) {\n    return _instance.captureEvent.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\n// The following two methods are used internally and are not meant for public use\nRollbar.prototype.captureDomContentLoaded = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureLoad(ts);\n};\n\n/* Internal */\n\nfunction addTransformsToNotifier(notifier, rollbar, gWindow) {\n  notifier\n    .addTransform(transforms.handleDomException)\n    .addTransform(transforms.handleItemWithError)\n    .addTransform(transforms.ensureItemHasSomethingToSay)\n    .addTransform(transforms.addBaseInfo)\n    .addTransform(transforms.addRequestInfo(gWindow))\n    .addTransform(transforms.addClientInfo(gWindow))\n    .addTransform(transforms.addPluginInfo(gWindow))\n    .addTransform(transforms.addBody)\n    .addTransform(sharedTransforms.addMessageWithError)\n    .addTransform(sharedTransforms.addTelemetryData)\n    .addTransform(sharedTransforms.addConfigToPayload)\n    .addTransform(transforms.addScrubber(rollbar.scrub))\n    .addTransform(sharedTransforms.addPayloadOptions)\n    .addTransform(sharedTransforms.userTransform(logger))\n    .addTransform(sharedTransforms.addConfiguredOptions)\n    .addTransform(sharedTransforms.addDiagnosticKeys)\n    .addTransform(sharedTransforms.itemToPayload);\n}\n\nfunction addPredicatesToQueue(queue) {\n  queue\n    .addPredicate(sharedPredicates.checkLevel)\n    .addPredicate(predicates.checkIgnore)\n    .addPredicate(sharedPredicates.userCheckIgnore(logger))\n    .addPredicate(sharedPredicates.urlIsNotBlockListed(logger))\n    .addPredicate(sharedPredicates.urlIsSafeListed(logger))\n    .addPredicate(sharedPredicates.messageIsIgnored(logger));\n}\n\nRollbar.prototype.loadFull = function () {\n  logger.info(\n    'Unexpected Rollbar.loadFull() called on a Notifier instance. This can happen when Rollbar is loaded multiple times.',\n  );\n};\n\nRollbar.prototype._createItem = function (args) {\n  return _.createItem(args, logger, this);\n};\n\nfunction _getFirstFunction(args) {\n  for (var i = 0, len = args.length; i < len; ++i) {\n    if (_.isFunction(args[i])) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\nfunction _gWindow() {\n  return (\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self)\n  );\n}\n\nvar defaults = require('../defaults');\nvar scrubFields = require('./defaults/scrubFields');\n\nvar defaultOptions = {\n  version: defaults.version,\n  scrubFields: scrubFields.scrubFields,\n  logLevel: defaults.logLevel,\n  reportLevel: defaults.reportLevel,\n  uncaughtErrorLevel: defaults.uncaughtErrorLevel,\n  endpoint: defaults.endpoint,\n  verbose: false,\n  enabled: true,\n  transmit: true,\n  sendConfig: false,\n  includeItemsInTelemetry: true,\n  captureIp: true,\n  inspectAnonymousErrors: true,\n  ignoreDuplicateErrors: true,\n  wrapGlobalEventHandlers: false,\n};\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nmodule.exports = {\n  scrubFields: [\n    'pw',\n    'pass',\n    'passwd',\n    'password',\n    'secret',\n    'confirm_password',\n    'confirmPassword',\n    'password_confirmation',\n    'passwordConfirmation',\n    'access_token',\n    'accessToken',\n    'X-Rollbar-Access-Token',\n    'secret_key',\n    'secretKey',\n    'secretToken',\n    'cc-number',\n    'card number',\n    'cardnumber',\n    'cardnum',\n    'ccnum',\n    'ccnumber',\n    'cc num',\n    'creditcardnumber',\n    'credit card number',\n    'newcreditcardnumber',\n    'new credit card',\n    'creditcardno',\n    'credit card no',\n    'card#',\n    'card #',\n    'cc-csc',\n    'cvc',\n    'cvc2',\n    'cvv2',\n    'ccv2',\n    'security code',\n    'card verification',\n    'name on credit card',\n    'name on card',\n    'nameoncard',\n    'cardholder',\n    'card holder',\n    'name des karteninhabers',\n    'ccname',\n    'card type',\n    'cardtype',\n    'cc type',\n    'cctype',\n    'payment type',\n    'expiration date',\n    'expirationdate',\n    'expdate',\n    'cc-exp',\n    'ccmonth',\n    'ccyear',\n  ],\n};\n", "'use strict';\n\n// This detection.js module is used to encapsulate any ugly browser/feature\n// detection we may need to do.\n\n// Figure out which version of IE we're using, if any.\n// This is gleaned from http://stackoverflow.com/questions/5574842/best-way-to-check-for-ie-less-than-9-in-javascript-without-library\n// Will return an integer on IE (i.e. 8)\n// Will return undefined otherwise\nfunction getIEVersion() {\n  var undef;\n  if (typeof document === 'undefined') {\n    return undef;\n  }\n\n  var v = 3,\n    div = document.createElement('div'),\n    all = div.getElementsByTagName('i');\n\n  while (\n    ((div.innerHTML = '<!--[if gt IE ' + ++v + ']><i></i><![endif]-->'), all[0])\n  );\n\n  return v > 4 ? v : undef;\n}\n\nvar Detection = {\n  ieVersion: getIEVersion,\n};\n\nmodule.exports = Detection;\n", "'use strict';\n\nfunction getElementType(e) {\n  return (e.getAttribute('type') || '').toLowerCase();\n}\n\nfunction isDescribedElement(element, type, subtypes) {\n  if (element.tagName.toLowerCase() !== type.toLowerCase()) {\n    return false;\n  }\n  if (!subtypes) {\n    return true;\n  }\n  element = getElementType(element);\n  for (var i = 0; i < subtypes.length; i++) {\n    if (subtypes[i] === element) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getElementFromEvent(evt, doc) {\n  if (evt.target) {\n    return evt.target;\n  }\n  if (doc && doc.elementFromPoint) {\n    return doc.elementFromPoint(evt.clientX, evt.clientY);\n  }\n  return undefined;\n}\n\nfunction treeToArray(elem) {\n  var MAX_HEIGHT = 5;\n  var out = [];\n  var nextDescription;\n  for (var height = 0; elem && height < MAX_HEIGHT; height++) {\n    nextDescription = describeElement(elem);\n    if (nextDescription.tagName === 'html') {\n      break;\n    }\n    out.unshift(nextDescription);\n    elem = elem.parentNode;\n  }\n  return out;\n}\n\nfunction elementArrayToString(a) {\n  var MAX_LENGTH = 80;\n  var separator = ' > ',\n    separatorLength = separator.length;\n  var out = [],\n    len = 0,\n    nextStr,\n    totalLength;\n\n  for (var i = a.length - 1; i >= 0; i--) {\n    nextStr = descriptionToString(a[i]);\n    totalLength = len + out.length * separatorLength + nextStr.length;\n    if (i < a.length - 1 && totalLength >= MAX_LENGTH + 3) {\n      out.unshift('...');\n      break;\n    }\n    out.unshift(nextStr);\n    len += nextStr.length;\n  }\n  return out.join(separator);\n}\n\nfunction descriptionToString(desc) {\n  if (!desc || !desc.tagName) {\n    return '';\n  }\n  var out = [desc.tagName];\n  if (desc.id) {\n    out.push('#' + desc.id);\n  }\n  if (desc.classes) {\n    out.push('.' + desc.classes.join('.'));\n  }\n  for (var i = 0; i < desc.attributes.length; i++) {\n    out.push(\n      '[' + desc.attributes[i].key + '=\"' + desc.attributes[i].value + '\"]',\n    );\n  }\n\n  return out.join('');\n}\n\n/**\n * Input: a dom element\n * Output: null if tagName is falsey or input is falsey, else\n *  {\n *    tagName: String,\n *    id: String | undefined,\n *    classes: [String] | undefined,\n *    attributes: [\n *      {\n *        key: OneOf(type, name, title, alt),\n *        value: String\n *      }\n *    ]\n *  }\n */\nfunction describeElement(elem) {\n  if (!elem || !elem.tagName) {\n    return null;\n  }\n  var out = {},\n    className,\n    key,\n    attr,\n    i;\n  out.tagName = elem.tagName.toLowerCase();\n  if (elem.id) {\n    out.id = elem.id;\n  }\n  className = elem.className;\n  if (className && typeof className === 'string') {\n    out.classes = className.split(/\\s+/);\n  }\n  var attributes = ['type', 'name', 'title', 'alt'];\n  out.attributes = [];\n  for (i = 0; i < attributes.length; i++) {\n    key = attributes[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.attributes.push({ key: key, value: attr });\n    }\n  }\n  return out;\n}\n\nmodule.exports = {\n  describeElement: describeElement,\n  descriptionToString: descriptionToString,\n  elementArrayToString: elementArrayToString,\n  treeToArray: treeToArray,\n  getElementFromEvent: getElementFromEvent,\n  isDescribedElement: isDescribedElement,\n  getElementType: getElementType,\n};\n", "'use strict';\n\nfunction captureUncaughtExceptions(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  var oldOnError;\n\n  if (typeof handler._rollbarOldOnError === 'function') {\n    oldOnError = handler._rollbarOldOnError;\n  } else if (window.onerror) {\n    oldOnError = window.onerror;\n    while (oldOnError._rollbarOldOnError) {\n      oldOnError = oldOnError._rollbarOldOnError;\n    }\n    handler._rollbarOldOnError = oldOnError;\n  }\n\n  handler.handleAnonymousErrors();\n\n  var fn = function () {\n    var args = Array.prototype.slice.call(arguments, 0);\n    _rollbarWindowOnError(window, handler, oldOnError, args);\n  };\n  if (shim) {\n    fn._rollbarOldOnError = oldOnError;\n  }\n  window.onerror = fn;\n}\n\nfunction _rollbarWindowOnError(window, r, old, args) {\n  if (window._rollbarWrappedError) {\n    if (!args[4]) {\n      args[4] = window._rollbarWrappedError;\n    }\n    if (!args[5]) {\n      args[5] = window._rollbarWrappedError._rollbarContext;\n    }\n    window._rollbarWrappedError = null;\n  }\n\n  var ret = r.handleUncaughtException.apply(r, args);\n\n  if (old) {\n    old.apply(window, args);\n  }\n\n  // Let other chained onerror handlers above run before setting this.\n  // If an error is thrown and caught within a chained onerror handler,\n  // Error.prepareStackTrace() will see that one before the one we want.\n  if (ret === 'anonymous') {\n    r.anonymousErrorsPending += 1; // See Rollbar.prototype.handleAnonymousErrors()\n  }\n}\n\nfunction captureUnhandledRejections(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n\n  if (\n    typeof window._rollbarURH === 'function' &&\n    window._rollbarURH.belongsToShim\n  ) {\n    window.removeEventListener('unhandledrejection', window._rollbarURH);\n  }\n\n  var rejectionHandler = function (evt) {\n    var reason, promise, detail;\n    try {\n      reason = evt.reason;\n    } catch (e) {\n      reason = undefined;\n    }\n    try {\n      promise = evt.promise;\n    } catch (e) {\n      promise = '[unhandledrejection] error getting `promise` from event';\n    }\n    try {\n      detail = evt.detail;\n      if (!reason && detail) {\n        reason = detail.reason;\n        promise = detail.promise;\n      }\n    } catch (e) {\n      // Ignore\n    }\n    if (!reason) {\n      reason = '[unhandledrejection] error getting `reason` from event';\n    }\n\n    if (handler && handler.handleUnhandledRejection) {\n      handler.handleUnhandledRejection(reason, promise);\n    }\n  };\n  rejectionHandler.belongsToShim = shim;\n  window._rollbarURH = rejectionHandler;\n  window.addEventListener('unhandledrejection', rejectionHandler);\n}\n\nmodule.exports = {\n  captureUncaughtExceptions: captureUncaughtExceptions,\n  captureUnhandledRejections: captureUnhandledRejections,\n};\n", "'use strict';\n\n/* eslint-disable no-console */\nrequire('console-polyfill');\nvar detection = require('./detection');\nvar _ = require('../utility');\n\nfunction error() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.error(_.formatArgsAsString(args));\n  } else {\n    console.error.apply(console, args);\n  }\n}\n\nfunction info() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.info(_.formatArgsAsString(args));\n  } else {\n    console.info.apply(console, args);\n  }\n}\n\nfunction log() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.log(_.formatArgsAsString(args));\n  } else {\n    console.log.apply(console, args);\n  }\n}\n\n/* eslint-enable no-console */\n\nmodule.exports = {\n  error: error,\n  info: info,\n  log: log,\n};\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction checkIgnore(item, settings) {\n  if (_.get(settings, 'plugins.jquery.ignoreAjaxErrors')) {\n    return !_.get(item, 'body.message.extra.isAjax');\n  }\n  return true;\n}\n\nmodule.exports = {\n  checkIgnore: checkIgnore,\n};\n", "'use strict';\n\nvar Rollbar = require('./core');\nvar telemeter = require('../telemetry');\nvar instrumenter = require('./telemetry');\nvar polyfillJSON = require('../utility/polyfillJSON');\nvar wrapGlobals = require('./wrapGlobals');\nvar scrub = require('../scrub');\nvar truncation = require('../truncation');\n\nRollbar.setComponents({\n  telemeter: telemeter,\n  instrumenter: instrumenter,\n  polyfillJSON: polyfillJSON,\n  wrapGlobals: wrapGlobals,\n  scrub: scrub,\n  truncation: truncation,\n});\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('../utility');\nvar headers = require('../utility/headers');\nvar replace = require('../utility/replace');\nvar scrub = require('../scrub');\nvar urlparser = require('./url');\nvar domUtil = require('./domUtility');\n\nvar defaults = {\n  network: true,\n  networkResponseHeaders: false,\n  networkResponseBody: false,\n  networkRequestHeaders: false,\n  networkRequestBody: false,\n  networkErrorOnHttp5xx: false,\n  networkErrorOnHttp4xx: false,\n  networkErrorOnHttp0: false,\n  log: true,\n  dom: true,\n  navigation: true,\n  connectivity: true,\n  contentSecurityPolicy: true,\n  errorOnContentSecurityPolicy: false,\n};\n\nfunction restore(replacements, type) {\n  var b;\n  while (replacements[type].length) {\n    b = replacements[type].shift();\n    b[0][b[1]] = b[2];\n  }\n}\n\nfunction nameFromDescription(description) {\n  if (!description || !description.attributes) {\n    return null;\n  }\n  var attrs = description.attributes;\n  for (var a = 0; a < attrs.length; ++a) {\n    if (attrs[a].key === 'name') {\n      return attrs[a].value;\n    }\n  }\n  return null;\n}\n\nfunction defaultValueScrubber(scrubFields) {\n  var patterns = [];\n  for (var i = 0; i < scrubFields.length; ++i) {\n    patterns.push(new RegExp(scrubFields[i], 'i'));\n  }\n  return function (description) {\n    var name = nameFromDescription(description);\n    if (!name) {\n      return false;\n    }\n    for (var i = 0; i < patterns.length; ++i) {\n      if (patterns[i].test(name)) {\n        return true;\n      }\n    }\n    return false;\n  };\n}\n\nfunction Instrumenter(options, telemeter, rollbar, _window, _document) {\n  this.options = options;\n  var autoInstrument = options.autoInstrument;\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  this.telemetryScrubber = options.telemetryScrubber;\n  this.defaultValueScrubber = defaultValueScrubber(options.scrubFields);\n  this.telemeter = telemeter;\n  this.rollbar = rollbar;\n  this.diagnostic = rollbar.client.notifier.diagnostic;\n  this._window = _window || {};\n  this._document = _document || {};\n  this.replacements = {\n    network: [],\n    log: [],\n    navigation: [],\n    connectivity: [],\n  };\n  this.eventRemovers = {\n    dom: [],\n    connectivity: [],\n    contentsecuritypolicy: [],\n  };\n\n  this._location = this._window.location;\n  this._lastHref = this._location && this._location.href;\n}\n\nInstrumenter.prototype.configure = function (options) {\n  this.options = _.merge(this.options, options);\n  var autoInstrument = options.autoInstrument;\n  var oldSettings = _.merge(this.autoInstrument);\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.instrument(oldSettings);\n  if (options.scrubTelemetryInputs !== undefined) {\n    this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  }\n  if (options.telemetryScrubber !== undefined) {\n    this.telemetryScrubber = options.telemetryScrubber;\n  }\n};\n\n// eslint-disable-next-line complexity\nInstrumenter.prototype.instrument = function (oldSettings) {\n  if (this.autoInstrument.network && !(oldSettings && oldSettings.network)) {\n    this.instrumentNetwork();\n  } else if (\n    !this.autoInstrument.network &&\n    oldSettings &&\n    oldSettings.network\n  ) {\n    this.deinstrumentNetwork();\n  }\n\n  if (this.autoInstrument.log && !(oldSettings && oldSettings.log)) {\n    this.instrumentConsole();\n  } else if (!this.autoInstrument.log && oldSettings && oldSettings.log) {\n    this.deinstrumentConsole();\n  }\n\n  if (this.autoInstrument.dom && !(oldSettings && oldSettings.dom)) {\n    this.instrumentDom();\n  } else if (!this.autoInstrument.dom && oldSettings && oldSettings.dom) {\n    this.deinstrumentDom();\n  }\n\n  if (\n    this.autoInstrument.navigation &&\n    !(oldSettings && oldSettings.navigation)\n  ) {\n    this.instrumentNavigation();\n  } else if (\n    !this.autoInstrument.navigation &&\n    oldSettings &&\n    oldSettings.navigation\n  ) {\n    this.deinstrumentNavigation();\n  }\n\n  if (\n    this.autoInstrument.connectivity &&\n    !(oldSettings && oldSettings.connectivity)\n  ) {\n    this.instrumentConnectivity();\n  } else if (\n    !this.autoInstrument.connectivity &&\n    oldSettings &&\n    oldSettings.connectivity\n  ) {\n    this.deinstrumentConnectivity();\n  }\n\n  if (\n    this.autoInstrument.contentSecurityPolicy &&\n    !(oldSettings && oldSettings.contentSecurityPolicy)\n  ) {\n    this.instrumentContentSecurityPolicy();\n  } else if (\n    !this.autoInstrument.contentSecurityPolicy &&\n    oldSettings &&\n    oldSettings.contentSecurityPolicy\n  ) {\n    this.deinstrumentContentSecurityPolicy();\n  }\n};\n\nInstrumenter.prototype.deinstrumentNetwork = function () {\n  restore(this.replacements, 'network');\n};\n\nInstrumenter.prototype.instrumentNetwork = function () {\n  var self = this;\n\n  function wrapProp(prop, xhr) {\n    if (prop in xhr && _.isFunction(xhr[prop])) {\n      replace(xhr, prop, function (orig) {\n        return self.rollbar.wrap(orig);\n      });\n    }\n  }\n\n  if ('XMLHttpRequest' in this._window) {\n    var xhrp = this._window.XMLHttpRequest.prototype;\n    replace(\n      xhrp,\n      'open',\n      function (orig) {\n        return function (method, url) {\n          var isUrlObject = _isUrlObject(url);\n          if (_.isType(url, 'string') || isUrlObject) {\n            url = isUrlObject ? url.toString() : url;\n            if (this.__rollbar_xhr) {\n              this.__rollbar_xhr.method = method;\n              this.__rollbar_xhr.url = url;\n              this.__rollbar_xhr.status_code = null;\n              this.__rollbar_xhr.start_time_ms = _.now();\n              this.__rollbar_xhr.end_time_ms = null;\n            } else {\n              this.__rollbar_xhr = {\n                method: method,\n                url: url,\n                status_code: null,\n                start_time_ms: _.now(),\n                end_time_ms: null,\n              };\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'setRequestHeader',\n      function (orig) {\n        return function (header, value) {\n          // If xhr.open is async, __rollbar_xhr may not be initialized yet.\n          if (!this.__rollbar_xhr) {\n            this.__rollbar_xhr = {};\n          }\n          if (_.isType(header, 'string') && _.isType(value, 'string')) {\n            if (self.autoInstrument.networkRequestHeaders) {\n              if (!this.__rollbar_xhr.request_headers) {\n                this.__rollbar_xhr.request_headers = {};\n              }\n              this.__rollbar_xhr.request_headers[header] = value;\n            }\n            // We want the content type even if request header telemetry is off.\n            if (header.toLowerCase() === 'content-type') {\n              this.__rollbar_xhr.request_content_type = value;\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'send',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (data) {\n          /* eslint-enable no-unused-vars */\n          var xhr = this;\n\n          function onreadystatechangeHandler() {\n            if (xhr.__rollbar_xhr) {\n              if (xhr.__rollbar_xhr.status_code === null) {\n                xhr.__rollbar_xhr.status_code = 0;\n                if (self.autoInstrument.networkRequestBody) {\n                  xhr.__rollbar_xhr.request = data;\n                }\n                xhr.__rollbar_event = self.captureNetwork(\n                  xhr.__rollbar_xhr,\n                  'xhr',\n                  undefined,\n                );\n              }\n              if (xhr.readyState < 2) {\n                xhr.__rollbar_xhr.start_time_ms = _.now();\n              }\n              if (xhr.readyState > 3) {\n                xhr.__rollbar_xhr.end_time_ms = _.now();\n\n                var headers = null;\n                xhr.__rollbar_xhr.response_content_type =\n                  xhr.getResponseHeader('Content-Type');\n                if (self.autoInstrument.networkResponseHeaders) {\n                  var headersConfig =\n                    self.autoInstrument.networkResponseHeaders;\n                  headers = {};\n                  try {\n                    var header, i;\n                    if (headersConfig === true) {\n                      var allHeaders = xhr.getAllResponseHeaders();\n                      if (allHeaders) {\n                        var arr = allHeaders.trim().split(/[\\r\\n]+/);\n                        var parts, value;\n                        for (i = 0; i < arr.length; i++) {\n                          parts = arr[i].split(': ');\n                          header = parts.shift();\n                          value = parts.join(': ');\n                          headers[header] = value;\n                        }\n                      }\n                    } else {\n                      for (i = 0; i < headersConfig.length; i++) {\n                        header = headersConfig[i];\n                        headers[header] = xhr.getResponseHeader(header);\n                      }\n                    }\n                  } catch (e) {\n                    /* we ignore the errors here that could come from different\n                     * browser issues with the xhr methods */\n                  }\n                }\n                var body = null;\n                if (self.autoInstrument.networkResponseBody) {\n                  try {\n                    body = xhr.responseText;\n                  } catch (e) {\n                    /* ignore errors from reading responseText */\n                  }\n                }\n                var response = null;\n                if (body || headers) {\n                  response = {};\n                  if (body) {\n                    if (\n                      self.isJsonContentType(\n                        xhr.__rollbar_xhr.response_content_type,\n                      )\n                    ) {\n                      response.body = self.scrubJson(body);\n                    } else {\n                      response.body = body;\n                    }\n                  }\n                  if (headers) {\n                    response.headers = headers;\n                  }\n                }\n                if (response) {\n                  xhr.__rollbar_xhr.response = response;\n                }\n                try {\n                  var code = xhr.status;\n                  code = code === 1223 ? 204 : code;\n                  xhr.__rollbar_xhr.status_code = code;\n                  xhr.__rollbar_event.level =\n                    self.telemeter.levelFromStatus(code);\n                  self.errorOnHttpStatus(xhr.__rollbar_xhr);\n                } catch (e) {\n                  /* ignore possible exception from xhr.status */\n                }\n              }\n            }\n          }\n\n          wrapProp('onload', xhr);\n          wrapProp('onerror', xhr);\n          wrapProp('onprogress', xhr);\n\n          if (\n            'onreadystatechange' in xhr &&\n            _.isFunction(xhr.onreadystatechange)\n          ) {\n            replace(xhr, 'onreadystatechange', function (orig) {\n              return self.rollbar.wrap(\n                orig,\n                undefined,\n                onreadystatechangeHandler,\n              );\n            });\n          } else {\n            xhr.onreadystatechange = onreadystatechangeHandler;\n          }\n          if (xhr.__rollbar_xhr && self.trackHttpErrors()) {\n            xhr.__rollbar_xhr.stack = new Error().stack;\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n\n  if ('fetch' in this._window) {\n    replace(\n      this._window,\n      'fetch',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (fn, t) {\n          /* eslint-enable no-unused-vars */\n          var args = new Array(arguments.length);\n          for (var i = 0, len = args.length; i < len; i++) {\n            args[i] = arguments[i];\n          }\n          var input = args[0];\n          var method = 'GET';\n          var url;\n          var isUrlObject = _isUrlObject(input);\n          if (_.isType(input, 'string') || isUrlObject) {\n            url = isUrlObject ? input.toString() : input;\n          } else if (input) {\n            url = input.url;\n            if (input.method) {\n              method = input.method;\n            }\n          }\n          if (args[1] && args[1].method) {\n            method = args[1].method;\n          }\n          var metadata = {\n            method: method,\n            url: url,\n            status_code: null,\n            start_time_ms: _.now(),\n            end_time_ms: null,\n          };\n          if (args[1] && args[1].headers) {\n            // Argument may be a Headers object, or plain object. Ensure here that\n            // we are working with a Headers object with case-insensitive keys.\n            var reqHeaders = headers(args[1].headers);\n\n            metadata.request_content_type = reqHeaders.get('Content-Type');\n\n            if (self.autoInstrument.networkRequestHeaders) {\n              metadata.request_headers = self.fetchHeaders(\n                reqHeaders,\n                self.autoInstrument.networkRequestHeaders,\n              );\n            }\n          }\n\n          if (self.autoInstrument.networkRequestBody) {\n            if (args[1] && args[1].body) {\n              metadata.request = args[1].body;\n            } else if (\n              args[0] &&\n              !_.isType(args[0], 'string') &&\n              args[0].body\n            ) {\n              metadata.request = args[0].body;\n            }\n          }\n          self.captureNetwork(metadata, 'fetch', undefined);\n          if (self.trackHttpErrors()) {\n            metadata.stack = new Error().stack;\n          }\n\n          // Start our handler before returning the promise. This allows resp.clone()\n          // to execute before other handlers touch the response.\n          return orig.apply(this, args).then(function (resp) {\n            metadata.end_time_ms = _.now();\n            metadata.status_code = resp.status;\n            metadata.response_content_type = resp.headers.get('Content-Type');\n            var headers = null;\n            if (self.autoInstrument.networkResponseHeaders) {\n              headers = self.fetchHeaders(\n                resp.headers,\n                self.autoInstrument.networkResponseHeaders,\n              );\n            }\n            var body = null;\n            if (self.autoInstrument.networkResponseBody) {\n              if (typeof resp.text === 'function') {\n                // Response.text() is not implemented on some platforms\n                // The response must be cloned to prevent reading (and locking) the original stream.\n                // This must be done before other handlers touch the response.\n                body = resp.clone().text(); //returns a Promise\n              }\n            }\n            if (headers || body) {\n              metadata.response = {};\n              if (body) {\n                // Test to ensure body is a Promise, which it should always be.\n                if (typeof body.then === 'function') {\n                  body.then(function (text) {\n                    if (\n                      text &&\n                      self.isJsonContentType(metadata.response_content_type)\n                    ) {\n                      metadata.response.body = self.scrubJson(text);\n                    } else {\n                      metadata.response.body = text;\n                    }\n                  });\n                } else {\n                  metadata.response.body = body;\n                }\n              }\n              if (headers) {\n                metadata.response.headers = headers;\n              }\n            }\n            self.errorOnHttpStatus(metadata);\n            return resp;\n          });\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n};\n\nInstrumenter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n) {\n  if (\n    metadata.request &&\n    this.isJsonContentType(metadata.request_content_type)\n  ) {\n    metadata.request = this.scrubJson(metadata.request);\n  }\n  return this.telemeter.captureNetwork(metadata, subtype, rollbarUUID);\n};\n\nInstrumenter.prototype.isJsonContentType = function (contentType) {\n  return contentType &&\n    _.isType(contentType, 'string') &&\n    contentType.toLowerCase().includes('json')\n    ? true\n    : false;\n};\n\nInstrumenter.prototype.scrubJson = function (json) {\n  return JSON.stringify(scrub(JSON.parse(json), this.options.scrubFields));\n};\n\nInstrumenter.prototype.fetchHeaders = function (inHeaders, headersConfig) {\n  var outHeaders = {};\n  try {\n    var i;\n    if (headersConfig === true) {\n      if (typeof inHeaders.entries === 'function') {\n        // Headers.entries() is not implemented in IE\n        var allHeaders = inHeaders.entries();\n        var currentHeader = allHeaders.next();\n        while (!currentHeader.done) {\n          outHeaders[currentHeader.value[0]] = currentHeader.value[1];\n          currentHeader = allHeaders.next();\n        }\n      }\n    } else {\n      for (i = 0; i < headersConfig.length; i++) {\n        var header = headersConfig[i];\n        outHeaders[header] = inHeaders.get(header);\n      }\n    }\n  } catch (e) {\n    /* ignore probable IE errors */\n  }\n  return outHeaders;\n};\n\nInstrumenter.prototype.trackHttpErrors = function () {\n  return (\n    this.autoInstrument.networkErrorOnHttp5xx ||\n    this.autoInstrument.networkErrorOnHttp4xx ||\n    this.autoInstrument.networkErrorOnHttp0\n  );\n};\n\nInstrumenter.prototype.errorOnHttpStatus = function (metadata) {\n  var status = metadata.status_code;\n\n  if (\n    (status >= 500 && this.autoInstrument.networkErrorOnHttp5xx) ||\n    (status >= 400 && this.autoInstrument.networkErrorOnHttp4xx) ||\n    (status === 0 && this.autoInstrument.networkErrorOnHttp0)\n  ) {\n    var error = new Error('HTTP request failed with Status ' + status);\n    error.stack = metadata.stack;\n    this.rollbar.error(error, { skipFrames: 1 });\n  }\n};\n\nInstrumenter.prototype.deinstrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n  var b;\n  while (this.replacements['log'].length) {\n    b = this.replacements['log'].shift();\n    this._window.console[b[0]] = b[1];\n  }\n};\n\nInstrumenter.prototype.instrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n\n  var self = this;\n  var c = this._window.console;\n\n  function wrapConsole(method) {\n    'use strict'; // See https://github.com/rollbar/rollbar.js/pull/778\n\n    var orig = c[method];\n    var origConsole = c;\n    var level = method === 'warn' ? 'warning' : method;\n    c[method] = function () {\n      var args = Array.prototype.slice.call(arguments);\n      var message = _.formatArgsAsString(args);\n      self.telemeter.captureLog(message, level);\n      if (orig) {\n        Function.prototype.apply.call(orig, origConsole, args);\n      }\n    };\n    self.replacements['log'].push([method, orig]);\n  }\n  var methods = ['debug', 'info', 'warn', 'error', 'log'];\n  try {\n    for (var i = 0, len = methods.length; i < len; i++) {\n      wrapConsole(methods[i]);\n    }\n  } catch (e) {\n    this.diagnostic.instrumentConsole = { error: e.message };\n  }\n};\n\nInstrumenter.prototype.deinstrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  this.removeListeners('dom');\n};\n\nInstrumenter.prototype.instrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  var clickHandler = this.handleClick.bind(this);\n  var blurHandler = this.handleBlur.bind(this);\n  this.addListener('dom', this._window, 'click', 'onclick', clickHandler, true);\n  this.addListener(\n    'dom',\n    this._window,\n    'blur',\n    'onfocusout',\n    blurHandler,\n    true,\n  );\n};\n\nInstrumenter.prototype.handleClick = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    var hasTag = e && e.tagName;\n    var anchorOrButton =\n      domUtil.isDescribedElement(e, 'a') ||\n      domUtil.isDescribedElement(e, 'button');\n    if (\n      hasTag &&\n      (anchorOrButton ||\n        domUtil.isDescribedElement(e, 'input', ['button', 'submit']))\n    ) {\n      this.captureDomEvent('click', e);\n    } else if (domUtil.isDescribedElement(e, 'input', ['checkbox', 'radio'])) {\n      this.captureDomEvent('input', e, e.value, e.checked);\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleBlur = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    if (e && e.tagName) {\n      if (domUtil.isDescribedElement(e, 'textarea')) {\n        this.captureDomEvent('input', e, e.value);\n      } else if (\n        domUtil.isDescribedElement(e, 'select') &&\n        e.options &&\n        e.options.length\n      ) {\n        this.handleSelectInputChanged(e);\n      } else if (\n        domUtil.isDescribedElement(e, 'input') &&\n        !domUtil.isDescribedElement(e, 'input', [\n          'button',\n          'submit',\n          'hidden',\n          'checkbox',\n          'radio',\n        ])\n      ) {\n        this.captureDomEvent('input', e, e.value);\n      }\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleSelectInputChanged = function (elem) {\n  if (elem.multiple) {\n    for (var i = 0; i < elem.options.length; i++) {\n      if (elem.options[i].selected) {\n        this.captureDomEvent('input', elem, elem.options[i].value);\n      }\n    }\n  } else if (elem.selectedIndex >= 0 && elem.options[elem.selectedIndex]) {\n    this.captureDomEvent('input', elem, elem.options[elem.selectedIndex].value);\n  }\n};\n\nInstrumenter.prototype.captureDomEvent = function (\n  subtype,\n  element,\n  value,\n  isChecked,\n) {\n  if (value !== undefined) {\n    if (\n      this.scrubTelemetryInputs ||\n      domUtil.getElementType(element) === 'password'\n    ) {\n      value = '[scrubbed]';\n    } else {\n      var description = domUtil.describeElement(element);\n      if (this.telemetryScrubber) {\n        if (this.telemetryScrubber(description)) {\n          value = '[scrubbed]';\n        }\n      } else if (this.defaultValueScrubber(description)) {\n        value = '[scrubbed]';\n      }\n    }\n  }\n  var elementString = domUtil.elementArrayToString(\n    domUtil.treeToArray(element),\n  );\n  this.telemeter.captureDom(subtype, elementString, value, isChecked);\n};\n\nInstrumenter.prototype.deinstrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  restore(this.replacements, 'navigation');\n};\n\nInstrumenter.prototype.instrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  var self = this;\n  replace(\n    this._window,\n    'onpopstate',\n    function (orig) {\n      return function () {\n        var current = self._location.href;\n        self.handleUrlChange(self._lastHref, current);\n        if (orig) {\n          orig.apply(this, arguments);\n        }\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n\n  replace(\n    this._window.history,\n    'pushState',\n    function (orig) {\n      return function () {\n        var url = arguments.length > 2 ? arguments[2] : undefined;\n        if (url) {\n          self.handleUrlChange(self._lastHref, url + '');\n        }\n        return orig.apply(this, arguments);\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n};\n\nInstrumenter.prototype.handleUrlChange = function (from, to) {\n  var parsedHref = urlparser.parse(this._location.href);\n  var parsedTo = urlparser.parse(to);\n  var parsedFrom = urlparser.parse(from);\n  this._lastHref = to;\n  if (\n    parsedHref.protocol === parsedTo.protocol &&\n    parsedHref.host === parsedTo.host\n  ) {\n    to = parsedTo.path + (parsedTo.hash || '');\n  }\n  if (\n    parsedHref.protocol === parsedFrom.protocol &&\n    parsedHref.host === parsedFrom.host\n  ) {\n    from = parsedFrom.path + (parsedFrom.hash || '');\n  }\n  this.telemeter.captureNavigation(from, to);\n};\n\nInstrumenter.prototype.deinstrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.removeListeners('connectivity');\n  } else {\n    restore(this.replacements, 'connectivity');\n  }\n};\n\nInstrumenter.prototype.instrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.addListener(\n      'connectivity',\n      this._window,\n      'online',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('online');\n      }.bind(this),\n      true,\n    );\n    this.addListener(\n      'connectivity',\n      this._window,\n      'offline',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('offline');\n      }.bind(this),\n      true,\n    );\n  } else {\n    var self = this;\n    replace(\n      this._document.body,\n      'ononline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('online');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n    replace(\n      this._document.body,\n      'onoffline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('offline');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n  }\n};\n\nInstrumenter.prototype.handleCspEvent = function (cspEvent) {\n  var message =\n    'Security Policy Violation: ' +\n    'blockedURI: ' +\n    cspEvent.blockedURI +\n    ', ' +\n    'violatedDirective: ' +\n    cspEvent.violatedDirective +\n    ', ' +\n    'effectiveDirective: ' +\n    cspEvent.effectiveDirective +\n    ', ';\n\n  if (cspEvent.sourceFile) {\n    message +=\n      'location: ' +\n      cspEvent.sourceFile +\n      ', ' +\n      'line: ' +\n      cspEvent.lineNumber +\n      ', ' +\n      'col: ' +\n      cspEvent.columnNumber +\n      ', ';\n  }\n\n  message += 'originalPolicy: ' + cspEvent.originalPolicy;\n\n  this.telemeter.captureLog(message, 'error');\n  this.handleCspError(message);\n};\n\nInstrumenter.prototype.handleCspError = function (message) {\n  if (this.autoInstrument.errorOnContentSecurityPolicy) {\n    this.rollbar.error(message);\n  }\n};\n\nInstrumenter.prototype.deinstrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  this.removeListeners('contentsecuritypolicy');\n};\n\nInstrumenter.prototype.instrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  var cspHandler = this.handleCspEvent.bind(this);\n  this.addListener(\n    'contentsecuritypolicy',\n    this._document,\n    'securitypolicyviolation',\n    null,\n    cspHandler,\n    false,\n  );\n};\n\nInstrumenter.prototype.addListener = function (\n  section,\n  obj,\n  type,\n  altType,\n  handler,\n  capture,\n) {\n  if (obj.addEventListener) {\n    obj.addEventListener(type, handler, capture);\n    this.eventRemovers[section].push(function () {\n      obj.removeEventListener(type, handler, capture);\n    });\n  } else if (altType) {\n    obj.attachEvent(altType, handler);\n    this.eventRemovers[section].push(function () {\n      obj.detachEvent(altType, handler);\n    });\n  }\n};\n\nInstrumenter.prototype.removeListeners = function (section) {\n  var r;\n  while (this.eventRemovers[section].length) {\n    r = this.eventRemovers[section].shift();\n    r();\n  }\n};\n\nfunction _isUrlObject(input) {\n  return typeof URL !== 'undefined' && input instanceof URL;\n}\n\nmodule.exports = Instrumenter;\n", "'use strict';\n\nvar _ = require('../utility');\nvar errorParser = require('../errorParser');\nvar logger = require('./logger');\n\nfunction handleDomException(item, options, callback) {\n  if (item.err && errorParser.Stack(item.err).name === 'DOMException') {\n    var originalError = new Error();\n    originalError.name = item.err.name;\n    originalError.message = item.err.message;\n    originalError.stack = item.err.stack;\n    originalError.nested = item.err;\n    item.err = originalError;\n  }\n  callback(null, item);\n}\n\nfunction handleItemWithError(item, options, callback) {\n  item.data = item.data || {};\n  if (item.err) {\n    try {\n      item.stackInfo =\n        item.err._savedStackTrace ||\n        errorParser.parse(item.err, item.skipFrames);\n\n      if (options.addErrorContext) {\n        addErrorContext(item);\n      }\n    } catch (e) {\n      logger.error('Error while parsing the error object.', e);\n      try {\n        item.message =\n          item.err.message ||\n          item.err.description ||\n          item.message ||\n          String(item.err);\n      } catch (e2) {\n        item.message = String(item.err) || String(e2);\n      }\n      delete item.err;\n    }\n  }\n  callback(null, item);\n}\n\nfunction addErrorContext(item) {\n  var chain = [];\n  var err = item.err;\n\n  chain.push(err);\n\n  while (err.nested || err.cause) {\n    err = err.nested || err.cause;\n    chain.push(err);\n  }\n\n  _.addErrorContext(item, chain);\n}\n\nfunction ensureItemHasSomethingToSay(item, options, callback) {\n  if (!item.message && !item.stackInfo && !item.custom) {\n    callback(new Error('No message, stack info, or custom data'), null);\n  }\n  callback(null, item);\n}\n\nfunction addBaseInfo(item, options, callback) {\n  var environment =\n    (options.payload && options.payload.environment) || options.environment;\n  item.data = _.merge(item.data, {\n    environment: environment,\n    level: item.level,\n    endpoint: options.endpoint,\n    platform: 'browser',\n    framework: 'browser-js',\n    language: 'javascript',\n    server: {},\n    uuid: item.uuid,\n    notifier: {\n      name: 'rollbar-browser-js',\n      version: options.version,\n    },\n    custom: item.custom,\n  });\n  callback(null, item);\n}\n\nfunction addRequestInfo(window) {\n  return function (item, options, callback) {\n    var requestInfo = {};\n\n    if (window && window.location) {\n      requestInfo.url = window.location.href;\n      requestInfo.query_string = window.location.search;\n    }\n\n    var remoteString = '$remote_ip';\n    if (!options.captureIp) {\n      remoteString = null;\n    } else if (options.captureIp !== true) {\n      remoteString += '_anonymize';\n    }\n    if (remoteString) requestInfo.user_ip = remoteString;\n\n    if (Object.keys(requestInfo).length > 0) {\n      _.set(item, 'data.request', requestInfo);\n    }\n\n    callback(null, item);\n  };\n}\n\nfunction addClientInfo(window) {\n  return function (item, options, callback) {\n    if (!window) {\n      return callback(null, item);\n    }\n    var nav = window.navigator || {};\n    var scr = window.screen || {};\n    _.set(item, 'data.client', {\n      runtime_ms: item.timestamp - window._rollbarStartTime,\n      timestamp: Math.round(item.timestamp / 1000),\n      javascript: {\n        browser: nav.userAgent,\n        language: nav.language,\n        cookie_enabled: nav.cookieEnabled,\n        screen: {\n          width: scr.width,\n          height: scr.height,\n        },\n      },\n    });\n    callback(null, item);\n  };\n}\n\nfunction addPluginInfo(window) {\n  return function (item, options, callback) {\n    if (!window || !window.navigator) {\n      return callback(null, item);\n    }\n    var plugins = [];\n    var navPlugins = window.navigator.plugins || [];\n    var cur;\n    for (var i = 0, l = navPlugins.length; i < l; ++i) {\n      cur = navPlugins[i];\n      plugins.push({ name: cur.name, description: cur.description });\n    }\n    _.set(item, 'data.client.javascript.plugins', plugins);\n    callback(null, item);\n  };\n}\n\nfunction addBody(item, options, callback) {\n  if (item.stackInfo) {\n    if (item.stackInfo.traceChain) {\n      addBodyTraceChain(item, options, callback);\n    } else {\n      addBodyTrace(item, options, callback);\n    }\n  } else {\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction addBodyMessage(item, options, callback) {\n  var message = item.message;\n  var custom = item.custom;\n\n  if (!message) {\n    message = 'Item sent with null or missing arguments.';\n  }\n  var result = {\n    body: message,\n  };\n\n  if (custom) {\n    result.extra = _.merge(custom);\n  }\n\n  _.set(item, 'data.body', { message: result });\n  callback(null, item);\n}\n\nfunction stackFromItem(item) {\n  // Transform a TraceKit stackInfo object into a Rollbar trace\n  var stack = item.stackInfo.stack;\n  if (\n    stack &&\n    stack.length === 0 &&\n    item._unhandledStackInfo &&\n    item._unhandledStackInfo.stack\n  ) {\n    stack = item._unhandledStackInfo.stack;\n  }\n  return stack;\n}\n\nfunction addBodyTraceChain(item, options, callback) {\n  var traceChain = item.stackInfo.traceChain;\n  var traces = [];\n\n  var traceChainLength = traceChain.length;\n  for (var i = 0; i < traceChainLength; i++) {\n    var trace = buildTrace(item, traceChain[i], options);\n    traces.push(trace);\n  }\n\n  _.set(item, 'data.body', { trace_chain: traces });\n  callback(null, item);\n}\n\nfunction addBodyTrace(item, options, callback) {\n  var stack = stackFromItem(item);\n\n  if (stack) {\n    var trace = buildTrace(item, item.stackInfo, options);\n    _.set(item, 'data.body', { trace: trace });\n    callback(null, item);\n  } else {\n    var stackInfo = item.stackInfo;\n    var guess = errorParser.guessErrorClass(stackInfo.message);\n    var className = errorClass(stackInfo, guess[0], options);\n    var message = guess[1];\n\n    item.message = className + ': ' + message;\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction buildTrace(item, stackInfo, options) {\n  var description = item && item.data.description;\n  var custom = item && item.custom;\n  var stack = stackFromItem(item);\n\n  var guess = errorParser.guessErrorClass(stackInfo.message);\n  var className = errorClass(stackInfo, guess[0], options);\n  var message = guess[1];\n  var trace = {\n    exception: {\n      class: className,\n      message: message,\n    },\n  };\n\n  if (description) {\n    trace.exception.description = description;\n  }\n\n  if (stack) {\n    if (stack.length === 0) {\n      trace.exception.stack = stackInfo.rawStack;\n      trace.exception.raw = String(stackInfo.rawException);\n    }\n    var stackFrame;\n    var frame;\n    var code;\n    var pre;\n    var post;\n    var contextLength;\n    var i, mid;\n\n    trace.frames = [];\n    for (i = 0; i < stack.length; ++i) {\n      stackFrame = stack[i];\n      frame = {\n        filename: stackFrame.url ? _.sanitizeUrl(stackFrame.url) : '(unknown)',\n        lineno: stackFrame.line || null,\n        method:\n          !stackFrame.func || stackFrame.func === '?'\n            ? '[anonymous]'\n            : stackFrame.func,\n        colno: stackFrame.column,\n      };\n      if (options.sendFrameUrl) {\n        frame.url = stackFrame.url;\n      }\n      if (\n        frame.method &&\n        frame.method.endsWith &&\n        frame.method.endsWith('_rollbar_wrapped')\n      ) {\n        continue;\n      }\n\n      code = pre = post = null;\n      contextLength = stackFrame.context ? stackFrame.context.length : 0;\n      if (contextLength) {\n        mid = Math.floor(contextLength / 2);\n        pre = stackFrame.context.slice(0, mid);\n        code = stackFrame.context[mid];\n        post = stackFrame.context.slice(mid);\n      }\n\n      if (code) {\n        frame.code = code;\n      }\n\n      if (pre || post) {\n        frame.context = {};\n        if (pre && pre.length) {\n          frame.context.pre = pre;\n        }\n        if (post && post.length) {\n          frame.context.post = post;\n        }\n      }\n\n      if (stackFrame.args) {\n        frame.args = stackFrame.args;\n      }\n\n      trace.frames.push(frame);\n    }\n\n    // NOTE(cory): reverse the frames since rollbar.com expects the most recent call last\n    trace.frames.reverse();\n\n    if (custom) {\n      trace.extra = _.merge(custom);\n    }\n  }\n\n  return trace;\n}\n\nfunction errorClass(stackInfo, guess, options) {\n  if (stackInfo.name) {\n    return stackInfo.name;\n  } else if (options.guessErrorClass) {\n    return guess;\n  } else {\n    return '(unknown)';\n  }\n}\n\nfunction addScrubber(scrubFn) {\n  return function (item, options, callback) {\n    if (scrubFn) {\n      var scrubFields = options.scrubFields || [];\n      var scrubPaths = options.scrubPaths || [];\n      item.data = scrubFn(item.data, scrubFields, scrubPaths);\n    }\n    callback(null, item);\n  };\n}\n\nmodule.exports = {\n  handleDomException: handleDomException,\n  handleItemWithError: handleItemWithError,\n  ensureItemHasSomethingToSay: ensureItemHasSomethingToSay,\n  addBaseInfo: addBaseInfo,\n  addRequestInfo: addRequestInfo,\n  addClientInfo: addClientInfo,\n  addPluginInfo: addPluginInfo,\n  addBody: addBody,\n  addScrubber: addScrubber,\n};\n", "'use strict';\n\nvar _ = require('../utility');\nvar makeFetchRequest = require('./transport/fetch');\nvar makeXhrRequest = require('./transport/xhr');\n\n/*\n * accessToken may be embedded in payload but that should not\n *   be assumed\n *\n * options: {\n *   hostname\n *   protocol\n *   path\n *   port\n *   method\n *   transport ('xhr' | 'fetch')\n * }\n *\n *  params is an object containing key/value pairs. These\n *    will be appended to the path as 'key=value&key=value'\n *\n * payload is an unserialized object\n */\nfunction Transport(truncation) {\n  this.truncation = truncation;\n}\n\nTransport.prototype.get = function (\n  accessToken,\n  options,\n  params,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n  _.addParamsAndAccessTokenToPath(accessToken, options, params);\n\n  var method = 'GET';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    null,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.post = function (\n  accessToken,\n  options,\n  payload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!payload) {\n    return callback(new Error('Cannot send empty request'));\n  }\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n  if (stringifyResult.error) {\n    return callback(stringifyResult.error);\n  }\n\n  var writeData = stringifyResult.value;\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    writeData,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.postJsonPayload = function (\n  accessToken,\n  options,\n  jsonPayload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    jsonPayload,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\n// Wraps _makeRequest and if Angular 2+ Zone.js is detected, changes scope\n// so Angular change detection isn't triggered on each API call.\n// This is the equivalent of runOutsideAngular().\n//\nTransport.prototype._makeZoneRequest = function () {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var currentZone = gWindow && gWindow.Zone && gWindow.Zone.current;\n  var args = Array.prototype.slice.call(arguments);\n\n  if (currentZone && currentZone._name === 'angular') {\n    var rootZone = currentZone._parent;\n    var self = this;\n    rootZone.run(function () {\n      self._makeRequest.apply(undefined, args);\n    });\n  } else {\n    this._makeRequest.apply(undefined, args);\n  }\n};\n\nTransport.prototype._makeRequest = function (\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n  transport,\n) {\n  if (typeof RollbarProxy !== 'undefined') {\n    return _proxyRequest(data, callback);\n  }\n\n  if (transport === 'fetch') {\n    makeFetchRequest(accessToken, url, method, data, callback, timeout);\n  } else {\n    makeXhrRequest(\n      accessToken,\n      url,\n      method,\n      data,\n      callback,\n      requestFactory,\n      timeout,\n    );\n  }\n};\n\n/* global RollbarProxy */\nfunction _proxyRequest(json, callback) {\n  var rollbarProxy = new RollbarProxy();\n  rollbarProxy.sendJsonPayload(\n    json,\n    function (_msg) {\n      /* do nothing */\n    }, // eslint-disable-line no-unused-vars\n    function (err) {\n      callback(new Error(err));\n    },\n  );\n}\n\nmodule.exports = Transport;\n", "'use strict';\n\nvar logger = require('../logger');\nvar _ = require('../../utility');\n\nfunction makeFetchRequest(accessToken, url, method, data, callback, timeout) {\n  var controller;\n  var timeoutId;\n\n  if (_.isFiniteNumber(timeout)) {\n    controller = new AbortController();\n    timeoutId = setTimeout(function () {\n      controller.abort();\n    }, timeout);\n  }\n\n  fetch(url, {\n    method: method,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Rollbar-Access-Token': accessToken,\n      signal: controller && controller.signal,\n    },\n    body: data,\n  })\n    .then(function (response) {\n      if (timeoutId) clearTimeout(timeoutId);\n      return response.json();\n    })\n    .then(function (data) {\n      callback(null, data);\n    })\n    .catch(function (error) {\n      logger.error(error.message);\n      callback(error);\n    });\n}\n\nmodule.exports = makeFetchRequest;\n", "'use strict';\n\n/*global XDomainRequest*/\n\nvar _ = require('../../utility');\nvar logger = require('../logger');\n\nfunction makeXhrRequest(\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n) {\n  var request;\n  if (requestFactory) {\n    request = requestFactory();\n  } else {\n    request = _createXMLHTTPObject();\n  }\n  if (!request) {\n    // Give up, no way to send requests\n    return callback(new Error('No way to send a request'));\n  }\n  try {\n    try {\n      var onreadystatechange = function () {\n        try {\n          if (onreadystatechange && request.readyState === 4) {\n            onreadystatechange = undefined;\n\n            var parseResponse = _.jsonParse(request.responseText);\n            if (_isSuccess(request)) {\n              callback(parseResponse.error, parseResponse.value);\n              return;\n            } else if (_isNormalFailure(request)) {\n              if (request.status === 403) {\n                // likely caused by using a server access token\n                var message =\n                  parseResponse.value && parseResponse.value.message;\n                logger.error(message);\n              }\n              // return valid http status codes\n              callback(new Error(String(request.status)));\n            } else {\n              // IE will return a status 12000+ on some sort of connection failure,\n              // so we return a blank error\n              // http://msdn.microsoft.com/en-us/library/aa383770%28VS.85%29.aspx\n              var msg =\n                'XHR response had no status code (likely connection failure)';\n              callback(_newRetriableError(msg));\n            }\n          }\n        } catch (ex) {\n          //jquery source mentions firefox may error out while accessing the\n          //request members if there is a network error\n          //https://github.com/jquery/jquery/blob/a938d7b1282fc0e5c52502c225ae8f0cef219f0a/src/ajax/xhr.js#L111\n          var exc;\n          if (ex && ex.stack) {\n            exc = ex;\n          } else {\n            exc = new Error(ex);\n          }\n          callback(exc);\n        }\n      };\n\n      request.open(method, url, true);\n      if (request.setRequestHeader) {\n        request.setRequestHeader('Content-Type', 'application/json');\n        request.setRequestHeader('X-Rollbar-Access-Token', accessToken);\n      }\n\n      if (_.isFiniteNumber(timeout)) {\n        request.timeout = timeout;\n      }\n\n      request.onreadystatechange = onreadystatechange;\n      request.send(data);\n    } catch (e1) {\n      // Sending using the normal xmlhttprequest object didn't work, try XDomainRequest\n      if (typeof XDomainRequest !== 'undefined') {\n        // Assume we are in a really old browser which has a bunch of limitations:\n        // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\n        // Extreme paranoia: if we have XDomainRequest then we have a window, but just in case\n        if (!window || !window.location) {\n          return callback(\n            new Error(\n              'No window available during request, unknown environment',\n            ),\n          );\n        }\n\n        // If the current page is http, try and send over http\n        if (\n          window.location.href.substring(0, 5) === 'http:' &&\n          url.substring(0, 5) === 'https'\n        ) {\n          url = 'http' + url.substring(5);\n        }\n\n        var xdomainrequest = new XDomainRequest();\n        xdomainrequest.onprogress = function () {};\n        xdomainrequest.ontimeout = function () {\n          var msg = 'Request timed out';\n          var code = 'ETIMEDOUT';\n          callback(_newRetriableError(msg, code));\n        };\n        xdomainrequest.onerror = function () {\n          callback(new Error('Error during request'));\n        };\n        xdomainrequest.onload = function () {\n          var parseResponse = _.jsonParse(xdomainrequest.responseText);\n          callback(parseResponse.error, parseResponse.value);\n        };\n        xdomainrequest.open(method, url, true);\n        xdomainrequest.send(data);\n      } else {\n        callback(new Error('Cannot find a method to transport a request'));\n      }\n    }\n  } catch (e2) {\n    callback(e2);\n  }\n}\n\nfunction _createXMLHTTPObject() {\n  /* global ActiveXObject:false */\n\n  var factories = [\n    function () {\n      return new XMLHttpRequest();\n    },\n    function () {\n      return new ActiveXObject('Msxml2.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Msxml3.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Microsoft.XMLHTTP');\n    },\n  ];\n  var xmlhttp;\n  var i;\n  var numFactories = factories.length;\n  for (i = 0; i < numFactories; i++) {\n    /* eslint-disable no-empty */\n    try {\n      xmlhttp = factories[i]();\n      break;\n    } catch (e) {\n      // pass\n    }\n    /* eslint-enable no-empty */\n  }\n  return xmlhttp;\n}\n\nfunction _isSuccess(r) {\n  return r && r.status && r.status === 200;\n}\n\nfunction _isNormalFailure(r) {\n  return r && _.isType(r.status, 'number') && r.status >= 400 && r.status < 600;\n}\n\nfunction _newRetriableError(message, code) {\n  var err = new Error(message);\n  err.code = code || 'ENOTFOUND';\n  return err;\n}\n\nmodule.exports = makeXhrRequest;\n", "'use strict';\n\n// See https://nodejs.org/docs/latest/api/url.html\nfunction parse(url) {\n  var result = {\n    protocol: null,\n    auth: null,\n    host: null,\n    path: null,\n    hash: null,\n    href: url,\n    hostname: null,\n    port: null,\n    pathname: null,\n    search: null,\n    query: null,\n  };\n\n  var i, last;\n  i = url.indexOf('//');\n  if (i !== -1) {\n    result.protocol = url.substring(0, i);\n    last = i + 2;\n  } else {\n    last = 0;\n  }\n\n  i = url.indexOf('@', last);\n  if (i !== -1) {\n    result.auth = url.substring(last, i);\n    last = i + 1;\n  }\n\n  i = url.indexOf('/', last);\n  if (i === -1) {\n    i = url.indexOf('?', last);\n    if (i === -1) {\n      i = url.indexOf('#', last);\n      if (i === -1) {\n        result.host = url.substring(last);\n      } else {\n        result.host = url.substring(last, i);\n        result.hash = url.substring(i);\n      }\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      return result;\n    } else {\n      result.host = url.substring(last, i);\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      last = i;\n    }\n  } else {\n    result.host = url.substring(last, i);\n    result.hostname = result.host.split(':')[0];\n    result.port = result.host.split(':')[1];\n    if (result.port) {\n      result.port = parseInt(result.port, 10);\n    }\n    last = i;\n  }\n\n  i = url.indexOf('#', last);\n  if (i === -1) {\n    result.path = url.substring(last);\n  } else {\n    result.path = url.substring(last, i);\n    result.hash = url.substring(i);\n  }\n\n  if (result.path) {\n    var pathParts = result.path.split('?');\n    result.pathname = pathParts[0];\n    result.query = pathParts[1];\n    result.search = result.query ? '?' + result.query : null;\n  }\n  return result;\n}\n\nmodule.exports = {\n  parse: parse,\n};\n", "'use strict';\n\nfunction wrapGlobals(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  // Adapted from https://github.com/bugsnag/bugsnag-js\n  var globals =\n    'EventTarget,Window,Node,ApplicationCache,AudioTrackList,ChannelMergerNode,CryptoOperation,EventSource,FileReader,HTMLUnknownElement,IDBDatabase,IDBRequest,IDBTransaction,KeyOperation,MediaController,MessagePort,ModalWindow,Notification,SVGElementInstance,Screen,TextTrack,TextTrackCue,TextTrackList,WebSocket,WebSocketWorker,Worker,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload'.split(\n      ',',\n    );\n  var i, global;\n  for (i = 0; i < globals.length; ++i) {\n    global = globals[i];\n\n    if (window[global] && window[global].prototype) {\n      _extendListenerPrototype(handler, window[global].prototype, shim);\n    }\n  }\n}\n\nfunction _extendListenerPrototype(handler, prototype, shim) {\n  if (\n    prototype.hasOwnProperty &&\n    prototype.hasOwnProperty('addEventListener')\n  ) {\n    var oldAddEventListener = prototype.addEventListener;\n    while (\n      oldAddEventListener._rollbarOldAdd &&\n      oldAddEventListener.belongsToShim\n    ) {\n      oldAddEventListener = oldAddEventListener._rollbarOldAdd;\n    }\n    var addFn = function (event, callback, bubble) {\n      oldAddEventListener.call(this, event, handler.wrap(callback), bubble);\n    };\n    addFn._rollbarOldAdd = oldAddEventListener;\n    addFn.belongsToShim = shim;\n    prototype.addEventListener = addFn;\n\n    var oldRemoveEventListener = prototype.removeEventListener;\n    while (\n      oldRemoveEventListener._rollbarOldRemove &&\n      oldRemoveEventListener.belongsToShim\n    ) {\n      oldRemoveEventListener = oldRemoveEventListener._rollbarOldRemove;\n    }\n    var removeFn = function (event, callback, bubble) {\n      oldRemoveEventListener.call(\n        this,\n        event,\n        (callback && callback._rollbar_wrapped) || callback,\n        bubble,\n      );\n    };\n    removeFn._rollbarOldRemove = oldRemoveEventListener;\n    removeFn.belongsToShim = shim;\n    prototype.removeEventListener = removeFn;\n  }\n}\n\nmodule.exports = wrapGlobals;\n", "'use strict';\n\nmodule.exports = {\n  version: '2.26.4',\n  endpoint: 'api.rollbar.com/api/1/item/',\n  logLevel: 'debug',\n  reportLevel: 'debug',\n  uncaughtErrorLevel: 'error',\n  maxItems: 0,\n  itemsPerMin: 60,\n};\n", "'use strict';\n\nvar ErrorStackParser = require('error-stack-parser');\n\nvar UNKNOWN_FUNCTION = '?';\nvar ERR_CLASS_REGEXP = new RegExp(\n  '^(([a-zA-Z0-9-_$ ]*): *)?(Uncaught )?([a-zA-Z0-9-_$ ]*): ',\n);\n\nfunction guessFunctionName() {\n  return UNKNOWN_FUNCTION;\n}\n\nfunction gatherContext() {\n  return null;\n}\n\nfunction Frame(stackFrame) {\n  var data = {};\n\n  data._stackFrame = stackFrame;\n\n  data.url = stackFrame.fileName;\n  data.line = stackFrame.lineNumber;\n  data.func = stackFrame.functionName;\n  data.column = stackFrame.columnNumber;\n  data.args = stackFrame.args;\n\n  data.context = gatherContext();\n\n  return data;\n}\n\nfunction Stack(exception, skip) {\n  function getStack() {\n    var parserStack = [];\n\n    skip = skip || 0;\n\n    try {\n      parserStack = ErrorStackParser.parse(exception);\n    } catch (e) {\n      parserStack = [];\n    }\n\n    var stack = [];\n\n    for (var i = skip; i < parserStack.length; i++) {\n      stack.push(new Frame(parserStack[i]));\n    }\n\n    return stack;\n  }\n\n  return {\n    stack: getStack(),\n    message: exception.message,\n    name: _mostSpecificErrorName(exception),\n    rawStack: exception.stack,\n    rawException: exception,\n  };\n}\n\nfunction parse(e, skip) {\n  var err = e;\n\n  if (err.nested || err.cause) {\n    var traceChain = [];\n    while (err) {\n      traceChain.push(new Stack(err, skip));\n      err = err.nested || err.cause;\n\n      skip = 0; // Only apply skip value to primary error\n    }\n\n    // Return primary error with full trace chain attached.\n    traceChain[0].traceChain = traceChain;\n    return traceChain[0];\n  } else {\n    return new Stack(err, skip);\n  }\n}\n\nfunction guessErrorClass(errMsg) {\n  if (!errMsg || !errMsg.match) {\n    return ['Unknown error. There was no error message to display.', ''];\n  }\n  var errClassMatch = errMsg.match(ERR_CLASS_REGEXP);\n  var errClass = '(unknown)';\n\n  if (errClassMatch) {\n    errClass = errClassMatch[errClassMatch.length - 1];\n    errMsg = errMsg.replace(\n      (errClassMatch[errClassMatch.length - 2] || '') + errClass + ':',\n      '',\n    );\n    errMsg = errMsg.replace(/(^[\\s]+|[\\s]+$)/g, '');\n  }\n  return [errClass, errMsg];\n}\n\n// * Prefers any value over an empty string\n// * Prefers any value over 'Error' where possible\n// * Prefers name over constructor.name when both are more specific than 'Error'\nfunction _mostSpecificErrorName(error) {\n  var name = error.name && error.name.length && error.name;\n  var constructorName =\n    error.constructor.name &&\n    error.constructor.name.length &&\n    error.constructor.name;\n\n  if (!name || !constructorName) {\n    return name || constructorName;\n  }\n\n  if (name === 'Error') {\n    return constructorName;\n  }\n  return name;\n}\n\nmodule.exports = {\n  guessFunctionName: guessFunctionName,\n  guessErrorClass: guessErrorClass,\n  gatherContext: gatherContext,\n  parse: parse,\n  Stack: Stack,\n  Frame: Frame,\n};\n", "'use strict';\n\n'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\n\nvar isPlainObject = function isPlainObject(obj) {\n  if (!obj || toStr.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n  var hasIsPrototypeOf =\n    obj.constructor &&\n    obj.constructor.prototype &&\n    hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n  // Not own constructor property must be Object\n  if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n    return false;\n  }\n\n  // Own properties are enumerated firstly, so to speed up,\n  // if last one is own, then all properties are own.\n  var key;\n  for (key in obj) {\n    /**/\n  }\n\n  return typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\nfunction merge() {\n  var i,\n    src,\n    copy,\n    clone,\n    name,\n    result = {},\n    current = null,\n    length = arguments.length;\n\n  for (i = 0; i < length; i++) {\n    current = arguments[i];\n    if (current == null) {\n      continue;\n    }\n\n    for (name in current) {\n      src = result[name];\n      copy = current[name];\n      if (result !== copy) {\n        if (copy && isPlainObject(copy)) {\n          clone = src && isPlainObject(src) ? src : {};\n          result[name] = merge(clone, copy);\n        } else if (typeof copy !== 'undefined') {\n          result[name] = copy;\n        }\n      }\n    }\n  }\n  return result;\n}\n\nmodule.exports = merge;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Notifier - the internal object responsible for delegating between the client exposed API, the\n * chain of transforms necessary to turn an item into something that can be sent to Rollbar, and the\n * queue which handles the communcation with the Rollbar API servers.\n *\n * @param queue - an object that conforms to the interface: addItem(item, callback)\n * @param options - an object representing the options to be set for this notifier, this should have\n * any defaults already set by the caller\n */\nfunction Notifier(queue, options) {\n  this.queue = queue;\n  this.options = options;\n  this.transforms = [];\n  this.diagnostic = {};\n}\n\n/*\n * configure - updates the options for this notifier with the passed in object\n *\n * @param options - an object which gets merged with the current options set on this notifier\n * @returns this\n */\nNotifier.prototype.configure = function (options) {\n  this.queue && this.queue.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addTransform - adds a transform onto the end of the queue of transforms for this notifier\n *\n * @param transform - a function which takes three arguments:\n *    * item: An Object representing the data to eventually be sent to Rollbar\n *    * options: The current value of the options for this notifier\n *    * callback: function(err: (Null|Error), item: (Null|Object)) the transform must call this\n *    callback with a null value for error if it wants the processing chain to continue, otherwise\n *    with an error to terminate the processing. The item should be the updated item after this\n *    transform is finished modifying it.\n */\nNotifier.prototype.addTransform = function (transform) {\n  if (_.isFunction(transform)) {\n    this.transforms.push(transform);\n  }\n  return this;\n};\n\n/*\n * log - the internal log function which applies the configured transforms and then pushes onto the\n * queue to be sent to the backend.\n *\n * @param item - An object with the following structure:\n *    message [String] - An optional string to be sent to rollbar\n *    error [Error] - An optional error\n *\n * @param callback - A function of type function(err, resp) which will be called with exactly one\n * null argument and one non-null argument. The callback will be called once, either during the\n * transform stage if an error occurs inside a transform, or in response to the communication with\n * the backend. The second argument will be the response from the backend in case of success.\n */\nNotifier.prototype.log = function (item, callback) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!this.options.enabled) {\n    return callback(new Error('Rollbar is not enabled'));\n  }\n\n  this.queue.addPendingItem(item);\n  var originalError = item.err;\n  this._applyTransforms(\n    item,\n    function (err, i) {\n      if (err) {\n        this.queue.removePendingItem(item);\n        return callback(err, null);\n      }\n      this.queue.addItem(i, callback, originalError, item);\n    }.bind(this),\n  );\n};\n\n/* Internal */\n\n/*\n * _applyTransforms - Applies the transforms that have been added to this notifier sequentially. See\n * `addTransform` for more information.\n *\n * @param item - An item to be transformed\n * @param callback - A function of type function(err, item) which will be called with a non-null\n * error and a null item in the case of a transform failure, or a null error and non-null item after\n * all transforms have been applied.\n */\nNotifier.prototype._applyTransforms = function (item, callback) {\n  var transformIndex = -1;\n  var transformsLength = this.transforms.length;\n  var transforms = this.transforms;\n  var options = this.options;\n\n  var cb = function (err, i) {\n    if (err) {\n      callback(err, null);\n      return;\n    }\n\n    transformIndex++;\n\n    if (transformIndex === transformsLength) {\n      callback(null, i);\n      return;\n    }\n\n    transforms[transformIndex](i, options, cb);\n  };\n\n  cb(null, item);\n};\n\nmodule.exports = Notifier;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction checkLevel(item, settings) {\n  var level = item.level;\n  var levelVal = _.LEVELS[level] || 0;\n  var reportLevel = settings.reportLevel;\n  var reportLevelVal = _.LEVELS[reportLevel] || 0;\n\n  if (levelVal < reportLevelVal) {\n    return false;\n  }\n  return true;\n}\n\nfunction userCheckIgnore(logger) {\n  return function (item, settings) {\n    var isUncaught = !!item._isUncaught;\n    delete item._isUncaught;\n    var args = item._originalArgs;\n    delete item._originalArgs;\n    try {\n      if (_.isFunction(settings.onSendCallback)) {\n        settings.onSendCallback(isUncaught, args, item);\n      }\n    } catch (e) {\n      settings.onSendCallback = null;\n      logger.error('Error while calling onSendCallback, removing', e);\n    }\n    try {\n      if (\n        _.isFunction(settings.checkIgnore) &&\n        settings.checkIgnore(isUncaught, args, item)\n      ) {\n        return false;\n      }\n    } catch (e) {\n      settings.checkIgnore = null;\n      logger.error('Error while calling custom checkIgnore(), removing', e);\n    }\n    return true;\n  };\n}\n\nfunction urlIsNotBlockListed(logger) {\n  return function (item, settings) {\n    return !urlIsOnAList(item, settings, 'blocklist', logger);\n  };\n}\n\nfunction urlIsSafeListed(logger) {\n  return function (item, settings) {\n    return urlIsOnAList(item, settings, 'safelist', logger);\n  };\n}\n\nfunction matchFrames(trace, list, block) {\n  if (!trace) {\n    return !block;\n  }\n\n  var frames = trace.frames;\n\n  if (!frames || frames.length === 0) {\n    return !block;\n  }\n\n  var frame, filename, url, urlRegex;\n  var listLength = list.length;\n  var frameLength = frames.length;\n  for (var i = 0; i < frameLength; i++) {\n    frame = frames[i];\n    filename = frame.filename;\n\n    if (!_.isType(filename, 'string')) {\n      return !block;\n    }\n\n    for (var j = 0; j < listLength; j++) {\n      url = list[j];\n      urlRegex = new RegExp(url);\n\n      if (urlRegex.test(filename)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction urlIsOnAList(item, settings, safeOrBlock, logger) {\n  // safelist is the default\n  var block = false;\n  if (safeOrBlock === 'blocklist') {\n    block = true;\n  }\n\n  var list, traces;\n  try {\n    list = block ? settings.hostBlockList : settings.hostSafeList;\n    traces = _.get(item, 'body.trace_chain') || [_.get(item, 'body.trace')];\n\n    // These two checks are important to come first as they are defaults\n    // in case the list is missing or the trace is missing or not well-formed\n    if (!list || list.length === 0) {\n      return !block;\n    }\n    if (traces.length === 0 || !traces[0]) {\n      return !block;\n    }\n\n    var tracesLength = traces.length;\n    for (var i = 0; i < tracesLength; i++) {\n      if (matchFrames(traces[i], list, block)) {\n        return true;\n      }\n    }\n  } catch (\n    e\n    /* istanbul ignore next */\n  ) {\n    if (block) {\n      settings.hostBlockList = null;\n    } else {\n      settings.hostSafeList = null;\n    }\n    var listName = block ? 'hostBlockList' : 'hostSafeList';\n    logger.error(\n      \"Error while reading your configuration's \" +\n        listName +\n        ' option. Removing custom ' +\n        listName +\n        '.',\n      e,\n    );\n    return !block;\n  }\n  return false;\n}\n\nfunction messageIsIgnored(logger) {\n  return function (item, settings) {\n    var i, j, ignoredMessages, len, messageIsIgnored, rIgnoredMessage, messages;\n\n    try {\n      messageIsIgnored = false;\n      ignoredMessages = settings.ignoredMessages;\n\n      if (!ignoredMessages || ignoredMessages.length === 0) {\n        return true;\n      }\n\n      messages = messagesFromItem(item);\n\n      if (messages.length === 0) {\n        return true;\n      }\n\n      len = ignoredMessages.length;\n      for (i = 0; i < len; i++) {\n        rIgnoredMessage = new RegExp(ignoredMessages[i], 'gi');\n\n        for (j = 0; j < messages.length; j++) {\n          messageIsIgnored = rIgnoredMessage.test(messages[j]);\n\n          if (messageIsIgnored) {\n            return false;\n          }\n        }\n      }\n    } catch (\n      e\n      /* istanbul ignore next */\n    ) {\n      settings.ignoredMessages = null;\n      logger.error(\n        \"Error while reading your configuration's ignoredMessages option. Removing custom ignoredMessages.\",\n      );\n    }\n\n    return true;\n  };\n}\n\nfunction messagesFromItem(item) {\n  var body = item.body;\n  var messages = [];\n\n  // The payload schema only allows one of trace_chain, message, or trace.\n  // However, existing test cases are based on having both trace and message present.\n  // So here we preserve the ability to collect strings from any combination of these keys.\n  if (body.trace_chain) {\n    var traceChain = body.trace_chain;\n    for (var i = 0; i < traceChain.length; i++) {\n      var trace = traceChain[i];\n      messages.push(_.get(trace, 'exception.message'));\n    }\n  }\n  if (body.trace) {\n    messages.push(_.get(body, 'trace.exception.message'));\n  }\n  if (body.message) {\n    messages.push(_.get(body, 'message.body'));\n  }\n  return messages;\n}\n\nmodule.exports = {\n  checkLevel: checkLevel,\n  userCheckIgnore: userCheckIgnore,\n  urlIsNotBlockListed: urlIsNotBlockListed,\n  urlIsSafeListed: urlIsSafeListed,\n  messageIsIgnored: messageIsIgnored,\n};\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Queue - an object which handles which handles a queue of items to be sent to Rollbar.\n *   This object handles rate limiting via a passed in rate limiter, retries based on connection\n *   errors, and filtering of items based on a set of configurable predicates. The communication to\n *   the backend is performed via a given API object.\n *\n * @param rateLimiter - An object which conforms to the interface\n *    rateLimiter.shouldSend(item) -> bool\n * @param api - An object which conforms to the interface\n *    api.postItem(payload, function(err, response))\n * @param logger - An object used to log verbose messages if desired\n * @param options - see Queue.prototype.configure\n */\nfunction Queue(rateLimiter, api, logger, options) {\n  this.rateLimiter = rateLimiter;\n  this.api = api;\n  this.logger = logger;\n  this.options = options;\n  this.predicates = [];\n  this.pendingItems = [];\n  this.pendingRequests = [];\n  this.retryQueue = [];\n  this.retryHandle = null;\n  this.waitCallback = null;\n  this.waitIntervalID = null;\n}\n\n/*\n * configure - updates the options this queue uses\n *\n * @param options\n */\nQueue.prototype.configure = function (options) {\n  this.api && this.api.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addPredicate - adds a predicate to the end of the list of predicates for this queue\n *\n * @param predicate - function(item, options) -> (bool|{err: Error})\n *  Returning true means that this predicate passes and the item is okay to go on the queue\n *  Returning false means do not add the item to the queue, but it is not an error\n *  Returning {err: Error} means do not add the item to the queue, and the given error explains why\n *  Returning {err: undefined} is equivalent to returning true but don't do that\n */\nQueue.prototype.addPredicate = function (predicate) {\n  if (_.isFunction(predicate)) {\n    this.predicates.push(predicate);\n  }\n  return this;\n};\n\nQueue.prototype.addPendingItem = function (item) {\n  this.pendingItems.push(item);\n};\n\nQueue.prototype.removePendingItem = function (item) {\n  var idx = this.pendingItems.indexOf(item);\n  if (idx !== -1) {\n    this.pendingItems.splice(idx, 1);\n  }\n};\n\n/*\n * addItem - Send an item to the Rollbar API if all of the predicates are satisfied\n *\n * @param item - The payload to send to the backend\n * @param callback - function(error, repsonse) which will be called with the response from the API\n *  in the case of a success, otherwise response will be null and error will have a value. If both\n *  error and response are null then the item was stopped by a predicate which did not consider this\n *  to be an error condition, but nonetheless did not send the item to the API.\n *  @param originalError - The original error before any transformations that is to be logged if any\n */\nQueue.prototype.addItem = function (\n  item,\n  callback,\n  originalError,\n  originalItem,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {\n      return;\n    };\n  }\n  var predicateResult = this._applyPredicates(item);\n  if (predicateResult.stop) {\n    this.removePendingItem(originalItem);\n    callback(predicateResult.err);\n    return;\n  }\n  this._maybeLog(item, originalError);\n  this.removePendingItem(originalItem);\n  if (!this.options.transmit) {\n    callback(new Error('Transmit disabled'));\n    return;\n  }\n  this.pendingRequests.push(item);\n  try {\n    this._makeApiRequest(\n      item,\n      function (err, resp) {\n        this._dequeuePendingRequest(item);\n        callback(err, resp);\n      }.bind(this),\n    );\n  } catch (e) {\n    this._dequeuePendingRequest(item);\n    callback(e);\n  }\n};\n\n/*\n * wait - Stop any further errors from being added to the queue, and get called back when all items\n *   currently processing have finished sending to the backend.\n *\n * @param callback - function() called when all pending items have been sent\n */\nQueue.prototype.wait = function (callback) {\n  if (!_.isFunction(callback)) {\n    return;\n  }\n  this.waitCallback = callback;\n  if (this._maybeCallWait()) {\n    return;\n  }\n  if (this.waitIntervalID) {\n    this.waitIntervalID = clearInterval(this.waitIntervalID);\n  }\n  this.waitIntervalID = setInterval(\n    function () {\n      this._maybeCallWait();\n    }.bind(this),\n    500,\n  );\n};\n\n/* _applyPredicates - Sequentially applies the predicates that have been added to the queue to the\n *   given item with the currently configured options.\n *\n * @param item - An item in the queue\n * @returns {stop: bool, err: (Error|null)} - stop being true means do not add item to the queue,\n *   the error value should be passed up to a callbak if we are stopping.\n */\nQueue.prototype._applyPredicates = function (item) {\n  var p = null;\n  for (var i = 0, len = this.predicates.length; i < len; i++) {\n    p = this.predicates[i](item, this.options);\n    if (!p || p.err !== undefined) {\n      return { stop: true, err: p.err };\n    }\n  }\n  return { stop: false, err: null };\n};\n\n/*\n * _makeApiRequest - Send an item to Rollbar, callback when done, if there is an error make an\n *   effort to retry if we are configured to do so.\n *\n * @param item - an item ready to send to the backend\n * @param callback - function(err, response)\n */\nQueue.prototype._makeApiRequest = function (item, callback) {\n  var rateLimitResponse = this.rateLimiter.shouldSend(item);\n  if (rateLimitResponse.shouldSend) {\n    this.api.postItem(\n      item,\n      function (err, resp) {\n        if (err) {\n          this._maybeRetry(err, item, callback);\n        } else {\n          callback(err, resp);\n        }\n      }.bind(this),\n    );\n  } else if (rateLimitResponse.error) {\n    callback(rateLimitResponse.error);\n  } else {\n    this.api.postItem(rateLimitResponse.payload, callback);\n  }\n};\n\n// These are errors basically mean there is no internet connection\nvar RETRIABLE_ERRORS = [\n  'ECONNRESET',\n  'ENOTFOUND',\n  'ESOCKETTIMEDOUT',\n  'ETIMEDOUT',\n  'ECONNREFUSED',\n  'EHOSTUNREACH',\n  'EPIPE',\n  'EAI_AGAIN',\n];\n\n/*\n * _maybeRetry - Given the error returned by the API, decide if we should retry or just callback\n *   with the error.\n *\n * @param err - an error returned by the API transport\n * @param item - the item that was trying to be sent when this error occured\n * @param callback - function(err, response)\n */\nQueue.prototype._maybeRetry = function (err, item, callback) {\n  var shouldRetry = false;\n  if (this.options.retryInterval) {\n    for (var i = 0, len = RETRIABLE_ERRORS.length; i < len; i++) {\n      if (err.code === RETRIABLE_ERRORS[i]) {\n        shouldRetry = true;\n        break;\n      }\n    }\n    if (shouldRetry && _.isFiniteNumber(this.options.maxRetries)) {\n      item.retries = item.retries ? item.retries + 1 : 1;\n      if (item.retries > this.options.maxRetries) {\n        shouldRetry = false;\n      }\n    }\n  }\n  if (shouldRetry) {\n    this._retryApiRequest(item, callback);\n  } else {\n    callback(err);\n  }\n};\n\n/*\n * _retryApiRequest - Add an item and a callback to a queue and possibly start a timer to process\n *   that queue based on the retryInterval in the options for this queue.\n *\n * @param item - an item that failed to send due to an error we deem retriable\n * @param callback - function(err, response)\n */\nQueue.prototype._retryApiRequest = function (item, callback) {\n  this.retryQueue.push({ item: item, callback: callback });\n\n  if (!this.retryHandle) {\n    this.retryHandle = setInterval(\n      function () {\n        while (this.retryQueue.length) {\n          var retryObject = this.retryQueue.shift();\n          this._makeApiRequest(retryObject.item, retryObject.callback);\n        }\n      }.bind(this),\n      this.options.retryInterval,\n    );\n  }\n};\n\n/*\n * _dequeuePendingRequest - Removes the item from the pending request queue, this queue is used to\n *   enable to functionality of providing a callback that clients can pass to `wait` to be notified\n *   when the pending request queue has been emptied. This must be called when the API finishes\n *   processing this item. If a `wait` callback is configured, it is called by this function.\n *\n * @param item - the item previously added to the pending request queue\n */\nQueue.prototype._dequeuePendingRequest = function (item) {\n  var idx = this.pendingRequests.indexOf(item);\n  if (idx !== -1) {\n    this.pendingRequests.splice(idx, 1);\n    this._maybeCallWait();\n  }\n};\n\nQueue.prototype._maybeLog = function (data, originalError) {\n  if (this.logger && this.options.verbose) {\n    var message = originalError;\n    message = message || _.get(data, 'body.trace.exception.message');\n    message = message || _.get(data, 'body.trace_chain.0.exception.message');\n    if (message) {\n      this.logger.error(message);\n      return;\n    }\n    message = _.get(data, 'body.message.body');\n    if (message) {\n      this.logger.log(message);\n    }\n  }\n};\n\nQueue.prototype._maybeCallWait = function () {\n  if (\n    _.isFunction(this.waitCallback) &&\n    this.pendingItems.length === 0 &&\n    this.pendingRequests.length === 0\n  ) {\n    if (this.waitIntervalID) {\n      this.waitIntervalID = clearInterval(this.waitIntervalID);\n    }\n    this.waitCallback();\n    return true;\n  }\n  return false;\n};\n\nmodule.exports = Queue;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * RateLimiter - an object that encapsulates the logic for counting items sent to Rollbar\n *\n * @param options - the same options that are accepted by configureGlobal offered as a convenience\n */\nfunction RateLimiter(options) {\n  this.startTime = _.now();\n  this.counter = 0;\n  this.perMinCounter = 0;\n  this.platform = null;\n  this.platformOptions = {};\n  this.configureGlobal(options);\n}\n\nRateLimiter.globalSettings = {\n  startTime: _.now(),\n  maxItems: undefined,\n  itemsPerMinute: undefined,\n};\n\n/*\n * configureGlobal - set the global rate limiter options\n *\n * @param options - Only the following values are recognized:\n *    startTime: a timestamp of the form returned by (new Date()).getTime()\n *    maxItems: the maximum items\n *    itemsPerMinute: the max number of items to send in a given minute\n */\nRateLimiter.prototype.configureGlobal = function (options) {\n  if (options.startTime !== undefined) {\n    RateLimiter.globalSettings.startTime = options.startTime;\n  }\n  if (options.maxItems !== undefined) {\n    RateLimiter.globalSettings.maxItems = options.maxItems;\n  }\n  if (options.itemsPerMinute !== undefined) {\n    RateLimiter.globalSettings.itemsPerMinute = options.itemsPerMinute;\n  }\n};\n\n/*\n * shouldSend - determine if we should send a given item based on rate limit settings\n *\n * @param item - the item we are about to send\n * @returns An object with the following structure:\n *  error: (Error|null)\n *  shouldSend: bool\n *  payload: (Object|null)\n *  If shouldSend is false, the item passed as a parameter should not be sent to Rollbar, and\n *  exactly one of error or payload will be non-null. If error is non-null, the returned Error will\n *  describe the situation, but it means that we were already over a rate limit (either globally or\n *  per minute) when this item was checked. If error is null, and therefore payload is non-null, it\n *  means this item put us over the global rate limit and the payload should be sent to Rollbar in\n *  place of the passed in item.\n */\nRateLimiter.prototype.shouldSend = function (item, now) {\n  now = now || _.now();\n  var elapsedTime = now - this.startTime;\n  if (elapsedTime < 0 || elapsedTime >= 60000) {\n    this.startTime = now;\n    this.perMinCounter = 0;\n  }\n\n  var globalRateLimit = RateLimiter.globalSettings.maxItems;\n  var globalRateLimitPerMin = RateLimiter.globalSettings.itemsPerMinute;\n\n  if (checkRate(item, globalRateLimit, this.counter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimit + ' max items reached',\n      false,\n    );\n  } else if (checkRate(item, globalRateLimitPerMin, this.perMinCounter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimitPerMin + ' items per minute reached',\n      false,\n    );\n  }\n  this.counter++;\n  this.perMinCounter++;\n\n  var shouldSend = !checkRate(item, globalRateLimit, this.counter);\n  var perMinute = shouldSend;\n  shouldSend =\n    shouldSend && !checkRate(item, globalRateLimitPerMin, this.perMinCounter);\n  return shouldSendValue(\n    this.platform,\n    this.platformOptions,\n    null,\n    shouldSend,\n    globalRateLimit,\n    globalRateLimitPerMin,\n    perMinute,\n  );\n};\n\nRateLimiter.prototype.setPlatformOptions = function (platform, options) {\n  this.platform = platform;\n  this.platformOptions = options;\n};\n\n/* Helpers */\n\nfunction checkRate(item, limit, counter) {\n  return !item.ignoreRateLimit && limit >= 1 && counter > limit;\n}\n\nfunction shouldSendValue(\n  platform,\n  options,\n  error,\n  shouldSend,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var payload = null;\n  if (error) {\n    error = new Error(error);\n  }\n  if (!error && !shouldSend) {\n    payload = rateLimitPayload(\n      platform,\n      options,\n      globalRateLimit,\n      limitPerMin,\n      perMinute,\n    );\n  }\n  return { error: error, shouldSend: shouldSend, payload: payload };\n}\n\nfunction rateLimitPayload(\n  platform,\n  options,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var environment =\n    options.environment || (options.payload && options.payload.environment);\n  var msg;\n  if (perMinute) {\n    msg = 'item per minute limit reached, ignoring errors until timeout';\n  } else {\n    msg = 'maxItems has been hit, ignoring errors until reset.';\n  }\n  var item = {\n    body: {\n      message: {\n        body: msg,\n        extra: {\n          maxItems: globalRateLimit,\n          itemsPerMinute: limitPerMin,\n        },\n      },\n    },\n    language: 'javascript',\n    environment: environment,\n    notifier: {\n      version:\n        (options.notifier && options.notifier.version) || options.version,\n    },\n  };\n  if (platform === 'browser') {\n    item.platform = 'browser';\n    item.framework = 'browser-js';\n    item.notifier.name = 'rollbar-browser-js';\n  } else if (platform === 'server') {\n    item.framework = options.framework || 'node-js';\n    item.notifier.name = options.notifier.name;\n  } else if (platform === 'react-native') {\n    item.framework = options.framework || 'react-native';\n    item.notifier.name = options.notifier.name;\n  }\n  return item;\n}\n\nmodule.exports = RateLimiter;\n", "'use strict';\n\nvar RateLimiter = require('./rateLimiter');\nvar Queue = require('./queue');\nvar Notifier = require('./notifier');\nvar _ = require('./utility');\n\n/*\n * Rollbar - the interface to Rollbar\n *\n * @param options\n * @param api\n * @param logger\n */\nfunction Rollbar(options, api, logger, telemeter, platform) {\n  this.options = _.merge(options);\n  this.logger = logger;\n  Rollbar.rateLimiter.configureGlobal(this.options);\n  Rollbar.rateLimiter.setPlatformOptions(platform, this.options);\n  this.api = api;\n  this.queue = new Queue(Rollbar.rateLimiter, api, logger, this.options);\n\n  // This must happen before the Notifier is created\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier = new Notifier(this.queue, this.options);\n  this.telemeter = telemeter;\n  setStackTraceLimit(options);\n  this.lastError = null;\n  this.lastErrorHash = 'none';\n}\n\nvar defaultOptions = {\n  maxItems: 0,\n  itemsPerMinute: 60,\n};\n\nRollbar.rateLimiter = new RateLimiter(defaultOptions);\n\nRollbar.prototype.global = function (options) {\n  Rollbar.rateLimiter.configureGlobal(options);\n  return this;\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n\n  this.options = _.merge(oldOptions, options, payload);\n\n  // This must happen before the Notifier is configured\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier && this.notifier.configure(this.options);\n  this.telemeter && this.telemeter.configure(this.options);\n  setStackTraceLimit(options);\n  this.global(this.options);\n\n  if (validateTracer(options.tracer)) {\n    this.tracer = options.tracer;\n  }\n\n  return this;\n};\n\nRollbar.prototype.log = function (item) {\n  var level = this._defaultLogLevel();\n  return this._log(level, item);\n};\n\nRollbar.prototype.debug = function (item) {\n  this._log('debug', item);\n};\n\nRollbar.prototype.info = function (item) {\n  this._log('info', item);\n};\n\nRollbar.prototype.warn = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.warning = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.error = function (item) {\n  this._log('error', item);\n};\n\nRollbar.prototype.critical = function (item) {\n  this._log('critical', item);\n};\n\nRollbar.prototype.wait = function (callback) {\n  this.queue.wait(callback);\n};\n\nRollbar.prototype.captureEvent = function (type, metadata, level) {\n  return this.telemeter && this.telemeter.captureEvent(type, metadata, level);\n};\n\nRollbar.prototype.captureDomContentLoaded = function (ts) {\n  return this.telemeter && this.telemeter.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (ts) {\n  return this.telemeter && this.telemeter.captureLoad(ts);\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.api.buildJsonPayload(item);\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  this.api.postJsonPayload(jsonPayload);\n};\n\n/* Internal */\n\nRollbar.prototype._log = function (defaultLevel, item) {\n  var callback;\n  if (item.callback) {\n    callback = item.callback;\n    delete item.callback;\n  }\n  if (this.options.ignoreDuplicateErrors && this._sameAsLastError(item)) {\n    if (callback) {\n      var error = new Error('ignored identical item');\n      error.item = item;\n      callback(error);\n    }\n    return;\n  }\n  try {\n    this._addTracingInfo(item);\n    item.level = item.level || defaultLevel;\n    this.telemeter && this.telemeter._captureRollbarItem(item);\n    item.telemetryEvents =\n      (this.telemeter && this.telemeter.copyEvents()) || [];\n    this.notifier.log(item, callback);\n  } catch (e) {\n    if (callback) {\n      callback(e);\n    }\n    this.logger.error(e);\n  }\n};\n\nRollbar.prototype._defaultLogLevel = function () {\n  return this.options.logLevel || 'debug';\n};\n\nRollbar.prototype._sameAsLastError = function (item) {\n  if (!item._isUncaught) {\n    return false;\n  }\n  var itemHash = generateItemHash(item);\n  if (this.lastErrorHash === itemHash) {\n    return true;\n  }\n  this.lastError = item.err;\n  this.lastErrorHash = itemHash;\n  return false;\n};\n\nRollbar.prototype._addTracingInfo = function (item) {\n  // Tracer validation occurs in the constructor\n  // or in the Rollbar.prototype.configure methods\n  if (this.tracer) {\n    // add rollbar occurrence uuid to span\n    var span = this.tracer.scope().active();\n\n    if (validateSpan(span)) {\n      span.setTag('rollbar.error_uuid', item.uuid);\n      span.setTag('rollbar.has_error', true);\n      span.setTag('error', true);\n      span.setTag(\n        'rollbar.item_url',\n        `https://rollbar.com/item/uuid/?uuid=${item.uuid}`,\n      );\n      span.setTag(\n        'rollbar.occurrence_url',\n        `https://rollbar.com/occurrence/uuid/?uuid=${item.uuid}`,\n      );\n\n      // add span ID & trace ID to occurrence\n      var opentracingSpanId = span.context().toSpanId();\n      var opentracingTraceId = span.context().toTraceId();\n\n      if (item.custom) {\n        item.custom.opentracing_span_id = opentracingSpanId;\n        item.custom.opentracing_trace_id = opentracingTraceId;\n      } else {\n        item.custom = {\n          opentracing_span_id: opentracingSpanId,\n          opentracing_trace_id: opentracingTraceId,\n        };\n      }\n    }\n  }\n};\n\nfunction generateItemHash(item) {\n  var message = item.message || '';\n  var stack = (item.err || {}).stack || String(item.err);\n  return message + '::' + stack;\n}\n\n// Node.js, Chrome, Safari, and some other browsers support this property\n// which globally sets the number of stack frames returned in an Error object.\n// If a browser can't use it, no harm done.\nfunction setStackTraceLimit(options) {\n  if (options.stackTraceLimit) {\n    Error.stackTraceLimit = options.stackTraceLimit;\n  }\n}\n\n/**\n * Validate the Tracer object provided to the Client\n * is valid for our Opentracing use case.\n * @param {opentracer.Tracer} tracer\n */\nfunction validateTracer(tracer) {\n  if (!tracer) {\n    return false;\n  }\n\n  if (!tracer.scope || typeof tracer.scope !== 'function') {\n    return false;\n  }\n\n  var scope = tracer.scope();\n\n  if (!scope || !scope.active || typeof scope.active !== 'function') {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Validate the Span object provided\n * @param {opentracer.Span} span\n */\nfunction validateSpan(span) {\n  if (!span || !span.context || typeof span.context !== 'function') {\n    return false;\n  }\n\n  var spanContext = span.context();\n\n  if (\n    !spanContext ||\n    !spanContext.toSpanId ||\n    !spanContext.toTraceId ||\n    typeof spanContext.toSpanId !== 'function' ||\n    typeof spanContext.toTraceId !== 'function'\n  ) {\n    return false;\n  }\n\n  return true;\n}\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction scrub(data, scrubFields, scrubPaths) {\n  scrubFields = scrubFields || [];\n\n  if (scrubPaths) {\n    for (var i = 0; i < scrubPaths.length; ++i) {\n      scrubPath(data, scrubPaths[i]);\n    }\n  }\n\n  var paramRes = _getScrubFieldRegexs(scrubFields);\n  var queryRes = _getScrubQueryParamRegexs(scrubFields);\n\n  function redactQueryParam(dummy0, paramPart) {\n    return paramPart + _.redact();\n  }\n\n  function paramScrubber(v) {\n    var i;\n    if (_.isType(v, 'string')) {\n      for (i = 0; i < queryRes.length; ++i) {\n        v = v.replace(queryRes[i], redactQueryParam);\n      }\n    }\n    return v;\n  }\n\n  function valScrubber(k, v) {\n    var i;\n    for (i = 0; i < paramRes.length; ++i) {\n      if (paramRes[i].test(k)) {\n        v = _.redact();\n        break;\n      }\n    }\n    return v;\n  }\n\n  function scrubber(k, v, seen) {\n    var tmpV = valScrubber(k, v);\n    if (tmpV === v) {\n      if (_.isType(v, 'object') || _.isType(v, 'array')) {\n        return traverse(v, scrubber, seen);\n      }\n      return paramScrubber(tmpV);\n    } else {\n      return tmpV;\n    }\n  }\n\n  return traverse(data, scrubber);\n}\n\nfunction scrubPath(obj, path) {\n  var keys = path.split('.');\n  var last = keys.length - 1;\n  try {\n    for (var i = 0; i <= last; ++i) {\n      if (i < last) {\n        obj = obj[keys[i]];\n      } else {\n        obj[keys[i]] = _.redact();\n      }\n    }\n  } catch (e) {\n    // Missing key is OK;\n  }\n}\n\nfunction _getScrubFieldRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '^\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?$';\n    ret.push(new RegExp(pat, 'i'));\n  }\n  return ret;\n}\n\nfunction _getScrubQueryParamRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?';\n    ret.push(new RegExp('(' + pat + '=)([^&\\\\n]+)', 'igm'));\n  }\n  return ret;\n}\n\nmodule.exports = scrub;\n", "'use strict';\n\nvar _ = require('./utility');\n\nvar MAX_EVENTS = 100;\n\nfunction Telemeter(options) {\n  this.queue = [];\n  this.options = _.merge(options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  this.maxQueueSize = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n}\n\nTelemeter.prototype.configure = function (options) {\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  var newMaxEvents = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n  var deleteCount = 0;\n  if (this.queue.length > newMaxEvents) {\n    deleteCount = this.queue.length - newMaxEvents;\n  }\n  this.maxQueueSize = newMaxEvents;\n  this.queue.splice(0, deleteCount);\n};\n\nTelemeter.prototype.copyEvents = function () {\n  var events = Array.prototype.slice.call(this.queue, 0);\n  if (_.isFunction(this.options.filterTelemetry)) {\n    try {\n      var i = events.length;\n      while (i--) {\n        if (this.options.filterTelemetry(events[i])) {\n          events.splice(i, 1);\n        }\n      }\n    } catch (e) {\n      this.options.filterTelemetry = null;\n    }\n  }\n  return events;\n};\n\nTelemeter.prototype.capture = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var e = {\n    level: getLevel(type, level),\n    type: type,\n    timestamp_ms: timestamp || _.now(),\n    body: metadata,\n    source: 'client',\n  };\n  if (rollbarUUID) {\n    e.uuid = rollbarUUID;\n  }\n\n  try {\n    if (\n      _.isFunction(this.options.filterTelemetry) &&\n      this.options.filterTelemetry(e)\n    ) {\n      return false;\n    }\n  } catch (exc) {\n    this.options.filterTelemetry = null;\n  }\n\n  this.push(e);\n  return e;\n};\n\nTelemeter.prototype.captureEvent = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n) {\n  return this.capture(type, metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.captureError = function (\n  err,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var metadata = {\n    message: err.message || String(err),\n  };\n  if (err.stack) {\n    metadata.stack = err.stack;\n  }\n  return this.capture('error', metadata, level, rollbarUUID, timestamp);\n};\n\nTelemeter.prototype.captureLog = function (\n  message,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  return this.capture(\n    'log',\n    {\n      message: message,\n    },\n    level,\n    rollbarUUID,\n    timestamp,\n  );\n};\n\nTelemeter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n  requestData,\n) {\n  subtype = subtype || 'xhr';\n  metadata.subtype = metadata.subtype || subtype;\n  if (requestData) {\n    metadata.request = requestData;\n  }\n  var level = this.levelFromStatus(metadata.status_code);\n  return this.capture('network', metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.levelFromStatus = function (statusCode) {\n  if (statusCode >= 200 && statusCode < 400) {\n    return 'info';\n  }\n  if (statusCode === 0 || statusCode >= 400) {\n    return 'error';\n  }\n  return 'info';\n};\n\nTelemeter.prototype.captureDom = function (\n  subtype,\n  element,\n  value,\n  checked,\n  rollbarUUID,\n) {\n  var metadata = {\n    subtype: subtype,\n    element: element,\n  };\n  if (value !== undefined) {\n    metadata.value = value;\n  }\n  if (checked !== undefined) {\n    metadata.checked = checked;\n  }\n  return this.capture('dom', metadata, 'info', rollbarUUID);\n};\n\nTelemeter.prototype.captureNavigation = function (from, to, rollbarUUID) {\n  return this.capture(\n    'navigation',\n    { from: from, to: to },\n    'info',\n    rollbarUUID,\n  );\n};\n\nTelemeter.prototype.captureDomContentLoaded = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'DOMContentLoaded' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'DOMContentLoaded'}, 'info', undefined, ts && ts.getTime());\n  */\n};\nTelemeter.prototype.captureLoad = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'load' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'load'}, 'info', undefined, ts && ts.getTime());\n  */\n};\n\nTelemeter.prototype.captureConnectivityChange = function (type, rollbarUUID) {\n  return this.captureNetwork({ change: type }, 'connectivity', rollbarUUID);\n};\n\n// Only intended to be used internally by the notifier\nTelemeter.prototype._captureRollbarItem = function (item) {\n  if (!this.options.includeItemsInTelemetry) {\n    return;\n  }\n  if (item.err) {\n    return this.captureError(item.err, item.level, item.uuid, item.timestamp);\n  }\n  if (item.message) {\n    return this.captureLog(item.message, item.level, item.uuid, item.timestamp);\n  }\n  if (item.custom) {\n    return this.capture(\n      'log',\n      item.custom,\n      item.level,\n      item.uuid,\n      item.timestamp,\n    );\n  }\n};\n\nTelemeter.prototype.push = function (e) {\n  this.queue.push(e);\n  if (this.queue.length > this.maxQueueSize) {\n    this.queue.shift();\n  }\n};\n\nfunction getLevel(type, level) {\n  if (level) {\n    return level;\n  }\n  var defaultLevel = {\n    error: 'error',\n    manual: 'info',\n  };\n  return defaultLevel[type] || 'info';\n}\n\nmodule.exports = Telemeter;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction itemToPayload(item, options, callback) {\n  var data = item.data;\n\n  if (item._isUncaught) {\n    data._isUncaught = true;\n  }\n  if (item._originalArgs) {\n    data._originalArgs = item._originalArgs;\n  }\n  callback(null, data);\n}\n\nfunction addPayloadOptions(item, options, callback) {\n  var payloadOptions = options.payload || {};\n  if (payloadOptions.body) {\n    delete payloadOptions.body;\n  }\n\n  item.data = _.merge(item.data, payloadOptions);\n  callback(null, item);\n}\n\nfunction addTelemetryData(item, options, callback) {\n  if (item.telemetryEvents) {\n    _.set(item, 'data.body.telemetry', item.telemetryEvents);\n  }\n  callback(null, item);\n}\n\nfunction addMessageWithError(item, options, callback) {\n  if (!item.message) {\n    callback(null, item);\n    return;\n  }\n  var tracePath = 'data.body.trace_chain.0';\n  var trace = _.get(item, tracePath);\n  if (!trace) {\n    tracePath = 'data.body.trace';\n    trace = _.get(item, tracePath);\n  }\n  if (trace) {\n    if (!(trace.exception && trace.exception.description)) {\n      _.set(item, tracePath + '.exception.description', item.message);\n      callback(null, item);\n      return;\n    }\n    var extra = _.get(item, tracePath + '.extra') || {};\n    var newExtra = _.merge(extra, { message: item.message });\n    _.set(item, tracePath + '.extra', newExtra);\n  }\n  callback(null, item);\n}\n\nfunction userTransform(logger) {\n  return function (item, options, callback) {\n    var newItem = _.merge(item);\n    var response = null;\n    try {\n      if (_.isFunction(options.transform)) {\n        response = options.transform(newItem.data, item);\n      }\n    } catch (e) {\n      options.transform = null;\n      logger.error(\n        'Error while calling custom transform() function. Removing custom transform().',\n        e,\n      );\n      callback(null, item);\n      return;\n    }\n    if (_.isPromise(response)) {\n      response.then(\n        function (promisedItem) {\n          if (promisedItem) {\n            newItem.data = promisedItem;\n          }\n          callback(null, newItem);\n        },\n        function (error) {\n          callback(error, item);\n        },\n      );\n    } else {\n      callback(null, newItem);\n    }\n  };\n}\n\nfunction addConfigToPayload(item, options, callback) {\n  if (!options.sendConfig) {\n    return callback(null, item);\n  }\n  var configKey = '_rollbarConfig';\n  var custom = _.get(item, 'data.custom') || {};\n  custom[configKey] = options;\n  item.data.custom = custom;\n  callback(null, item);\n}\n\nfunction addFunctionOption(options, name) {\n  if (_.isFunction(options[name])) {\n    options[name] = options[name].toString();\n  }\n}\n\nfunction addConfiguredOptions(item, options, callback) {\n  var configuredOptions = options._configuredOptions;\n\n  // These must be stringified or they'll get dropped during serialization.\n  addFunctionOption(configuredOptions, 'transform');\n  addFunctionOption(configuredOptions, 'checkIgnore');\n  addFunctionOption(configuredOptions, 'onSendCallback');\n\n  delete configuredOptions.accessToken;\n  item.data.notifier.configured_options = configuredOptions;\n  callback(null, item);\n}\n\nfunction addDiagnosticKeys(item, options, callback) {\n  var diagnostic = _.merge(\n    item.notifier.client.notifier.diagnostic,\n    item.diagnostic,\n  );\n\n  if (_.get(item, 'err._isAnonymous')) {\n    diagnostic.is_anonymous = true;\n  }\n\n  if (item._isUncaught) {\n    diagnostic.is_uncaught = item._isUncaught;\n  }\n\n  if (item.err) {\n    try {\n      diagnostic.raw_error = {\n        message: item.err.message,\n        name: item.err.name,\n        constructor_name: item.err.constructor && item.err.constructor.name,\n        filename: item.err.fileName,\n        line: item.err.lineNumber,\n        column: item.err.columnNumber,\n        stack: item.err.stack,\n      };\n    } catch (e) {\n      diagnostic.raw_error = { failed: String(e) };\n    }\n  }\n\n  item.data.notifier.diagnostic = _.merge(\n    item.data.notifier.diagnostic,\n    diagnostic,\n  );\n  callback(null, item);\n}\n\nmodule.exports = {\n  itemToPayload: itemToPayload,\n  addPayloadOptions: addPayloadOptions,\n  addTelemetryData: addTelemetryData,\n  addMessageWithError: addMessageWithError,\n  userTransform: userTransform,\n  addConfigToPayload: addConfigToPayload,\n  addConfiguredOptions: addConfiguredOptions,\n  addDiagnosticKeys: addDiagnosticKeys,\n};\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction raw(payload, jsonBackup) {\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction selectFrames(frames, range) {\n  var len = frames.length;\n  if (len > range * 2) {\n    return frames.slice(0, range).concat(frames.slice(len - range));\n  }\n  return frames;\n}\n\nfunction truncateFrames(payload, jsonBackup, range) {\n  range = typeof range === 'undefined' ? 30 : range;\n  var body = payload.data.body;\n  var frames;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      frames = chain[i].frames;\n      frames = selectFrames(frames, range);\n      chain[i].frames = frames;\n    }\n  } else if (body.trace) {\n    frames = body.trace.frames;\n    frames = selectFrames(frames, range);\n    body.trace.frames = frames;\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction maybeTruncateValue(len, val) {\n  if (!val) {\n    return val;\n  }\n  if (val.length > len) {\n    return val.slice(0, len - 3).concat('...');\n  }\n  return val;\n}\n\nfunction truncateStrings(len, payload, jsonBackup) {\n  function truncator(k, v, seen) {\n    switch (_.typeName(v)) {\n      case 'string':\n        return maybeTruncateValue(len, v);\n      case 'object':\n      case 'array':\n        return traverse(v, truncator, seen);\n      default:\n        return v;\n    }\n  }\n  payload = traverse(payload, truncator);\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction truncateTraceData(traceData) {\n  if (traceData.exception) {\n    delete traceData.exception.description;\n    traceData.exception.message = maybeTruncateValue(\n      255,\n      traceData.exception.message,\n    );\n  }\n  traceData.frames = selectFrames(traceData.frames, 1);\n  return traceData;\n}\n\nfunction minBody(payload, jsonBackup) {\n  var body = payload.data.body;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      chain[i] = truncateTraceData(chain[i]);\n    }\n  } else if (body.trace) {\n    body.trace = truncateTraceData(body.trace);\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction needsTruncation(payload, maxSize) {\n  return _.maxByteSize(payload) > maxSize;\n}\n\nfunction truncate(payload, jsonBackup, maxSize) {\n  maxSize = typeof maxSize === 'undefined' ? 512 * 1024 : maxSize;\n  var strategies = [\n    raw,\n    truncateFrames,\n    truncateStrings.bind(null, 1024),\n    truncateStrings.bind(null, 512),\n    truncateStrings.bind(null, 256),\n    minBody,\n  ];\n  var strategy, results, result;\n\n  while ((strategy = strategies.shift())) {\n    results = strategy(payload, jsonBackup);\n    payload = results[0];\n    result = results[1];\n    if (result.error || !needsTruncation(result.value, maxSize)) {\n      return result;\n    }\n  }\n  return result;\n}\n\nmodule.exports = {\n  truncate: truncate,\n\n  /* for testing */\n  raw: raw,\n  truncateFrames: truncateFrames,\n  truncateStrings: truncateStrings,\n  maybeTruncateValue: maybeTruncateValue,\n};\n", "'use strict';\n\nvar merge = require('./merge');\n\nvar RollbarJSON = {};\nfunction setupJSON(polyfillJSON) {\n  if (isFunction(RollbarJSON.stringify) && isFunction(RollbarJSON.parse)) {\n    return;\n  }\n\n  if (isDefined(JSON)) {\n    // If polyfill is provided, prefer it over existing non-native shims.\n    if (polyfillJSON) {\n      if (isNativeFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isNativeFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    } else {\n      // else accept any interface that is present.\n      if (isFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    }\n  }\n  if (!isFunction(RollbarJSON.stringify) || !isFunction(RollbarJSON.parse)) {\n    polyfillJSON && polyfillJSON(RollbarJSON);\n  }\n}\n\n/*\n * isType - Given a Javascript value and a string, returns true if the type of the value matches the\n * given string.\n *\n * @param x - any value\n * @param t - a lowercase string containing one of the following type names:\n *    - undefined\n *    - null\n *    - error\n *    - number\n *    - boolean\n *    - string\n *    - symbol\n *    - function\n *    - object\n *    - array\n * @returns true if x is of type t, otherwise false\n */\nfunction isType(x, t) {\n  return t === typeName(x);\n}\n\n/*\n * typeName - Given a Javascript value, returns the type of the object as a string\n */\nfunction typeName(x) {\n  var name = typeof x;\n  if (name !== 'object') {\n    return name;\n  }\n  if (!x) {\n    return 'null';\n  }\n  if (x instanceof Error) {\n    return 'error';\n  }\n  return {}.toString\n    .call(x)\n    .match(/\\s([a-zA-Z]+)/)[1]\n    .toLowerCase();\n}\n\n/* isFunction - a convenience function for checking if a value is a function\n *\n * @param f - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isFunction(f) {\n  return isType(f, 'function');\n}\n\n/* isNativeFunction - a convenience function for checking if a value is a native JS function\n *\n * @param f - any value\n * @returns true if f is a native JS function, otherwise false\n */\nfunction isNativeFunction(f) {\n  var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n  var funcMatchString = Function.prototype.toString\n    .call(Object.prototype.hasOwnProperty)\n    .replace(reRegExpChar, '\\\\$&')\n    .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?');\n  var reIsNative = RegExp('^' + funcMatchString + '$');\n  return isObject(f) && reIsNative.test(f);\n}\n\n/* isObject - Checks if the argument is an object\n *\n * @param value - any value\n * @returns true is value is an object function is an object)\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\n/* isString - Checks if the argument is a string\n *\n * @param value - any value\n * @returns true if value is a string\n */\nfunction isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\n\n/**\n * isFiniteNumber - determines whether the passed value is a finite number\n *\n * @param {*} n - any value\n * @returns true if value is a finite number\n */\nfunction isFiniteNumber(n) {\n  return Number.isFinite(n);\n}\n\n/*\n * isDefined - a convenience function for checking if a value is not equal to undefined\n *\n * @param u - any value\n * @returns true if u is anything other than undefined\n */\nfunction isDefined(u) {\n  return !isType(u, 'undefined');\n}\n\n/*\n * isIterable - convenience function for checking if a value can be iterated, essentially\n * whether it is an object or an array.\n *\n * @param i - any value\n * @returns true if i is an object or an array as determined by `typeName`\n */\nfunction isIterable(i) {\n  var type = typeName(i);\n  return type === 'object' || type === 'array';\n}\n\n/*\n * isError - convenience function for checking if a value is of an error type\n *\n * @param e - any value\n * @returns true if e is an error\n */\nfunction isError(e) {\n  // Detect both Error and Firefox Exception type\n  return isType(e, 'error') || isType(e, 'exception');\n}\n\n/* isPromise - a convenience function for checking if a value is a promise\n *\n * @param p - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isPromise(p) {\n  return isObject(p) && isType(p.then, 'function');\n}\n\nfunction redact() {\n  return '********';\n}\n\n// from http://stackoverflow.com/a/8809472/1138191\nfunction uuid4() {\n  var d = now();\n  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(\n    /[xy]/g,\n    function (c) {\n      var r = (d + Math.random() * 16) % 16 | 0;\n      d = Math.floor(d / 16);\n      return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);\n    },\n  );\n  return uuid;\n}\n\nvar LEVELS = {\n  debug: 0,\n  info: 1,\n  warning: 2,\n  error: 3,\n  critical: 4,\n};\n\nfunction sanitizeUrl(url) {\n  var baseUrlParts = parseUri(url);\n  if (!baseUrlParts) {\n    return '(unknown)';\n  }\n\n  // remove a trailing # if there is no anchor\n  if (baseUrlParts.anchor === '') {\n    baseUrlParts.source = baseUrlParts.source.replace('#', '');\n  }\n\n  url = baseUrlParts.source.replace('?' + baseUrlParts.query, '');\n  return url;\n}\n\nvar parseUriOptions = {\n  strictMode: false,\n  key: [\n    'source',\n    'protocol',\n    'authority',\n    'userInfo',\n    'user',\n    'password',\n    'host',\n    'port',\n    'relative',\n    'path',\n    'directory',\n    'file',\n    'query',\n    'anchor',\n  ],\n  q: {\n    name: 'queryKey',\n    parser: /(?:^|&)([^&=]*)=?([^&]*)/g,\n  },\n  parser: {\n    strict:\n      /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n    loose:\n      /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n  },\n};\n\nfunction parseUri(str) {\n  if (!isType(str, 'string')) {\n    return undefined;\n  }\n\n  var o = parseUriOptions;\n  var m = o.parser[o.strictMode ? 'strict' : 'loose'].exec(str);\n  var uri = {};\n\n  for (var i = 0, l = o.key.length; i < l; ++i) {\n    uri[o.key[i]] = m[i] || '';\n  }\n\n  uri[o.q.name] = {};\n  uri[o.key[12]].replace(o.q.parser, function ($0, $1, $2) {\n    if ($1) {\n      uri[o.q.name][$1] = $2;\n    }\n  });\n\n  return uri;\n}\n\nfunction addParamsAndAccessTokenToPath(accessToken, options, params) {\n  params = params || {};\n  params.access_token = accessToken;\n  var paramsArray = [];\n  var k;\n  for (k in params) {\n    if (Object.prototype.hasOwnProperty.call(params, k)) {\n      paramsArray.push([k, params[k]].join('='));\n    }\n  }\n  var query = '?' + paramsArray.sort().join('&');\n\n  options = options || {};\n  options.path = options.path || '';\n  var qs = options.path.indexOf('?');\n  var h = options.path.indexOf('#');\n  var p;\n  if (qs !== -1 && (h === -1 || h > qs)) {\n    p = options.path;\n    options.path = p.substring(0, qs) + query + '&' + p.substring(qs + 1);\n  } else {\n    if (h !== -1) {\n      p = options.path;\n      options.path = p.substring(0, h) + query + p.substring(h);\n    } else {\n      options.path = options.path + query;\n    }\n  }\n}\n\nfunction formatUrl(u, protocol) {\n  protocol = protocol || u.protocol;\n  if (!protocol && u.port) {\n    if (u.port === 80) {\n      protocol = 'http:';\n    } else if (u.port === 443) {\n      protocol = 'https:';\n    }\n  }\n  protocol = protocol || 'https:';\n\n  if (!u.hostname) {\n    return null;\n  }\n  var result = protocol + '//' + u.hostname;\n  if (u.port) {\n    result = result + ':' + u.port;\n  }\n  if (u.path) {\n    result = result + u.path;\n  }\n  return result;\n}\n\nfunction stringify(obj, backup) {\n  var value, error;\n  try {\n    value = RollbarJSON.stringify(obj);\n  } catch (jsonError) {\n    if (backup && isFunction(backup)) {\n      try {\n        value = backup(obj);\n      } catch (backupError) {\n        error = backupError;\n      }\n    } else {\n      error = jsonError;\n    }\n  }\n  return { error: error, value: value };\n}\n\nfunction maxByteSize(string) {\n  // The transport will use utf-8, so assume utf-8 encoding.\n  //\n  // This minimal implementation will accurately count bytes for all UCS-2 and\n  // single code point UTF-16. If presented with multi code point UTF-16,\n  // which should be rare, it will safely overcount, not undercount.\n  //\n  // While robust utf-8 encoders exist, this is far smaller and far more performant.\n  // For quickly counting payload size for truncation, smaller is better.\n\n  var count = 0;\n  var length = string.length;\n\n  for (var i = 0; i < length; i++) {\n    var code = string.charCodeAt(i);\n    if (code < 128) {\n      // up to 7 bits\n      count = count + 1;\n    } else if (code < 2048) {\n      // up to 11 bits\n      count = count + 2;\n    } else if (code < 65536) {\n      // up to 16 bits\n      count = count + 3;\n    }\n  }\n\n  return count;\n}\n\nfunction jsonParse(s) {\n  var value, error;\n  try {\n    value = RollbarJSON.parse(s);\n  } catch (e) {\n    error = e;\n  }\n  return { error: error, value: value };\n}\n\nfunction makeUnhandledStackInfo(\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  mode,\n  backupMessage,\n  errorParser,\n) {\n  var location = {\n    url: url || '',\n    line: lineno,\n    column: colno,\n  };\n  location.func = errorParser.guessFunctionName(location.url, location.line);\n  location.context = errorParser.gatherContext(location.url, location.line);\n  var href =\n    typeof document !== 'undefined' &&\n    document &&\n    document.location &&\n    document.location.href;\n  var useragent =\n    typeof window !== 'undefined' &&\n    window &&\n    window.navigator &&\n    window.navigator.userAgent;\n  return {\n    mode: mode,\n    message: error ? String(error) : message || backupMessage,\n    url: href,\n    stack: [location],\n    useragent: useragent,\n  };\n}\n\nfunction wrapCallback(logger, f) {\n  return function (err, resp) {\n    try {\n      f(err, resp);\n    } catch (e) {\n      logger.error(e);\n    }\n  };\n}\n\nfunction nonCircularClone(obj) {\n  var seen = [obj];\n\n  function clone(obj, seen) {\n    var value,\n      name,\n      newSeen,\n      result = {};\n\n    try {\n      for (name in obj) {\n        value = obj[name];\n\n        if (value && (isType(value, 'object') || isType(value, 'array'))) {\n          if (seen.includes(value)) {\n            result[name] = 'Removed circular reference: ' + typeName(value);\n          } else {\n            newSeen = seen.slice();\n            newSeen.push(value);\n            result[name] = clone(value, newSeen);\n          }\n          continue;\n        }\n\n        result[name] = value;\n      }\n    } catch (e) {\n      result = 'Failed cloning custom data: ' + e.message;\n    }\n    return result;\n  }\n  return clone(obj, seen);\n}\n\nfunction createItem(args, logger, notifier, requestKeys, lambdaContext) {\n  var message, err, custom, callback, request;\n  var arg;\n  var extraArgs = [];\n  var diagnostic = {};\n  var argTypes = [];\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    argTypes.push(typ);\n    switch (typ) {\n      case 'undefined':\n        break;\n      case 'string':\n        message ? extraArgs.push(arg) : (message = arg);\n        break;\n      case 'function':\n        callback = wrapCallback(logger, arg);\n        break;\n      case 'date':\n        extraArgs.push(arg);\n        break;\n      case 'error':\n      case 'domexception':\n      case 'exception': // Firefox Exception type\n        err ? extraArgs.push(arg) : (err = arg);\n        break;\n      case 'object':\n      case 'array':\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        if (requestKeys && typ === 'object' && !request) {\n          for (var j = 0, len = requestKeys.length; j < len; ++j) {\n            if (arg[requestKeys[j]] !== undefined) {\n              request = arg;\n              break;\n            }\n          }\n          if (request) {\n            break;\n          }\n        }\n        custom ? extraArgs.push(arg) : (custom = arg);\n        break;\n      default:\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        extraArgs.push(arg);\n    }\n  }\n\n  // if custom is an array this turns it into an object with integer keys\n  if (custom) custom = nonCircularClone(custom);\n\n  if (extraArgs.length > 0) {\n    if (!custom) custom = nonCircularClone({});\n    custom.extraArgs = nonCircularClone(extraArgs);\n  }\n\n  var item = {\n    message: message,\n    err: err,\n    custom: custom,\n    timestamp: now(),\n    callback: callback,\n    notifier: notifier,\n    diagnostic: diagnostic,\n    uuid: uuid4(),\n  };\n\n  setCustomItemKeys(item, custom);\n\n  if (requestKeys && request) {\n    item.request = request;\n  }\n  if (lambdaContext) {\n    item.lambdaContext = lambdaContext;\n  }\n  item._originalArgs = args;\n  item.diagnostic.original_arg_types = argTypes;\n  return item;\n}\n\nfunction setCustomItemKeys(item, custom) {\n  if (custom && custom.level !== undefined) {\n    item.level = custom.level;\n    delete custom.level;\n  }\n  if (custom && custom.skipFrames !== undefined) {\n    item.skipFrames = custom.skipFrames;\n    delete custom.skipFrames;\n  }\n}\n\nfunction addErrorContext(item, errors) {\n  var custom = item.data.custom || {};\n  var contextAdded = false;\n\n  try {\n    for (var i = 0; i < errors.length; ++i) {\n      if (errors[i].hasOwnProperty('rollbarContext')) {\n        custom = merge(custom, nonCircularClone(errors[i].rollbarContext));\n        contextAdded = true;\n      }\n    }\n\n    // Avoid adding an empty object to the data.\n    if (contextAdded) {\n      item.data.custom = custom;\n    }\n  } catch (e) {\n    item.diagnostic.error_context = 'Failed: ' + e.message;\n  }\n}\n\nvar TELEMETRY_TYPES = [\n  'log',\n  'network',\n  'dom',\n  'navigation',\n  'error',\n  'manual',\n];\nvar TELEMETRY_LEVELS = ['critical', 'error', 'warning', 'info', 'debug'];\n\nfunction arrayIncludes(arr, val) {\n  for (var k = 0; k < arr.length; ++k) {\n    if (arr[k] === val) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction createTelemetryEvent(args) {\n  var type, metadata, level;\n  var arg;\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    switch (typ) {\n      case 'string':\n        if (!type && arrayIncludes(TELEMETRY_TYPES, arg)) {\n          type = arg;\n        } else if (!level && arrayIncludes(TELEMETRY_LEVELS, arg)) {\n          level = arg;\n        }\n        break;\n      case 'object':\n        metadata = arg;\n        break;\n      default:\n        break;\n    }\n  }\n  var event = {\n    type: type || 'manual',\n    metadata: metadata || {},\n    level: level,\n  };\n\n  return event;\n}\n\n/*\n * get - given an obj/array and a keypath, return the value at that keypath or\n *       undefined if not possible.\n *\n * @param obj - an object or array\n * @param path - a string of keys separated by '.' such as 'plugin.jquery.0.message'\n *    which would correspond to 42 in `{plugin: {jquery: [{message: 42}]}}`\n */\nfunction get(obj, path) {\n  if (!obj) {\n    return undefined;\n  }\n  var keys = path.split('.');\n  var result = obj;\n  try {\n    for (var i = 0, len = keys.length; i < len; ++i) {\n      result = result[keys[i]];\n    }\n  } catch (e) {\n    result = undefined;\n  }\n  return result;\n}\n\nfunction set(obj, path, value) {\n  if (!obj) {\n    return;\n  }\n  var keys = path.split('.');\n  var len = keys.length;\n  if (len < 1) {\n    return;\n  }\n  if (len === 1) {\n    obj[keys[0]] = value;\n    return;\n  }\n  try {\n    var temp = obj[keys[0]] || {};\n    var replacement = temp;\n    for (var i = 1; i < len - 1; ++i) {\n      temp[keys[i]] = temp[keys[i]] || {};\n      temp = temp[keys[i]];\n    }\n    temp[keys[len - 1]] = value;\n    obj[keys[0]] = replacement;\n  } catch (e) {\n    return;\n  }\n}\n\nfunction formatArgsAsString(args) {\n  var i, len, arg;\n  var result = [];\n  for (i = 0, len = args.length; i < len; ++i) {\n    arg = args[i];\n    switch (typeName(arg)) {\n      case 'object':\n        arg = stringify(arg);\n        arg = arg.error || arg.value;\n        if (arg.length > 500) {\n          arg = arg.substr(0, 497) + '...';\n        }\n        break;\n      case 'null':\n        arg = 'null';\n        break;\n      case 'undefined':\n        arg = 'undefined';\n        break;\n      case 'symbol':\n        arg = arg.toString();\n        break;\n    }\n    result.push(arg);\n  }\n  return result.join(' ');\n}\n\nfunction now() {\n  if (Date.now) {\n    return +Date.now();\n  }\n  return +new Date();\n}\n\nfunction filterIp(requestData, captureIp) {\n  if (!requestData || !requestData['user_ip'] || captureIp === true) {\n    return;\n  }\n  var newIp = requestData['user_ip'];\n  if (!captureIp) {\n    newIp = null;\n  } else {\n    try {\n      var parts;\n      if (newIp.indexOf('.') !== -1) {\n        parts = newIp.split('.');\n        parts.pop();\n        parts.push('0');\n        newIp = parts.join('.');\n      } else if (newIp.indexOf(':') !== -1) {\n        parts = newIp.split(':');\n        if (parts.length > 2) {\n          var beginning = parts.slice(0, 3);\n          var slashIdx = beginning[2].indexOf('/');\n          if (slashIdx !== -1) {\n            beginning[2] = beginning[2].substring(0, slashIdx);\n          }\n          var terminal = '0000:0000:0000:0000:0000';\n          newIp = beginning.concat(terminal).join(':');\n        }\n      } else {\n        newIp = null;\n      }\n    } catch (e) {\n      newIp = null;\n    }\n  }\n  requestData['user_ip'] = newIp;\n}\n\nfunction handleOptions(current, input, payload, logger) {\n  var result = merge(current, input, payload);\n  result = updateDeprecatedOptions(result, logger);\n  if (!input || input.overwriteScrubFields) {\n    return result;\n  }\n  if (input.scrubFields) {\n    result.scrubFields = (current.scrubFields || []).concat(input.scrubFields);\n  }\n  return result;\n}\n\nfunction updateDeprecatedOptions(options, logger) {\n  if (options.hostWhiteList && !options.hostSafeList) {\n    options.hostSafeList = options.hostWhiteList;\n    options.hostWhiteList = undefined;\n    logger && logger.log('hostWhiteList is deprecated. Use hostSafeList.');\n  }\n  if (options.hostBlackList && !options.hostBlockList) {\n    options.hostBlockList = options.hostBlackList;\n    options.hostBlackList = undefined;\n    logger && logger.log('hostBlackList is deprecated. Use hostBlockList.');\n  }\n  return options;\n}\n\nmodule.exports = {\n  addParamsAndAccessTokenToPath: addParamsAndAccessTokenToPath,\n  createItem: createItem,\n  addErrorContext: addErrorContext,\n  createTelemetryEvent: createTelemetryEvent,\n  filterIp: filterIp,\n  formatArgsAsString: formatArgsAsString,\n  formatUrl: formatUrl,\n  get: get,\n  handleOptions: handleOptions,\n  isError: isError,\n  isFiniteNumber: isFiniteNumber,\n  isFunction: isFunction,\n  isIterable: isIterable,\n  isNativeFunction: isNativeFunction,\n  isObject: isObject,\n  isString: isString,\n  isType: isType,\n  isPromise: isPromise,\n  jsonParse: jsonParse,\n  LEVELS: LEVELS,\n  makeUnhandledStackInfo: makeUnhandledStackInfo,\n  merge: merge,\n  now: now,\n  redact: redact,\n  RollbarJSON: RollbarJSON,\n  sanitizeUrl: sanitizeUrl,\n  set: set,\n  setupJSON: setupJSON,\n  stringify: stringify,\n  maxByteSize: maxByteSize,\n  typeName: typeName,\n  uuid4: uuid4,\n};\n", "'use strict';\n\n/*\n * headers - Detect when fetch Headers are undefined and use a partial polyfill.\n *\n * A full polyfill is not used in order to keep package size as small as possible.\n * Since this is only used internally and is not added to the window object,\n * the full interface doesn't need to be supported.\n *\n * This implementation is modified from whatwg-fetch:\n * https://github.com/github/fetch\n */\nfunction headers(headers) {\n  if (typeof Headers === 'undefined') {\n    return new FetchHeaders(headers);\n  }\n\n  return new Headers(headers);\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name);\n  }\n  return name.toLowerCase();\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value);\n  }\n  return value;\n}\n\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function () {\n      var value = items.shift();\n      return { done: value === undefined, value: value };\n    },\n  };\n\n  return iterator;\n}\n\nfunction FetchHeaders(headers) {\n  this.map = {};\n\n  if (headers instanceof FetchHeaders) {\n    headers.forEach(function (value, name) {\n      this.append(name, value);\n    }, this);\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function (header) {\n      this.append(header[0], header[1]);\n    }, this);\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function (name) {\n      this.append(name, headers[name]);\n    }, this);\n  }\n}\n\nFetchHeaders.prototype.append = function (name, value) {\n  name = normalizeName(name);\n  value = normalizeValue(value);\n  var oldValue = this.map[name];\n  this.map[name] = oldValue ? oldValue + ', ' + value : value;\n};\n\nFetchHeaders.prototype.get = function (name) {\n  name = normalizeName(name);\n  return this.has(name) ? this.map[name] : null;\n};\n\nFetchHeaders.prototype.has = function (name) {\n  return this.map.hasOwnProperty(normalizeName(name));\n};\n\nFetchHeaders.prototype.forEach = function (callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this);\n    }\n  }\n};\n\nFetchHeaders.prototype.entries = function () {\n  var items = [];\n  this.forEach(function (value, name) {\n    items.push([name, value]);\n  });\n  return iteratorFor(items);\n};\n\nmodule.exports = headers;\n", "'use strict';\n\nvar polyfillJSON = require('../../vendor/JSON-js/json3');\n\nmodule.exports = polyfillJSON;\n", "'use strict';\n\nfunction replace(obj, name, replacement, replacements, type) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (replacements) {\n    replacements[type].push([obj, name, orig]);\n  }\n}\n\nmodule.exports = replace;\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction traverse(obj, func, seen) {\n  var k, v, i;\n  var isObj = _.isType(obj, 'object');\n  var isArray = _.isType(obj, 'array');\n  var keys = [];\n  var seenIndex;\n\n  // Best might be to use Map here with `obj` as the keys, but we want to support IE < 11.\n  seen = seen || { obj: [], mapped: [] };\n\n  if (isObj) {\n    seenIndex = seen.obj.indexOf(obj);\n\n    if (isObj && seenIndex !== -1) {\n      // Prefer the mapped object if there is one.\n      return seen.mapped[seenIndex] || seen.obj[seenIndex];\n    }\n\n    seen.obj.push(obj);\n    seenIndex = seen.obj.length - 1;\n  }\n\n  if (isObj) {\n    for (k in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, k)) {\n        keys.push(k);\n      }\n    }\n  } else if (isArray) {\n    for (i = 0; i < obj.length; ++i) {\n      keys.push(i);\n    }\n  }\n\n  var result = isObj ? {} : [];\n  var same = true;\n  for (i = 0; i < keys.length; ++i) {\n    k = keys[i];\n    v = obj[k];\n    result[k] = func(k, v, seen);\n    same = same && result[k] === obj[k];\n  }\n\n  if (isObj && !same) {\n    seen.mapped[seenIndex] = result;\n  }\n\n  return !same ? result : obj;\n}\n\nmodule.exports = traverse;\n", "//  json3.js\n//  2017-02-21\n//  Public Domain.\n//  NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n//  See http://www.JSON.org/js.html\n//  This code should be minified before deployment.\n//  See http://javascript.crockford.com/jsmin.html\n\n//  USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n//  NOT CONTROL.\n\n//  This file creates a global JSON object containing two methods: stringify\n//  and parse. This file provides the ES5 JSON capability to ES3 systems.\n//  If a project might run on IE8 or earlier, then this file should be included.\n//  This file does nothing on ES5 systems.\n\n//      JSON.stringify(value, replacer, space)\n//          value       any JavaScript value, usually an object or array.\n//          replacer    an optional parameter that determines how object\n//                      values are stringified for objects. It can be a\n//                      function or an array of strings.\n//          space       an optional parameter that specifies the indentation\n//                      of nested structures. If it is omitted, the text will\n//                      be packed without extra whitespace. If it is a number,\n//                      it will specify the number of spaces to indent at each\n//                      level. If it is a string (such as \"\\t\" or \"&nbsp;\"),\n//                      it contains the characters used to indent at each level.\n//          This method produces a JSON text from a JavaScript value.\n//          When an object value is found, if the object contains a toJSON\n//          method, its toJSON method will be called and the result will be\n//          stringified. A toJSON method does not serialize: it returns the\n//          value represented by the name/value pair that should be serialized,\n//          or undefined if nothing should be serialized. The toJSON method\n//          will be passed the key associated with the value, and this will be\n//          bound to the value.\n\n//          For example, this would serialize Dates as ISO strings.\n\n//              Date.prototype.toJSON = function (key) {\n//                  function f(n) {\n//                      // Format integers to have at least two digits.\n//                      return (n < 10)\n//                          ? \"0\" + n\n//                          : n;\n//                  }\n//                  return this.getUTCFullYear()   + \"-\" +\n//                       f(this.getUTCMonth() + 1) + \"-\" +\n//                       f(this.getUTCDate())      + \"T\" +\n//                       f(this.getUTCHours())     + \":\" +\n//                       f(this.getUTCMinutes())   + \":\" +\n//                       f(this.getUTCSeconds())   + \"Z\";\n//              };\n\n//          You can provide an optional replacer method. It will be passed the\n//          key and value of each member, with this bound to the containing\n//          object. The value that is returned from your method will be\n//          serialized. If your method returns undefined, then the member will\n//          be excluded from the serialization.\n\n//          If the replacer parameter is an array of strings, then it will be\n//          used to select the members to be serialized. It filters the results\n//          such that only members with keys listed in the replacer array are\n//          stringified.\n\n//          Values that do not have JSON representations, such as undefined or\n//          functions, will not be serialized. Such values in objects will be\n//          dropped; in arrays they will be replaced with null. You can use\n//          a replacer function to replace those with JSON values.\n\n//          JSON.stringify(undefined) returns undefined.\n\n//          The optional space parameter produces a stringification of the\n//          value that is filled with line breaks and indentation to make it\n//          easier to read.\n\n//          If the space parameter is a non-empty string, then that string will\n//          be used for indentation. If the space parameter is a number, then\n//          the indentation will be that many spaces.\n\n//          Example:\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}]);\n//          // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}], null, \"\\t\");\n//          // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n//          text = JSON.stringify([new Date()], function (key, value) {\n//              return this[key] instanceof Date\n//                  ? \"Date(\" + this[key] + \")\"\n//                  : value;\n//          });\n//          // text is '[\"Date(---current time---)\"]'\n\n//      JSON.parse(text, reviver)\n//          This method parses a JSON text to produce an object or array.\n//          It can throw a SyntaxError exception.\n//          This has been modified to use JSON-js/json_parse_state.js as the\n//          parser instead of the one built around eval found in JSON-js/json2.js\n\n//          The optional reviver parameter is a function that can filter and\n//          transform the results. It receives each of the keys and values,\n//          and its return value is used instead of the original value.\n//          If it returns what it received, then the structure is not modified.\n//          If it returns undefined then the member is deleted.\n\n//          Example:\n\n//          // Parse the text. Values that look like ISO date strings will\n//          // be converted to Date objects.\n\n//          myData = JSON.parse(text, function (key, value) {\n//              var a;\n//              if (typeof value === \"string\") {\n//                  a =\n//   /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n//                  if (a) {\n//                      return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n//                          +a[5], +a[6]));\n//                  }\n//              }\n//              return value;\n//          });\n\n//          myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n//              var d;\n//              if (typeof value === \"string\" &&\n//                      value.slice(0, 5) === \"Date(\" &&\n//                      value.slice(-1) === \")\") {\n//                  d = new Date(value.slice(5, -1));\n//                  if (d) {\n//                      return d;\n//                  }\n//              }\n//              return value;\n//          });\n\n//  This is a reference implementation. You are free to copy, modify, or\n//  redistribute.\n\n/*jslint\n  for, this\n  */\n\n/*property\n  JSON, apply, call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n  getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n  lastIndex, length, parse, prototype, push, replace, slice, stringify,\n  test, toJSON, toString, valueOf\n  */\n\nvar setupCustomJSON = function(JSON) {\n\n  var rx_one = /^[\\],:{}\\s]*$/;\n  var rx_two = /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9a-fA-F]{4})/g;\n  var rx_three = /\"[^\"\\\\\\n\\r]*\"|true|false|null|-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\n  var rx_four = /(?:^|:|,)(?:\\s*\\[)+/g;\n  var rx_escapable = /[\\\\\"\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n  var rx_dangerous = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n  function f(n) {\n    // Format integers to have at least two digits.\n    return n < 10\n      ? \"0\" + n\n      : n;\n  }\n\n  function this_value() {\n    return this.valueOf();\n  }\n\n  if (typeof Date.prototype.toJSON !== \"function\") {\n\n    Date.prototype.toJSON = function () {\n\n      return isFinite(this.valueOf())\n        ? this.getUTCFullYear() + \"-\" +\n        f(this.getUTCMonth() + 1) + \"-\" +\n        f(this.getUTCDate()) + \"T\" +\n        f(this.getUTCHours()) + \":\" +\n        f(this.getUTCMinutes()) + \":\" +\n        f(this.getUTCSeconds()) + \"Z\"\n        : null;\n    };\n\n    Boolean.prototype.toJSON = this_value;\n    Number.prototype.toJSON = this_value;\n    String.prototype.toJSON = this_value;\n  }\n\n  var gap;\n  var indent;\n  var meta;\n  var rep;\n\n\n  function quote(string) {\n\n    // If the string contains no control characters, no quote characters, and no\n    // backslash characters, then we can safely slap some quotes around it.\n    // Otherwise we must also replace the offending characters with safe escape\n    // sequences.\n\n    rx_escapable.lastIndex = 0;\n    return rx_escapable.test(string)\n      ? \"\\\"\" + string.replace(rx_escapable, function (a) {\n        var c = meta[a];\n        return typeof c === \"string\"\n          ? c\n          : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n      }) + \"\\\"\"\n    : \"\\\"\" + string + \"\\\"\";\n  }\n\n\n  function str(key, holder) {\n\n    // Produce a string from holder[key].\n\n    var i;          // The loop counter.\n    var k;          // The member key.\n    var v;          // The member value.\n    var length;\n    var mind = gap;\n    var partial;\n    var value = holder[key];\n\n    // If the value has a toJSON method, call it to obtain a replacement value.\n\n    if (value && typeof value === \"object\" &&\n        typeof value.toJSON === \"function\") {\n      value = value.toJSON(key);\n    }\n\n    // If we were called with a replacer function, then call the replacer to\n    // obtain a replacement value.\n\n    if (typeof rep === \"function\") {\n      value = rep.call(holder, key, value);\n    }\n\n    // What happens next depends on the value's type.\n\n    switch (typeof value) {\n      case \"string\":\n        return quote(value);\n\n      case \"number\":\n\n        // JSON numbers must be finite. Encode non-finite numbers as null.\n\n        return isFinite(value)\n          ? String(value)\n          : \"null\";\n\n      case \"boolean\":\n      case \"null\":\n\n        // If the value is a boolean or null, convert it to a string. Note:\n        // typeof null does not produce \"null\". The case is included here in\n        // the remote chance that this gets fixed someday.\n\n        return String(value);\n\n        // If the type is \"object\", we might be dealing with an object or an array or\n        // null.\n\n      case \"object\":\n\n        // Due to a specification blunder in ECMAScript, typeof null is \"object\",\n        // so watch out for that case.\n\n        if (!value) {\n          return \"null\";\n        }\n\n        // Make an array to hold the partial results of stringifying this object value.\n\n        gap += indent;\n        partial = [];\n\n        // Is the value an array?\n\n        if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n\n          // The value is an array. Stringify every element. Use null as a placeholder\n          // for non-JSON values.\n\n          length = value.length;\n          for (i = 0; i < length; i += 1) {\n            partial[i] = str(i, value) || \"null\";\n          }\n\n          // Join all of the elements together, separated with commas, and wrap them in\n          // brackets.\n\n          v = partial.length === 0\n            ? \"[]\"\n            : gap\n            ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\"\n            : \"[\" + partial.join(\",\") + \"]\";\n          gap = mind;\n          return v;\n        }\n\n        // If the replacer is an array, use it to select the members to be stringified.\n\n        if (rep && typeof rep === \"object\") {\n          length = rep.length;\n          for (i = 0; i < length; i += 1) {\n            if (typeof rep[i] === \"string\") {\n              k = rep[i];\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        } else {\n\n          // Otherwise, iterate through all of the keys in the object.\n\n          for (k in value) {\n            if (Object.prototype.hasOwnProperty.call(value, k)) {\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        }\n\n        // Join all of the member texts together, separated with commas,\n        // and wrap them in braces.\n\n        v = partial.length === 0\n          ? \"{}\"\n          : gap\n          ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n          : \"{\" + partial.join(\",\") + \"}\";\n        gap = mind;\n        return v;\n    }\n  }\n\n  // If the JSON object does not yet have a stringify method, give it one.\n\n  if (typeof JSON.stringify !== \"function\") {\n    meta = {    // table of character substitutions\n      \"\\b\": \"\\\\b\",\n      \"\\t\": \"\\\\t\",\n      \"\\n\": \"\\\\n\",\n      \"\\f\": \"\\\\f\",\n      \"\\r\": \"\\\\r\",\n      \"\\\"\": \"\\\\\\\"\",\n      \"\\\\\": \"\\\\\\\\\"\n    };\n    JSON.stringify = function (value, replacer, space) {\n\n      // The stringify method takes a value and an optional replacer, and an optional\n      // space parameter, and returns a JSON text. The replacer can be a function\n      // that can replace values, or an array of strings that will select the keys.\n      // A default replacer method can be provided. Use of the space parameter can\n      // produce text that is more easily readable.\n\n      var i;\n      gap = \"\";\n      indent = \"\";\n\n      // If the space parameter is a number, make an indent string containing that\n      // many spaces.\n\n      if (typeof space === \"number\") {\n        for (i = 0; i < space; i += 1) {\n          indent += \" \";\n        }\n\n        // If the space parameter is a string, it will be used as the indent string.\n\n      } else if (typeof space === \"string\") {\n        indent = space;\n      }\n\n      // If there is a replacer, it must be a function or an array.\n      // Otherwise, throw an error.\n\n      rep = replacer;\n      if (replacer && typeof replacer !== \"function\" &&\n          (typeof replacer !== \"object\" ||\n           typeof replacer.length !== \"number\")) {\n        throw new Error(\"JSON.stringify\");\n      }\n\n      // Make a fake root object containing our value under the key of \"\".\n      // Return the result of stringifying the value.\n\n      return str(\"\", {\"\": value});\n    };\n  }\n\n\n  // If the JSON object does not yet have a parse method, give it one.\n\n  if (typeof JSON.parse !== \"function\") {\n    JSON.parse = (function () {\n\n      // This function creates a JSON parse function that uses a state machine rather\n      // than the dangerous eval function to parse a JSON text.\n\n      var state;      // The state of the parser, one of\n      // 'go'         The starting state\n      // 'ok'         The final, accepting state\n      // 'firstokey'  Ready for the first key of the object or\n      //              the closing of an empty object\n      // 'okey'       Ready for the next key of the object\n      // 'colon'      Ready for the colon\n      // 'ovalue'     Ready for the value half of a key/value pair\n      // 'ocomma'     Ready for a comma or closing }\n      // 'firstavalue' Ready for the first value of an array or\n      //              an empty array\n      // 'avalue'     Ready for the next value of an array\n      // 'acomma'     Ready for a comma or closing ]\n      var stack;      // The stack, for controlling nesting.\n      var container;  // The current container object or array\n      var key;        // The current key\n      var value;      // The current value\n      var escapes = { // Escapement translation table\n        \"\\\\\": \"\\\\\",\n        \"\\\"\": \"\\\"\",\n        \"/\": \"/\",\n        \"t\": \"\\t\",\n        \"n\": \"\\n\",\n        \"r\": \"\\r\",\n        \"f\": \"\\f\",\n        \"b\": \"\\b\"\n      };\n      var string = {   // The actions for string tokens\n        go: function () {\n          state = \"ok\";\n        },\n        firstokey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        okey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var number = {   // The actions for number tokens\n        go: function () {\n          state = \"ok\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var action = {\n\n        // The action table describes the behavior of the machine. It contains an\n        // object for each token. Each object contains a method that is called when\n        // a token is matched in a state. An object will lack a method for illegal\n        // states.\n\n        \"{\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = {};\n            state = \"firstokey\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          }\n        },\n        \"}\": {\n          firstokey: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          ocomma: function () {\n            var pop = stack.pop();\n            container[key] = value;\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \"[\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = [];\n            state = \"firstavalue\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          }\n        },\n        \"]\": {\n          firstavalue: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          acomma: function () {\n            var pop = stack.pop();\n            container.push(value);\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \":\": {\n          colon: function () {\n            if (Object.hasOwnProperty.call(container, key)) {\n              throw new SyntaxError(\"Duplicate key '\" + key + \"\\\"\");\n            }\n            state = \"ovalue\";\n          }\n        },\n        \",\": {\n          ocomma: function () {\n            container[key] = value;\n            state = \"okey\";\n          },\n          acomma: function () {\n            container.push(value);\n            state = \"avalue\";\n          }\n        },\n        \"true\": {\n          go: function () {\n            value = true;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = true;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = true;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = true;\n            state = \"acomma\";\n          }\n        },\n        \"false\": {\n          go: function () {\n            value = false;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = false;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = false;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = false;\n            state = \"acomma\";\n          }\n        },\n        \"null\": {\n          go: function () {\n            value = null;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = null;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = null;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = null;\n            state = \"acomma\";\n          }\n        }\n      };\n\n      function debackslashify(text) {\n\n        // Remove and replace any backslash escapement.\n\n        return text.replace(/\\\\(?:u(.{4})|([^u]))/g, function (ignore, b, c) {\n          return b\n            ? String.fromCharCode(parseInt(b, 16))\n            : escapes[c];\n        });\n      }\n\n      return function (source, reviver) {\n\n        // A regular expression is used to extract tokens from the JSON text.\n        // The extraction process is cautious.\n\n        var result;\n        var tx = /^[\\u0020\\t\\n\\r]*(?:([,:\\[\\]{}]|true|false|null)|(-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)|\"((?:[^\\r\\n\\t\\\\\\\"]|\\\\(?:[\"\\\\\\/trnfb]|u[0-9a-fA-F]{4}))*)\")/;\n\n        // Set the starting state.\n\n        state = \"go\";\n\n        // The stack records the container, key, and state for each object or array\n        // that contains another object or array while processing nested structures.\n\n        stack = [];\n\n        // If any error occurs, we will catch it and ultimately throw a syntax error.\n\n        try {\n\n          // For each token...\n\n          while (true) {\n            result = tx.exec(source);\n            if (!result) {\n              break;\n            }\n\n            // result is the result array from matching the tokenizing regular expression.\n            //  result[0] contains everything that matched, including any initial whitespace.\n            //  result[1] contains any punctuation that was matched, or true, false, or null.\n            //  result[2] contains a matched number, still in string form.\n            //  result[3] contains a matched string, without quotes but with escapement.\n\n            if (result[1]) {\n\n              // Token: Execute the action for this state and token.\n\n              action[result[1]][state]();\n\n            } else if (result[2]) {\n\n              // Number token: Convert the number string into a number value and execute\n              // the action for this state and number.\n\n              value = +result[2];\n              number[state]();\n            } else {\n\n              // String token: Replace the escapement sequences and execute the action for\n              // this state and string.\n\n              value = debackslashify(result[3]);\n              string[state]();\n            }\n\n            // Remove the token from the string. The loop will continue as long as there\n            // are tokens. This is a slow process, but it allows the use of ^ matching,\n            // which assures that no illegal tokens slip through.\n\n            source = source.slice(result[0].length);\n          }\n\n          // If we find a state/token combination that is illegal, then the action will\n          // cause an error. We handle the error by simply changing the state.\n\n        } catch (e) {\n          state = e;\n        }\n\n        // The parsing is finished. If we are not in the final \"ok\" state, or if the\n        // remaining source contains anything except whitespace, then we did not have\n        //a well-formed JSON text.\n\n        if (state !== \"ok\" || (/[^\\u0020\\t\\n\\r]/.test(source))) {\n          throw (state instanceof SyntaxError)\n            ? state\n            : new SyntaxError(\"JSON\");\n        }\n\n        // If there is a reviver function, we recursively walk the new structure,\n        // passing each name/value pair to the reviver function for possible\n        // transformation, starting with a temporary root object that holds the current\n        // value in an empty key. If there is not a reviver function, we simply return\n        // that value.\n\n        return (typeof reviver === \"function\")\n          ? (function walk(holder, key) {\n            var k;\n            var v;\n            var val = holder[key];\n            if (val && typeof val === \"object\") {\n              for (k in value) {\n                if (Object.prototype.hasOwnProperty.call(val, k)) {\n                  v = walk(val, k);\n                  if (v !== undefined) {\n                    val[k] = v;\n                  } else {\n                    delete val[k];\n                  }\n                }\n              }\n            }\n            return reviver.call(holder, key, val);\n          }({\"\": value}, \"\"))\n        : value;\n      };\n    }());\n  }\n}\n\nmodule.exports = setupCustomJSON;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(409);\n"], "names": ["global", "console", "prop", "method", "con", "dummy", "properties", "methods", "split", "pop", "window", "this", "StackFrame", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "Error", "extractLocation", "urlLike", "indexOf", "parts", "exec", "replace", "undefined", "filter", "line", "map", "sanitizedLine", "location", "tokens", "slice", "locationParts", "functionName", "join", "fileName", "lineNumber", "columnNumber", "source", "functionNameRegex", "matches", "e", "message", "length", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "i", "len", "push", "argsRaw", "functionCall", "shift", "args", "_isNumber", "n", "isNaN", "parseFloat", "isFinite", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "p", "booleanProps", "numericProps", "stringProps", "arrayProps", "objectProps", "props", "concat", "obj", "prototype", "getArgs", "set<PERSON>rgs", "v", "Object", "toString", "call", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "getLineNumber", "getColumnNumber", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "argsEndIndex", "lastIndexOf", "locationString", "Boolean", "j", "Number", "k", "String", "_", "helpers", "defaultOptions", "hostname", "path", "search", "version", "protocol", "port", "Api", "options", "transport", "urllib", "truncation", "jsonBackup", "url", "accessToken", "transportOptions", "_getTransport", "getTransportFromOptions", "postItem", "data", "callback", "payload", "buildPayload", "self", "setTimeout", "post", "buildJsonPayload", "stringifyResult", "truncate", "stringify", "value", "postJsonPayload", "jsonPayload", "configure", "oldOptions", "merge", "module", "exports", "isType", "context", "contextResult", "substr", "access_token", "defaults", "timeout", "gWindow", "defaultTransport", "fetch", "XMLHttpRequest", "detectTransport", "proxy", "endpoint", "opts", "pathname", "transportAPI", "host", "appendPathToPath", "base", "baseTrailingSlash", "test", "pathBeginningSlash", "rollbar", "_rollbarConfig", "alias", "globalAlias", "shim<PERSON>unning", "shimId", "_rollbarStartTime", "Date", "getTime", "<PERSON><PERSON>", "_rollbarDidLoad", "Client", "API", "logger", "globals", "Transport", "transforms", "sharedTransforms", "predicates", "sharedPredicates", "error<PERSON><PERSON>er", "client", "handleOptions", "_configuredOptions", "Telemeter", "components", "telemeter", "Instrumenter", "instrumenter", "polyfillJSON", "wrapGlobals", "scrub", "api", "_gWindow", "gDocument", "document", "isChrome", "chrome", "runtime", "anonymousErrorsPending", "notifier", "addTransform", "handleDomException", "handleItemWithError", "ensureItemHasSomethingToSay", "addBaseInfo", "addRequestInfo", "addClientInfo", "addPluginInfo", "addBody", "addMessageWithError", "addTelemetryData", "addConfigToPayload", "addScrubber", "addPayloadOptions", "userTransform", "addConfiguredOptions", "addDiagnosticKeys", "itemToPayload", "addTransformsToNotifier", "queue", "addPredicate", "checkLevel", "checkIgnore", "userCheckIgnore", "urlIsNotBlockListed", "urlIsSafeListed", "messageIsIgnored", "setupUnhandledCapture", "instrument", "setupJSON", "_instance", "handleUninitialized", "maybe<PERSON><PERSON><PERSON>", "_getFirstFunction", "isFunction", "init", "setComponents", "payloadData", "lastError", "log", "item", "_createItem", "arguments", "uuid", "apply", "debug", "info", "warn", "warning", "critical", "sendJsonPayload", "unhandledExceptionsInitialized", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUncaughtExceptions", "captureUncaughtExceptions", "wrapGlobalEventHandlers", "unhandledRejectionsInitialized", "captureUnhandledRejections", "handleUnhandledRejections", "handleUncaughtException", "lineno", "colno", "inspectAnonymousErrors", "stackInfo", "makeUnhandledStackInfo", "isError", "_unhandledStackInfo", "level", "uncaughtErrorLevel", "_is<PERSON><PERSON><PERSON>t", "handleAnonymousErrors", "r", "prepareStackTrace", "_stack", "_isAnonymous", "handleUnhandledRejection", "reason", "promise", "reasonResult", "_rollbarContext", "_originalArgs", "wrap", "f", "_before", "ctxFn", "_isWrap", "_rollbar_wrapped", "exc", "_rollbarWrappedError", "_wrappedSource", "hasOwnProperty", "captureEvent", "event", "createTelemetryEvent", "type", "metadata", "captureDomContentLoaded", "ts", "captureLoad", "loadFull", "createItem", "scrubFields", "logLevel", "reportLevel", "verbose", "enabled", "transmit", "sendConfig", "includeItemsInTelemetry", "captureIp", "ignoreDuplicateErrors", "Detection", "ieVersion", "undef", "div", "createElement", "all", "getElementsByTagName", "innerHTML", "getElementType", "getAttribute", "toLowerCase", "descriptionToString", "desc", "tagName", "out", "id", "classes", "attributes", "key", "describeElement", "elem", "className", "attr", "elementArrayToString", "a", "nextStr", "totalLength", "separator", "MAX_LENGTH", "unshift", "treeToArray", "nextDescription", "height", "parentNode", "getElementFromEvent", "evt", "doc", "target", "elementFromPoint", "clientX", "clientY", "isDescribedElement", "element", "subtypes", "handler", "shim", "oldOnError", "_rollbarOldOnError", "onerror", "fn", "Array", "old", "ret", "_rollbarWindowOnError", "_rollbarURH", "belongs<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "detail", "addEventListener", "detection", "formatArgsAsString", "settings", "get", "headers", "url<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "network", "networkResponseHeaders", "networkResponseBody", "networkRequestHeaders", "networkRequestBody", "networkErrorOnHttp5xx", "networkErrorOnHttp4xx", "networkErrorOnHttp0", "dom", "navigation", "connectivity", "contentSecurityPolicy", "errorOnContentSecurityPolicy", "restore", "replacements", "b", "_window", "_document", "autoInstrument", "scrubTelemetryInputs", "telemetryScrubber", "defaultValueScrubber", "patterns", "RegExp", "description", "name", "attrs", "nameFromDescription", "diagnostic", "eventRemovers", "contentsecuritypolicy", "_location", "_lastHref", "href", "_isUrlObject", "input", "URL", "oldSettings", "deinstrumentNetwork", "instrumentNetwork", "deinstrumentConsole", "instrumentConsole", "deinstrumentDom", "instrumentDom", "deinstrumentNavigation", "instrumentNavigation", "deinstrumentConnectivity", "instrumentConnectivity", "deinstrumentContentSecurityPolicy", "instrumentContentSecurityPolicy", "wrapProp", "xhr", "orig", "xhrp", "isUrlObject", "__rollbar_xhr", "status_code", "start_time_ms", "now", "end_time_ms", "header", "request_headers", "request_content_type", "onreadystatechangeHandler", "request", "__rollbar_event", "captureNetwork", "readyState", "response_content_type", "getResponseHeader", "headersConfig", "allHeaders", "getAllResponseHeaders", "arr", "trim", "body", "responseText", "response", "isJsonContentType", "scrubJson", "code", "status", "levelFromStatus", "errorOnHttpStatus", "onreadystatechange", "trackHttpErrors", "t", "reqHeaders", "fetchHeaders", "then", "resp", "text", "clone", "subtype", "rollbarUUID", "contentType", "includes", "json", "JSON", "inHeaders", "outHeaders", "entries", "<PERSON><PERSON><PERSON><PERSON>", "next", "done", "skip<PERSON><PERSON><PERSON>", "c", "wrapConsole", "origConsole", "captureLog", "Function", "removeListeners", "clickHandler", "handleClick", "bind", "<PERSON><PERSON><PERSON><PERSON>", "handleBlur", "addListener", "hasTag", "anchorOrButton", "captureDomEvent", "checked", "handleSelectInputChanged", "multiple", "selected", "selectedIndex", "isChecked", "elementString", "captureDom", "app", "history", "pushState", "current", "handleUrlChange", "from", "to", "parsedHref", "parsedTo", "parsedFrom", "hash", "captureNavigation", "captureConnectivityChange", "handleCspEvent", "cspEvent", "blockedURI", "violatedDirective", "effectiveDirective", "sourceFile", "originalPolicy", "handleCspError", "c<PERSON><PERSON><PERSON><PERSON>", "section", "altType", "capture", "attachEvent", "detachEvent", "addBodyMessage", "custom", "extra", "set", "stackFromItem", "buildTrace", "guess", "guessErrorClass", "trace", "exception", "class", "errorClass", "stackFrame", "frame", "pre", "contextLength", "mid", "rawStack", "raw", "rawException", "frames", "filename", "sanitizeUrl", "func", "column", "sendFrameUrl", "endsWith", "Math", "floor", "reverse", "err", "<PERSON><PERSON>", "originalError", "nested", "_savedStackTrace", "addErrorContext", "chain", "cause", "e2", "environment", "platform", "framework", "language", "server", "requestInfo", "query_string", "remoteString", "user_ip", "keys", "nav", "navigator", "scr", "screen", "runtime_ms", "timestamp", "round", "javascript", "browser", "userAgent", "cookie_enabled", "cookieEnabled", "width", "cur", "plugins", "navPlugins", "l", "<PERSON><PERSON><PERSON><PERSON>", "traces", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trace_chain", "addBodyTraceChain", "addBodyTrace", "scrubFn", "scrubPaths", "makeFetchRequest", "makeXhrRequest", "params", "requestFactory", "addParamsAndAccessTokenToPath", "formatUrl", "_makeZoneRequest", "writeData", "currentZone", "Zone", "_name", "rootZone", "_parent", "run", "_makeRequest", "RollbarProxy", "_msg", "_proxyRequest", "controller", "timeoutId", "isFiniteNumber", "AbortController", "abort", "signal", "clearTimeout", "catch", "_newRetriableError", "xmlhttp", "factories", "ActiveXObject", "numFactories", "_createXMLHTTPObject", "parseResponse", "jsonParse", "_isNormalFailure", "ex", "open", "setRequestHeader", "send", "e1", "XDomainRequest", "xdomainrequest", "onprogress", "ontimeout", "onload", "last", "auth", "query", "parseInt", "pathParts", "_extendListenerPrototype", "oldAddEventListener", "_rollbarOldAdd", "addFn", "bubble", "oldRemoveEventListener", "_rollbarOldRemove", "removeFn", "maxItems", "itemsPerMin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ERR_CLASS_REGEXP", "gatherContext", "<PERSON>ame", "_stackFrame", "skip", "parserStack", "getStack", "_mostSpecificErrorName", "constructorName", "constructor", "guessFunctionName", "errMsg", "errClassMatch", "err<PERSON><PERSON>", "hasOwn", "toStr", "isPlainObject", "hasOwnConstructor", "hasIsPrototypeOf", "src", "copy", "Notifier", "transform", "addPendingItem", "_applyTransforms", "removePendingItem", "addItem", "transformIndex", "<PERSON><PERSON><PERSON><PERSON>", "cb", "matchFrames", "list", "block", "listLength", "frameLength", "urlIsOnAList", "safeOrBlock", "hostBlockList", "hostSafeList", "<PERSON><PERSON><PERSON><PERSON>", "listName", "levelVal", "LEVELS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onSendCallback", "ignoredMessages", "rIgnoredMessage", "messages", "messagesFromItem", "Queue", "rateLimiter", "pendingItems", "pendingRequests", "retryQueue", "retryHandle", "waitCallback", "waitIntervalID", "predicate", "idx", "splice", "originalItem", "predicateResult", "_applyPredicates", "stop", "_<PERSON><PERSON><PERSON>", "_makeApiRequest", "_dequeuePendingRequest", "wait", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearInterval", "setInterval", "rateLimitResponse", "shouldSend", "_maybeRetry", "RETRIABLE_ERRORS", "shouldRetry", "retryInterval", "maxRetries", "retries", "_retryApiRequest", "retryObject", "RateLimiter", "startTime", "counter", "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "platformOptions", "configureGlobal", "checkRate", "limit", "ignoreRateLimit", "shouldSendV<PERSON>ue", "globalRateLimit", "limitPerMin", "perMinute", "msg", "itemsPerMinute", "rateLimitPayload", "globalSettings", "elapsedTime", "globalRateLimitPerMin", "setPlatformOptions", "tracer", "validateTracer", "setStackTraceLimit", "lastErrorHash", "stackTraceLimit", "scope", "active", "_defaultLogLevel", "_log", "defaultLevel", "_sameAsLastError", "_addTracingInfo", "_captureRollbarItem", "telemetryEvents", "copyEvents", "itemHash", "generateItemHash", "span", "spanContext", "toSpanId", "toTraceId", "validateSpan", "setTag", "opentracingSpanId", "opentracingTraceId", "opentracing_span_id", "opentracing_trace_id", "traverse", "scrubPath", "redact", "paramRes", "pat", "_getScrubFieldRegexs", "queryRes", "_getScrubQueryParamRegexs", "redactQueryParam", "dummy0", "paramPart", "scrubber", "seen", "tmpV", "valScrubber", "param<PERSON><PERSON><PERSON><PERSON><PERSON>", "MAX_EVENTS", "maxTelemetryEvents", "maxQueueSize", "max", "min", "getLevel", "manual", "newMaxEvents", "deleteCount", "events", "filterTelemetry", "timestamp_ms", "captureError", "requestData", "statusCode", "change", "addFunctionOption", "payloadOptions", "tracePath", "newExtra", "newItem", "isPromise", "promisedItem", "configuredOptions", "configured_options", "is_anonymous", "is_uncaught", "raw_error", "constructor_name", "failed", "selectFrames", "range", "truncateFrames", "maybeTruncateValue", "val", "truncateStrings", "truncator", "typeName", "truncateTraceData", "traceData", "minBody", "needsTruncation", "maxSize", "maxByteSize", "strategy", "results", "strategies", "RollbarJSON", "x", "isNativeFunction", "funcMatchString", "reIsNative", "isObject", "uuid4", "d", "random", "parseUriOptions", "strictMode", "q", "parser", "strict", "loose", "backup", "jsonError", "backup<PERSON><PERSON>r", "wrapCallback", "nonCircularClone", "newSeen", "TELEMETRY_TYPES", "TELEMETRY_LEVELS", "arrayIncludes", "paramsArray", "sort", "qs", "h", "requestKeys", "lambdaContext", "arg", "extraArgs", "argTypes", "typ", "DOMException", "setCustomItemKeys", "original_arg_types", "errors", "contextAdded", "rollbarContext", "error_context", "filterIp", "newIp", "beginning", "slashIdx", "u", "hostWhiteList", "hostBlackList", "updateDeprecatedOptions", "overwriteScrubFields", "isIterable", "isString", "s", "mode", "backupMessage", "useragent", "baseUrlParts", "o", "m", "uri", "$0", "$1", "$2", "parseUri", "anchor", "temp", "replacement", "string", "count", "charCodeAt", "normalizeName", "FetchHeaders", "for<PERSON>ach", "append", "isArray", "getOwnPropertyNames", "normalizeValue", "oldValue", "has", "thisArg", "items", "iteratorFor", "Headers", "seenIndex", "isObj", "mapped", "same", "gap", "indent", "meta", "rep", "state", "container", "escapes", "number", "action", "rx_escapable", "this_value", "valueOf", "quote", "lastIndex", "holder", "partial", "mind", "toJSON", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "replacer", "space", "go", "firstokey", "okey", "ovalue", "firstavalue", "avalue", "ocomma", "acomma", "colon", "SyntaxError", "reviver", "tx", "ignore", "fromCharCode", "walk", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}