{"version": 3, "file": "rollbar.umd.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;AClBD;AACA;AACA;;AAEA;AACA,QAAQ,IAA0C;AAClD,QAAQ,iCAA6B,CAAC,wBAAY,CAAC,oCAAE,OAAO;AAAA;AAAA;AAAA,kGAAC;AAC7D,MAAM,KAAK,EAIN;AACL,CAAC;AACD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mBAAmB,OAAO;AAC1B,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA,cAAc;AACd;AACA,cAAc;AACd;AACA,cAAc;AACd;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;;AAET;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;;AAET;AACA;AACA;AACA;AACA,cAAc;AACd;AACA,cAAc;AACd;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA,gDAAgD,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;;AAEA,gDAAgD,SAAS;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,CAAC;;;;;;;;ACzMD;AACA;AACA;;AAEA;AACA,QAAQ,IAA0C;AAClD,QAAQ,iCAAqB,EAAE,oCAAE,OAAO;AAAA;AAAA;AAAA,kGAAC;AACzC,MAAM,KAAK,EAIN;AACL,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,cAAc;AACd;AACA,cAAc;AACd;AACA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA,oBAAoB,yBAAyB;AAC7C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA,oBAAoB,yBAAyB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA,oBAAoB,wBAAwB;AAC5C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA,CAAC;;;;;;;;;AC9IY;;AAEb,QAAQ,mBAAO,CAAC,GAAW;AAC3B,cAAc,mBAAO,CAAC,GAAc;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;ACvHa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC7Ga;;AAEb,cAAc,mBAAO,CAAC,GAAY;;AAElC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;;;;;;;;;ACvBa;;AAEb,aAAa,mBAAO,CAAC,GAAY;AACjC,QAAQ,mBAAO,CAAC,GAAY;AAC5B,UAAU,mBAAO,CAAC,GAAQ;AAC1B,aAAa,mBAAO,CAAC,GAAU;AAC/B,cAAc,mBAAO,CAAC,GAAe;;AAErC,gBAAgB,mBAAO,CAAC,GAAa;AACrC,aAAa,mBAAO,CAAC,GAAO;;AAE5B,iBAAiB,mBAAO,CAAC,GAAc;AACvC,uBAAuB,mBAAO,CAAC,GAAe;AAC9C,iBAAiB,mBAAO,CAAC,GAAc;AACvC,uBAAuB,mBAAO,CAAC,EAAe;AAC9C,kBAAkB,mBAAO,CAAC,GAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB;AACh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qCAAqC,SAAS;AAC9C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,mBAAO,CAAC,GAAa;AACpC,kBAAkB,mBAAO,CAAC,GAAwB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AC9lBa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC5Da;;AAEb;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;AC9Ba;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,uBAAuB,6BAA6B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,6BAA6B,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,4BAA4B;AAC9C;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA,4BAA4B,uBAAuB;AACnD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC7Ia;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;ACxGa;;AAEb;AACA,mBAAO,CAAC,GAAkB;AAC1B,gBAAgB,mBAAO,CAAC,GAAa;AACrC,QAAQ,mBAAO,CAAC,GAAY;;AAE5B;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AC3Ca;;AAEb,QAAQ,mBAAO,CAAC,GAAY;;AAE5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;ACba;;AAEb,cAAc,mBAAO,CAAC,GAAQ;AAC9B,gBAAgB,mBAAO,CAAC,GAAc;AACtC,mBAAmB,mBAAO,CAAC,GAAa;AACxC,mBAAmB,mBAAO,CAAC,GAAyB;AACpD,kBAAkB,mBAAO,CAAC,GAAe;AACzC,YAAY,mBAAO,CAAC,GAAU;AAC9B,iBAAiB,mBAAO,CAAC,GAAe;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;;;;;;;;;ACnBa;;AAEb,QAAQ,mBAAO,CAAC,GAAY;AAC5B,cAAc,mBAAO,CAAC,GAAoB;AAC1C,cAAc,mBAAO,CAAC,GAAoB;AAC1C,YAAY,mBAAO,CAAC,GAAU;AAC9B,gBAAgB,mBAAO,CAAC,GAAO;AAC/B,cAAc,mBAAO,CAAC,GAAc;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,wBAAwB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,gBAAgB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,kCAAkC,0BAA0B;AAC5D;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,mBAAmB;AACnB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,kBAAkB,0BAA0B;AAC5C;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,eAAe;AAC/C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,SAAS;AACnD;AACA;AACA,IAAI;AACJ,0CAA0C;AAC1C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA,oBAAoB,yBAAyB;AAC7C;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;ACj+Ba;;AAEb,QAAQ,mBAAO,CAAC,GAAY;AAC5B,kBAAkB,mBAAO,CAAC,GAAgB;AAC1C,aAAa,mBAAO,CAAC,GAAU;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,OAAO;AAClD;AACA,qBAAqB,8CAA8C;AACnE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,6BAA6B,iBAAiB;AAC9C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAkB,sBAAsB;AACxC;AACA;AACA;;AAEA,6BAA6B,qBAAqB;AAClD;AACA;;AAEA;AACA;;AAEA;AACA;AACA,+BAA+B,cAAc;AAC7C;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,kBAAkB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACtWa;;AAEb,QAAQ,mBAAO,CAAC,GAAY;AAC5B,uBAAuB,mBAAO,CAAC,GAAmB;AAClD,qBAAqB,mBAAO,CAAC,EAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;AAEA;;;;;;;;;ACxLa;;AAEb,aAAa,mBAAO,CAAC,GAAW;AAChC,QAAQ,mBAAO,CAAC,GAAe;;AAE/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;;AAEA;;;;;;;;;ACtCa;;AAEb;;AAEA,QAAQ,mBAAO,CAAC,GAAe;AAC/B,aAAa,mBAAO,CAAC,GAAW;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AChLa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;ACxFa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,oBAAoB;AAClC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AC7Da;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACVa;;AAEb,uBAAuB,mBAAO,CAAC,GAAoB;;AAEnD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,MAAM;AACN;AACA;;AAEA;;AAEA,uBAAuB,wBAAwB;AAC/C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AChIa;;AAEb;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;;AAEA,cAAc,YAAY;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AChEa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;;;;;;;;AC3Ha;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,iBAAiB;AACnC;AACA;;AAEA;AACA;AACA;;AAEA,oBAAoB,gBAAgB;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAkB,SAAS;AAC3B;;AAEA,oBAAoB,qBAAqB;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACtNa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wDAAwD,WAAW;AACnE;AACA;AACA,eAAe,YAAY;AAC3B,eAAe,gBAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa,+BAA+B;AAC5C;AACA;AACA;AACA;AACA,gDAAgD,SAAS;AACzD;AACA;AACA,eAAe;AACf;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,SAAS;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,gCAAgC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AC7Sa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;ACzLa;;AAEb,kBAAkB,mBAAO,CAAC,GAAe;AACzC,YAAY,mBAAO,CAAC,GAAS;AAC7B,eAAe,mBAAO,CAAC,GAAY;AACnC,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,UAAU;AACzD;AACA;AACA;AACA,qDAAqD,UAAU;AAC/D;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,6BAA6B;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,mBAAmB;AAC9B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,WAAW,iBAAiB;AAC5B;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;;;;;;;;AC5Ra;;AAEb,QAAQ,mBAAO,CAAC,GAAW;AAC3B,eAAe,mBAAO,CAAC,GAAoB;;AAE3C;AACA;;AAEA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,qBAAqB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,qBAAqB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,wBAAwB;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,kBAAkB,wBAAwB;AAC1C;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AC7Fa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,oBAAoB;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM,6BAA6B;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,4BAA4B;AAC1D;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,gBAAgB;AAC9C;AACA;;AAEA;AACA,+BAA+B,cAAc;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;AClPa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;;AAE3B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,uBAAuB;AAC3D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,+BAA+B;AAC/B;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACxKa;;AAEb,QAAQ,mBAAO,CAAC,GAAW;AAC3B,eAAe,mBAAO,CAAC,GAAoB;;AAE3C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC1Ha;;AAEb,YAAY,mBAAO,CAAC,GAAS;;AAE7B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,oCAAoC,OAAO;AAC3C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,mCAAmC,OAAO;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,SAAS;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,6CAA6C;AAC7C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,oBAAoB,mBAAmB;AACvC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,mCAAmC,OAAO;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,SAAS,UAAU,YAAY,GAAG;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,SAAS;AAChD;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAa;AACjC;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACjzBa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,eAAe;AACf,KAAK;AACL;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;;;;;;;;AC/Fa;;AAEb,mBAAmB,mBAAO,CAAC,GAA4B;;AAEvD;;;;;;;;;ACJa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;ACVa;;AAEb,QAAQ,mBAAO,CAAC,GAAY;;AAE5B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;;AAEnB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,gBAAgB,gBAAgB;AAChC;AACA;AACA;;AAEA,0BAA0B;AAC1B;AACA,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;;;;;;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,yCAAyC,iBAAiB;AAC1D,8BAA8B,kBAAkB;;AAEhD,yCAAyC,iBAAiB;AAC1D,sCAAsC,6BAA6B;;AAEnE;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,wBAAwB;AACxB,+CAA+C,EAAE;AACjD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;;;AAGA;;AAEA;;AAEA,oBAAoB;AACpB,oBAAoB;AACpB,oBAAoB;AACpB;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA,sBAAsB,YAAY;AAClC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,sBAAsB,YAAY;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;;AAEV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,eAAe;AACf;AACA,cAAc,wDAAwD;AACtE,cAAc,0BAA0B;AACxC;AACA;AACA;AACA;;AAEA;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,oBAAoB,WAAW;AAC/B;AACA;;AAEA;;AAEA,QAAQ;AACR;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,sBAAsB,UAAU;AAChC;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;;AAEA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,sBAAsB;AACtB,sBAAsB;AACtB,sBAAsB;AACtB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,UAAU;AACV;AACA,wBAAwB,YAAY;AACpC;AACA;AACA,WAAW;AACX;AACA,wBAAwB,gDAAgD;AACxE;AACA;AACA,WAAW;AACX;AACA,wBAAwB,sCAAsC;AAC9D;AACA;AACA,WAAW;AACX;AACA,wBAAwB,sCAAsC;AAC9D;AACA;AACA;AACA,SAAS;AACT,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,wBAAwB,YAAY;AACpC;AACA;AACA,WAAW;AACX;AACA,wBAAwB,gDAAgD;AACxE;AACA;AACA,WAAW;AACX;AACA,wBAAwB,sCAAsC;AAC9D;AACA;AACA,WAAW;AACX;AACA,wBAAwB,sCAAsC;AAC9D;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA,sCAAsC,EAAE;AACxC;AACA;AACA;AACA,SAAS;AACT;;AAEA;;AAEA;AACA;;AAEA;AACA,+CAA+C,yGAAyG,EAAE;;AAE1J;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA,cAAc;;AAEd;AACA;;AAEA;AACA;AACA,cAAc;;AAEd;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,UAAU;AACV;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,EAAE,UAAU;AACvB;AACA;AACA,KAAK;AACL;AACA;;AAEA;;;;;;;UC1vBA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://rollbar/webpack/universalModuleDefinition", "webpack://rollbar/./node_modules/console-polyfill/index.js", "webpack://rollbar/./node_modules/error-stack-parser/error-stack-parser.js", "webpack://rollbar/./node_modules/error-stack-parser/node_modules/stackframe/stackframe.js", "webpack://rollbar/./src/api.js", "webpack://rollbar/./src/apiUtility.js", "webpack://rollbar/./src/browser/bundles/rollbar.js", "webpack://rollbar/./src/browser/core.js", "webpack://rollbar/./src/browser/defaults/scrubFields.js", "webpack://rollbar/./src/browser/detection.js", "webpack://rollbar/./src/browser/domUtility.js", "webpack://rollbar/./src/browser/globalSetup.js", "webpack://rollbar/./src/browser/logger.js", "webpack://rollbar/./src/browser/predicates.js", "webpack://rollbar/./src/browser/rollbar.js", "webpack://rollbar/./src/browser/telemetry.js", "webpack://rollbar/./src/browser/transforms.js", "webpack://rollbar/./src/browser/transport.js", "webpack://rollbar/./src/browser/transport/fetch.js", "webpack://rollbar/./src/browser/transport/xhr.js", "webpack://rollbar/./src/browser/url.js", "webpack://rollbar/./src/browser/wrapGlobals.js", "webpack://rollbar/./src/defaults.js", "webpack://rollbar/./src/errorParser.js", "webpack://rollbar/./src/merge.js", "webpack://rollbar/./src/notifier.js", "webpack://rollbar/./src/predicates.js", "webpack://rollbar/./src/queue.js", "webpack://rollbar/./src/rateLimiter.js", "webpack://rollbar/./src/rollbar.js", "webpack://rollbar/./src/scrub.js", "webpack://rollbar/./src/telemetry.js", "webpack://rollbar/./src/transforms.js", "webpack://rollbar/./src/truncation.js", "webpack://rollbar/./src/utility.js", "webpack://rollbar/./src/utility/headers.js", "webpack://rollbar/./src/utility/polyfillJSON.js", "webpack://rollbar/./src/utility/replace.js", "webpack://rollbar/./src/utility/traverse.js", "webpack://rollbar/./vendor/JSON-js/json3.js", "webpack://rollbar/webpack/bootstrap", "webpack://rollbar/webpack/before-startup", "webpack://rollbar/webpack/startup", "webpack://rollbar/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"rollbar\"] = factory();\n\telse\n\t\troot[\"rollbar\"] = factory();\n})(this, () => {\nreturn ", "// Console-polyfill. MIT license.\n// https://github.com/paulmillr/console-polyfill\n// Make it safe to do console.log() always.\n(function(global) {\n  'use strict';\n  if (!global.console) {\n    global.console = {};\n  }\n  var con = global.console;\n  var prop, method;\n  var dummy = function() {};\n  var properties = ['memory'];\n  var methods = ('assert,clear,count,debug,dir,dirxml,error,exception,group,' +\n     'groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,' +\n     'show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn').split(',');\n  while (prop = properties.pop()) if (!con[prop]) con[prop] = {};\n  while (method = methods.pop()) if (!con[method]) con[method] = dummy;\n  // Using `this` for web workers & supports Browserify / Webpack.\n})(typeof window === 'undefined' ? this : window);\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n    var objectProps = ['evalOrigin'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps, objectProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "'use strict';\n\nvar _ = require('./utility');\nvar helpers = require('./apiUtility');\n\nvar defaultOptions = {\n  hostname: 'api.rollbar.com',\n  path: '/api/1/item/',\n  search: null,\n  version: '1',\n  protocol: 'https:',\n  port: 443,\n};\n\n/**\n * Api is an object that encapsulates methods of communicating with\n * the Rollbar API.  It is a standard interface with some parts implemented\n * differently for server or browser contexts.  It is an object that should\n * be instantiated when used so it can contain non-global options that may\n * be different for another instance of RollbarApi.\n *\n * @param options {\n *    accessToken: the accessToken to use for posting items to rollbar\n *    endpoint: an alternative endpoint to send errors to\n *        must be a valid, fully qualified URL.\n *        The default is: https://api.rollbar.com/api/1/item\n *    proxy: if you wish to proxy requests provide an object\n *        with the following keys:\n *          host or hostname (required): foo.example.com\n *          port (optional): 123\n *          protocol (optional): https\n * }\n */\nfunction Api(options, transport, urllib, truncation, jsonBackup) {\n  this.options = options;\n  this.transport = transport;\n  this.url = urllib;\n  this.truncation = truncation;\n  this.jsonBackup = jsonBackup;\n  this.accessToken = options.accessToken;\n  this.transportOptions = _getTransport(options, urllib);\n}\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.postItem = function (data, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n  var self = this;\n\n  // ensure the network request is scheduled after the current tick.\n  setTimeout(function () {\n    self.transport.post(self.accessToken, transportOptions, payload, callback);\n  }, 0);\n};\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.buildJsonPayload = function (data, callback) {\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n\n  if (stringifyResult.error) {\n    if (callback) {\n      callback(stringifyResult.error);\n    }\n    return null;\n  }\n\n  return stringifyResult.value;\n};\n\n/**\n *\n * @param jsonPayload\n * @param callback\n */\nApi.prototype.postJsonPayload = function (jsonPayload, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  this.transport.postJsonPayload(\n    this.accessToken,\n    transportOptions,\n    jsonPayload,\n    callback,\n  );\n};\n\nApi.prototype.configure = function (options) {\n  var oldOptions = this.oldOptions;\n  this.options = _.merge(oldOptions, options);\n  this.transportOptions = _getTransport(this.options, this.url);\n  if (this.options.accessToken !== undefined) {\n    this.accessToken = this.options.accessToken;\n  }\n  return this;\n};\n\nfunction _getTransport(options, url) {\n  return helpers.getTransportFromOptions(options, defaultOptions, url);\n}\n\nmodule.exports = Api;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction buildPayload(accessToken, data, jsonBackup) {\n  if (!_.isType(data.context, 'string')) {\n    var contextResult = _.stringify(data.context, jsonBackup);\n    if (contextResult.error) {\n      data.context = \"Error: could not serialize 'context'\";\n    } else {\n      data.context = contextResult.value || '';\n    }\n    if (data.context.length > 255) {\n      data.context = data.context.substr(0, 255);\n    }\n  }\n  return {\n    access_token: accessToken,\n    data: data,\n  };\n}\n\nfunction getTransportFromOptions(options, defaults, url) {\n  var hostname = defaults.hostname;\n  var protocol = defaults.protocol;\n  var port = defaults.port;\n  var path = defaults.path;\n  var search = defaults.search;\n  var timeout = options.timeout;\n  var transport = detectTransport(options);\n\n  var proxy = options.proxy;\n  if (options.endpoint) {\n    var opts = url.parse(options.endpoint);\n    hostname = opts.hostname;\n    protocol = opts.protocol;\n    port = opts.port;\n    path = opts.pathname;\n    search = opts.search;\n  }\n  return {\n    timeout: timeout,\n    hostname: hostname,\n    protocol: protocol,\n    port: port,\n    path: path,\n    search: search,\n    proxy: proxy,\n    transport: transport,\n  };\n}\n\nfunction detectTransport(options) {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var transport = options.defaultTransport || 'xhr';\n  if (typeof gWindow.fetch === 'undefined') transport = 'xhr';\n  if (typeof gWindow.XMLHttpRequest === 'undefined') transport = 'fetch';\n  return transport;\n}\n\nfunction transportOptions(transport, method) {\n  var protocol = transport.protocol || 'https:';\n  var port =\n    transport.port ||\n    (protocol === 'http:' ? 80 : protocol === 'https:' ? 443 : undefined);\n  var hostname = transport.hostname;\n  var path = transport.path;\n  var timeout = transport.timeout;\n  var transportAPI = transport.transport;\n  if (transport.search) {\n    path = path + transport.search;\n  }\n  if (transport.proxy) {\n    path = protocol + '//' + hostname + path;\n    hostname = transport.proxy.host || transport.proxy.hostname;\n    port = transport.proxy.port;\n    protocol = transport.proxy.protocol || protocol;\n  }\n  return {\n    timeout: timeout,\n    protocol: protocol,\n    hostname: hostname,\n    path: path,\n    port: port,\n    method: method,\n    transport: transportAPI,\n  };\n}\n\nfunction appendPathToPath(base, path) {\n  var baseTrailingSlash = /\\/$/.test(base);\n  var pathBeginningSlash = /^\\//.test(path);\n\n  if (baseTrailingSlash && pathBeginningSlash) {\n    path = path.substring(1);\n  } else if (!baseTrailingSlash && !pathBeginningSlash) {\n    path = '/' + path;\n  }\n\n  return base + path;\n}\n\nmodule.exports = {\n  buildPayload: buildPayload,\n  getTransportFromOptions: getTransportFromOptions,\n  transportOptions: transportOptions,\n  appendPathToPath: appendPathToPath,\n};\n", "'use strict';\n\nvar rollbar = require('../rollbar');\n\nvar options = (typeof window !== 'undefined') && window._rollbarConfig;\nvar alias = options && options.globalAlias || 'Rollbar';\nvar shimRunning = (typeof window !== 'undefined') && window[alias] && typeof window[alias].shimId === 'function' && window[alias].shimId() !== undefined;\n\nif ((typeof window !== 'undefined') && !window._rollbarStartTime) {\n  window._rollbarStartTime = (new Date()).getTime();\n}\n\nif (!shimRunning && options) {\n  var Rollbar = new rollbar(options);\n  window[alias] = Rollbar;\n} else if (typeof window !== 'undefined') {\n  window.rollbar = rollbar;\n  window._rollbarDidLoad = true;\n} else if (typeof self !== 'undefined') {\n  self.rollbar = rollbar;\n  self._rollbarDidLoad = true;\n}\n\nmodule.exports = rollbar;\n", "'use strict';\n\nvar Client = require('../rollbar');\nvar _ = require('../utility');\nvar API = require('../api');\nvar logger = require('./logger');\nvar globals = require('./globalSetup');\n\nvar Transport = require('./transport');\nvar urllib = require('./url');\n\nvar transforms = require('./transforms');\nvar sharedTransforms = require('../transforms');\nvar predicates = require('./predicates');\nvar sharedPredicates = require('../predicates');\nvar errorParser = require('../errorParser');\n\nfunction Rollbar(options, client) {\n  this.options = _.handleOptions(defaultOptions, options, null, logger);\n  this.options._configuredOptions = options;\n  var Telemeter = this.components.telemeter;\n  var Instrumenter = this.components.instrumenter;\n  var polyfillJSON = this.components.polyfillJSON;\n  this.wrapGlobals = this.components.wrapGlobals;\n  this.scrub = this.components.scrub;\n  var truncation = this.components.truncation;\n\n  var transport = new Transport(truncation);\n  var api = new API(this.options, transport, urllib, truncation);\n  if (Telemeter) {\n    this.telemeter = new Telemeter(this.options);\n  }\n  this.client =\n    client || new Client(this.options, api, logger, this.telemeter, 'browser');\n  var gWindow = _gWindow();\n  var gDocument = typeof document != 'undefined' && document;\n  this.isChrome = gWindow.chrome && gWindow.chrome.runtime; // check .runtime to avoid Edge browsers\n  this.anonymousErrorsPending = 0;\n  addTransformsToNotifier(this.client.notifier, this, gWindow);\n  addPredicatesToQueue(this.client.queue);\n  this.setupUnhandledCapture();\n  if (Instrumenter) {\n    this.instrumenter = new Instrumenter(\n      this.options,\n      this.client.telemeter,\n      this,\n      gWindow,\n      gDocument,\n    );\n    this.instrumenter.instrument();\n  }\n  _.setupJSON(polyfillJSON);\n\n  // Used with rollbar-react for rollbar-react-native compatibility.\n  this.rollbar = this;\n}\n\nvar _instance = null;\nRollbar.init = function (options, client) {\n  if (_instance) {\n    return _instance.global(options).configure(options);\n  }\n  _instance = new Rollbar(options, client);\n  return _instance;\n};\n\nRollbar.prototype.components = {};\n\nRollbar.setComponents = function (components) {\n  Rollbar.prototype.components = components;\n};\n\nfunction handleUninitialized(maybeCallback) {\n  var message = 'Rollbar is not initialized';\n  logger.error(message);\n  if (maybeCallback) {\n    maybeCallback(new Error(message));\n  }\n}\n\nRollbar.prototype.global = function (options) {\n  this.client.global(options);\n  return this;\n};\nRollbar.global = function (options) {\n  if (_instance) {\n    return _instance.global(options);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n  this.options = _.handleOptions(oldOptions, options, payload, logger);\n  this.options._configuredOptions = _.handleOptions(\n    oldOptions._configuredOptions,\n    options,\n    payload,\n  );\n  this.client.configure(this.options, payloadData);\n  this.instrumenter && this.instrumenter.configure(this.options);\n  this.setupUnhandledCapture();\n  return this;\n};\nRollbar.configure = function (options, payloadData) {\n  if (_instance) {\n    return _instance.configure(options, payloadData);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.lastError = function () {\n  return this.client.lastError;\n};\nRollbar.lastError = function () {\n  if (_instance) {\n    return _instance.lastError();\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.log = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.log(item);\n  return { uuid: uuid };\n};\nRollbar.log = function () {\n  if (_instance) {\n    return _instance.log.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.debug = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.debug(item);\n  return { uuid: uuid };\n};\nRollbar.debug = function () {\n  if (_instance) {\n    return _instance.debug.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.info = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.info(item);\n  return { uuid: uuid };\n};\nRollbar.info = function () {\n  if (_instance) {\n    return _instance.info.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warn = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warn(item);\n  return { uuid: uuid };\n};\nRollbar.warn = function () {\n  if (_instance) {\n    return _instance.warn.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warning = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warning(item);\n  return { uuid: uuid };\n};\nRollbar.warning = function () {\n  if (_instance) {\n    return _instance.warning.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.error = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.error(item);\n  return { uuid: uuid };\n};\nRollbar.error = function () {\n  if (_instance) {\n    return _instance.error.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.critical = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.critical(item);\n  return { uuid: uuid };\n};\nRollbar.critical = function () {\n  if (_instance) {\n    return _instance.critical.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.client.buildJsonPayload(item);\n};\nRollbar.buildJsonPayload = function () {\n  if (_instance) {\n    return _instance.buildJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  return this.client.sendJsonPayload(jsonPayload);\n};\nRollbar.sendJsonPayload = function () {\n  if (_instance) {\n    return _instance.sendJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.setupUnhandledCapture = function () {\n  var gWindow = _gWindow();\n\n  if (!this.unhandledExceptionsInitialized) {\n    if (this.options.captureUncaught || this.options.handleUncaughtExceptions) {\n      globals.captureUncaughtExceptions(gWindow, this);\n      if (this.wrapGlobals && this.options.wrapGlobalEventHandlers) {\n        this.wrapGlobals(gWindow, this);\n      }\n      this.unhandledExceptionsInitialized = true;\n    }\n  }\n  if (!this.unhandledRejectionsInitialized) {\n    if (\n      this.options.captureUnhandledRejections ||\n      this.options.handleUnhandledRejections\n    ) {\n      globals.captureUnhandledRejections(gWindow, this);\n      this.unhandledRejectionsInitialized = true;\n    }\n  }\n};\n\nRollbar.prototype.handleUncaughtException = function (\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  context,\n) {\n  if (!this.options.captureUncaught && !this.options.handleUncaughtExceptions) {\n    return;\n  }\n\n  // Chrome will always send 5+ arguments and error will be valid or null, not undefined.\n  // If error is undefined, we have a different caller.\n  // Chrome also sends errors from web workers with null error, but does not invoke\n  // prepareStackTrace() for these. Test for empty url to skip them.\n  if (\n    this.options.inspectAnonymousErrors &&\n    this.isChrome &&\n    error === null &&\n    url === ''\n  ) {\n    return 'anonymous';\n  }\n\n  var item;\n  var stackInfo = _.makeUnhandledStackInfo(\n    message,\n    url,\n    lineno,\n    colno,\n    error,\n    'onerror',\n    'uncaught exception',\n    errorParser,\n  );\n  if (_.isError(error)) {\n    item = this._createItem([message, error, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else if (_.isError(url)) {\n    item = this._createItem([message, url, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else {\n    item = this._createItem([message, context]);\n    item.stackInfo = stackInfo;\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  this.client.log(item);\n};\n\n/**\n * Chrome only. Other browsers will ignore.\n *\n * Use Error.prepareStackTrace to extract information about errors that\n * do not have a valid error object in onerror().\n *\n * In tested version of Chrome, onerror is called first but has no way\n * to communicate with prepareStackTrace. Use a counter to let this\n * handler know which errors to send to Rollbar.\n *\n * In config options, set inspectAnonymousErrors to enable.\n */\nRollbar.prototype.handleAnonymousErrors = function () {\n  if (!this.options.inspectAnonymousErrors || !this.isChrome) {\n    return;\n  }\n\n  var r = this;\n  function prepareStackTrace(error, _stack) {\n    // eslint-disable-line no-unused-vars\n    if (r.options.inspectAnonymousErrors) {\n      if (r.anonymousErrorsPending) {\n        // This is the only known way to detect that onerror saw an anonymous error.\n        // It depends on onerror reliably being called before Error.prepareStackTrace,\n        // which so far holds true on tested versions of Chrome. If versions of Chrome\n        // are tested that behave differently, this logic will need to be updated\n        // accordingly.\n        r.anonymousErrorsPending -= 1;\n\n        if (!error) {\n          // Not likely to get here, but calling handleUncaughtException from here\n          // without an error object would throw off the anonymousErrorsPending counter,\n          // so return now.\n          return;\n        }\n\n        // Allow this to be tracked later.\n        error._isAnonymous = true;\n\n        // url, lineno, colno shouldn't be needed for these errors.\n        // If that changes, update this accordingly, using the unused\n        // _stack param as needed (rather than parse error.toString()).\n        r.handleUncaughtException(error.message, null, null, null, error);\n      }\n    }\n\n    // Workaround to ensure stack is preserved for normal errors.\n    return error.stack;\n  }\n\n  // https://v8.dev/docs/stack-trace-api\n  try {\n    Error.prepareStackTrace = prepareStackTrace;\n  } catch (e) {\n    this.options.inspectAnonymousErrors = false;\n    this.error('anonymous error handler failed', e);\n  }\n};\n\nRollbar.prototype.handleUnhandledRejection = function (reason, promise) {\n  if (\n    !this.options.captureUnhandledRejections &&\n    !this.options.handleUnhandledRejections\n  ) {\n    return;\n  }\n\n  var message = 'unhandled rejection was null or undefined!';\n  if (reason) {\n    if (reason.message) {\n      message = reason.message;\n    } else {\n      var reasonResult = _.stringify(reason);\n      if (reasonResult.value) {\n        message = reasonResult.value;\n      }\n    }\n  }\n  var context =\n    (reason && reason._rollbarContext) || (promise && promise._rollbarContext);\n\n  var item;\n  if (_.isError(reason)) {\n    item = this._createItem([message, reason, context]);\n  } else {\n    item = this._createItem([message, reason, context]);\n    item.stackInfo = _.makeUnhandledStackInfo(\n      message,\n      '',\n      0,\n      0,\n      null,\n      'unhandledrejection',\n      '',\n      errorParser,\n    );\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  item._originalArgs = item._originalArgs || [];\n  item._originalArgs.push(promise);\n  this.client.log(item);\n};\n\nRollbar.prototype.wrap = function (f, context, _before) {\n  try {\n    var ctxFn;\n    if (_.isFunction(context)) {\n      ctxFn = context;\n    } else {\n      ctxFn = function () {\n        return context || {};\n      };\n    }\n\n    if (!_.isFunction(f)) {\n      return f;\n    }\n\n    if (f._isWrap) {\n      return f;\n    }\n\n    if (!f._rollbar_wrapped) {\n      f._rollbar_wrapped = function () {\n        if (_before && _.isFunction(_before)) {\n          _before.apply(this, arguments);\n        }\n        try {\n          return f.apply(this, arguments);\n        } catch (exc) {\n          var e = exc;\n          if (e && window._rollbarWrappedError !== e) {\n            if (_.isType(e, 'string')) {\n              e = new String(e);\n            }\n            e._rollbarContext = ctxFn() || {};\n            e._rollbarContext._wrappedSource = f.toString();\n\n            window._rollbarWrappedError = e;\n          }\n          throw e;\n        }\n      };\n\n      f._rollbar_wrapped._isWrap = true;\n\n      if (f.hasOwnProperty) {\n        for (var prop in f) {\n          if (f.hasOwnProperty(prop) && prop !== '_rollbar_wrapped') {\n            f._rollbar_wrapped[prop] = f[prop];\n          }\n        }\n      }\n    }\n\n    return f._rollbar_wrapped;\n  } catch (e) {\n    // Return the original function if the wrap fails.\n    return f;\n  }\n};\nRollbar.wrap = function (f, context) {\n  if (_instance) {\n    return _instance.wrap(f, context);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.captureEvent = function () {\n  var event = _.createTelemetryEvent(arguments);\n  return this.client.captureEvent(event.type, event.metadata, event.level);\n};\nRollbar.captureEvent = function () {\n  if (_instance) {\n    return _instance.captureEvent.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\n// The following two methods are used internally and are not meant for public use\nRollbar.prototype.captureDomContentLoaded = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureLoad(ts);\n};\n\n/* Internal */\n\nfunction addTransformsToNotifier(notifier, rollbar, gWindow) {\n  notifier\n    .addTransform(transforms.handleDomException)\n    .addTransform(transforms.handleItemWithError)\n    .addTransform(transforms.ensureItemHasSomethingToSay)\n    .addTransform(transforms.addBaseInfo)\n    .addTransform(transforms.addRequestInfo(gWindow))\n    .addTransform(transforms.addClientInfo(gWindow))\n    .addTransform(transforms.addPluginInfo(gWindow))\n    .addTransform(transforms.addBody)\n    .addTransform(sharedTransforms.addMessageWithError)\n    .addTransform(sharedTransforms.addTelemetryData)\n    .addTransform(sharedTransforms.addConfigToPayload)\n    .addTransform(transforms.addScrubber(rollbar.scrub))\n    .addTransform(sharedTransforms.addPayloadOptions)\n    .addTransform(sharedTransforms.userTransform(logger))\n    .addTransform(sharedTransforms.addConfiguredOptions)\n    .addTransform(sharedTransforms.addDiagnosticKeys)\n    .addTransform(sharedTransforms.itemToPayload);\n}\n\nfunction addPredicatesToQueue(queue) {\n  queue\n    .addPredicate(sharedPredicates.checkLevel)\n    .addPredicate(predicates.checkIgnore)\n    .addPredicate(sharedPredicates.userCheckIgnore(logger))\n    .addPredicate(sharedPredicates.urlIsNotBlockListed(logger))\n    .addPredicate(sharedPredicates.urlIsSafeListed(logger))\n    .addPredicate(sharedPredicates.messageIsIgnored(logger));\n}\n\nRollbar.prototype.loadFull = function () {\n  logger.info(\n    'Unexpected Rollbar.loadFull() called on a Notifier instance. This can happen when Rollbar is loaded multiple times.',\n  );\n};\n\nRollbar.prototype._createItem = function (args) {\n  return _.createItem(args, logger, this);\n};\n\nfunction _getFirstFunction(args) {\n  for (var i = 0, len = args.length; i < len; ++i) {\n    if (_.isFunction(args[i])) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\nfunction _gWindow() {\n  return (\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self)\n  );\n}\n\nvar defaults = require('../defaults');\nvar scrubFields = require('./defaults/scrubFields');\n\nvar defaultOptions = {\n  version: defaults.version,\n  scrubFields: scrubFields.scrubFields,\n  logLevel: defaults.logLevel,\n  reportLevel: defaults.reportLevel,\n  uncaughtErrorLevel: defaults.uncaughtErrorLevel,\n  endpoint: defaults.endpoint,\n  verbose: false,\n  enabled: true,\n  transmit: true,\n  sendConfig: false,\n  includeItemsInTelemetry: true,\n  captureIp: true,\n  inspectAnonymousErrors: true,\n  ignoreDuplicateErrors: true,\n  wrapGlobalEventHandlers: false,\n};\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nmodule.exports = {\n  scrubFields: [\n    'pw',\n    'pass',\n    'passwd',\n    'password',\n    'secret',\n    'confirm_password',\n    'confirmPassword',\n    'password_confirmation',\n    'passwordConfirmation',\n    'access_token',\n    'accessToken',\n    'X-Rollbar-Access-Token',\n    'secret_key',\n    'secretKey',\n    'secretToken',\n    'cc-number',\n    'card number',\n    'cardnumber',\n    'cardnum',\n    'ccnum',\n    'ccnumber',\n    'cc num',\n    'creditcardnumber',\n    'credit card number',\n    'newcreditcardnumber',\n    'new credit card',\n    'creditcardno',\n    'credit card no',\n    'card#',\n    'card #',\n    'cc-csc',\n    'cvc',\n    'cvc2',\n    'cvv2',\n    'ccv2',\n    'security code',\n    'card verification',\n    'name on credit card',\n    'name on card',\n    'nameoncard',\n    'cardholder',\n    'card holder',\n    'name des karteninhabers',\n    'ccname',\n    'card type',\n    'cardtype',\n    'cc type',\n    'cctype',\n    'payment type',\n    'expiration date',\n    'expirationdate',\n    'expdate',\n    'cc-exp',\n    'ccmonth',\n    'ccyear',\n  ],\n};\n", "'use strict';\n\n// This detection.js module is used to encapsulate any ugly browser/feature\n// detection we may need to do.\n\n// Figure out which version of IE we're using, if any.\n// This is gleaned from http://stackoverflow.com/questions/5574842/best-way-to-check-for-ie-less-than-9-in-javascript-without-library\n// Will return an integer on IE (i.e. 8)\n// Will return undefined otherwise\nfunction getIEVersion() {\n  var undef;\n  if (typeof document === 'undefined') {\n    return undef;\n  }\n\n  var v = 3,\n    div = document.createElement('div'),\n    all = div.getElementsByTagName('i');\n\n  while (\n    ((div.innerHTML = '<!--[if gt IE ' + ++v + ']><i></i><![endif]-->'), all[0])\n  );\n\n  return v > 4 ? v : undef;\n}\n\nvar Detection = {\n  ieVersion: getIEVersion,\n};\n\nmodule.exports = Detection;\n", "'use strict';\n\nfunction getElementType(e) {\n  return (e.getAttribute('type') || '').toLowerCase();\n}\n\nfunction isDescribedElement(element, type, subtypes) {\n  if (element.tagName.toLowerCase() !== type.toLowerCase()) {\n    return false;\n  }\n  if (!subtypes) {\n    return true;\n  }\n  element = getElementType(element);\n  for (var i = 0; i < subtypes.length; i++) {\n    if (subtypes[i] === element) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getElementFromEvent(evt, doc) {\n  if (evt.target) {\n    return evt.target;\n  }\n  if (doc && doc.elementFromPoint) {\n    return doc.elementFromPoint(evt.clientX, evt.clientY);\n  }\n  return undefined;\n}\n\nfunction treeToArray(elem) {\n  var MAX_HEIGHT = 5;\n  var out = [];\n  var nextDescription;\n  for (var height = 0; elem && height < MAX_HEIGHT; height++) {\n    nextDescription = describeElement(elem);\n    if (nextDescription.tagName === 'html') {\n      break;\n    }\n    out.unshift(nextDescription);\n    elem = elem.parentNode;\n  }\n  return out;\n}\n\nfunction elementArrayToString(a) {\n  var MAX_LENGTH = 80;\n  var separator = ' > ',\n    separatorLength = separator.length;\n  var out = [],\n    len = 0,\n    nextStr,\n    totalLength;\n\n  for (var i = a.length - 1; i >= 0; i--) {\n    nextStr = descriptionToString(a[i]);\n    totalLength = len + out.length * separatorLength + nextStr.length;\n    if (i < a.length - 1 && totalLength >= MAX_LENGTH + 3) {\n      out.unshift('...');\n      break;\n    }\n    out.unshift(nextStr);\n    len += nextStr.length;\n  }\n  return out.join(separator);\n}\n\nfunction descriptionToString(desc) {\n  if (!desc || !desc.tagName) {\n    return '';\n  }\n  var out = [desc.tagName];\n  if (desc.id) {\n    out.push('#' + desc.id);\n  }\n  if (desc.classes) {\n    out.push('.' + desc.classes.join('.'));\n  }\n  for (var i = 0; i < desc.attributes.length; i++) {\n    out.push(\n      '[' + desc.attributes[i].key + '=\"' + desc.attributes[i].value + '\"]',\n    );\n  }\n\n  return out.join('');\n}\n\n/**\n * Input: a dom element\n * Output: null if tagName is falsey or input is falsey, else\n *  {\n *    tagName: String,\n *    id: String | undefined,\n *    classes: [String] | undefined,\n *    attributes: [\n *      {\n *        key: OneOf(type, name, title, alt),\n *        value: String\n *      }\n *    ]\n *  }\n */\nfunction describeElement(elem) {\n  if (!elem || !elem.tagName) {\n    return null;\n  }\n  var out = {},\n    className,\n    key,\n    attr,\n    i;\n  out.tagName = elem.tagName.toLowerCase();\n  if (elem.id) {\n    out.id = elem.id;\n  }\n  className = elem.className;\n  if (className && typeof className === 'string') {\n    out.classes = className.split(/\\s+/);\n  }\n  var attributes = ['type', 'name', 'title', 'alt'];\n  out.attributes = [];\n  for (i = 0; i < attributes.length; i++) {\n    key = attributes[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.attributes.push({ key: key, value: attr });\n    }\n  }\n  return out;\n}\n\nmodule.exports = {\n  describeElement: describeElement,\n  descriptionToString: descriptionToString,\n  elementArrayToString: elementArrayToString,\n  treeToArray: treeToArray,\n  getElementFromEvent: getElementFromEvent,\n  isDescribedElement: isDescribedElement,\n  getElementType: getElementType,\n};\n", "'use strict';\n\nfunction captureUncaughtExceptions(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  var oldOnError;\n\n  if (typeof handler._rollbarOldOnError === 'function') {\n    oldOnError = handler._rollbarOldOnError;\n  } else if (window.onerror) {\n    oldOnError = window.onerror;\n    while (oldOnError._rollbarOldOnError) {\n      oldOnError = oldOnError._rollbarOldOnError;\n    }\n    handler._rollbarOldOnError = oldOnError;\n  }\n\n  handler.handleAnonymousErrors();\n\n  var fn = function () {\n    var args = Array.prototype.slice.call(arguments, 0);\n    _rollbarWindowOnError(window, handler, oldOnError, args);\n  };\n  if (shim) {\n    fn._rollbarOldOnError = oldOnError;\n  }\n  window.onerror = fn;\n}\n\nfunction _rollbarWindowOnError(window, r, old, args) {\n  if (window._rollbarWrappedError) {\n    if (!args[4]) {\n      args[4] = window._rollbarWrappedError;\n    }\n    if (!args[5]) {\n      args[5] = window._rollbarWrappedError._rollbarContext;\n    }\n    window._rollbarWrappedError = null;\n  }\n\n  var ret = r.handleUncaughtException.apply(r, args);\n\n  if (old) {\n    old.apply(window, args);\n  }\n\n  // Let other chained onerror handlers above run before setting this.\n  // If an error is thrown and caught within a chained onerror handler,\n  // Error.prepareStackTrace() will see that one before the one we want.\n  if (ret === 'anonymous') {\n    r.anonymousErrorsPending += 1; // See Rollbar.prototype.handleAnonymousErrors()\n  }\n}\n\nfunction captureUnhandledRejections(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n\n  if (\n    typeof window._rollbarURH === 'function' &&\n    window._rollbarURH.belongsToShim\n  ) {\n    window.removeEventListener('unhandledrejection', window._rollbarURH);\n  }\n\n  var rejectionHandler = function (evt) {\n    var reason, promise, detail;\n    try {\n      reason = evt.reason;\n    } catch (e) {\n      reason = undefined;\n    }\n    try {\n      promise = evt.promise;\n    } catch (e) {\n      promise = '[unhandledrejection] error getting `promise` from event';\n    }\n    try {\n      detail = evt.detail;\n      if (!reason && detail) {\n        reason = detail.reason;\n        promise = detail.promise;\n      }\n    } catch (e) {\n      // Ignore\n    }\n    if (!reason) {\n      reason = '[unhandledrejection] error getting `reason` from event';\n    }\n\n    if (handler && handler.handleUnhandledRejection) {\n      handler.handleUnhandledRejection(reason, promise);\n    }\n  };\n  rejectionHandler.belongsToShim = shim;\n  window._rollbarURH = rejectionHandler;\n  window.addEventListener('unhandledrejection', rejectionHandler);\n}\n\nmodule.exports = {\n  captureUncaughtExceptions: captureUncaughtExceptions,\n  captureUnhandledRejections: captureUnhandledRejections,\n};\n", "'use strict';\n\n/* eslint-disable no-console */\nrequire('console-polyfill');\nvar detection = require('./detection');\nvar _ = require('../utility');\n\nfunction error() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.error(_.formatArgsAsString(args));\n  } else {\n    console.error.apply(console, args);\n  }\n}\n\nfunction info() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.info(_.formatArgsAsString(args));\n  } else {\n    console.info.apply(console, args);\n  }\n}\n\nfunction log() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.log(_.formatArgsAsString(args));\n  } else {\n    console.log.apply(console, args);\n  }\n}\n\n/* eslint-enable no-console */\n\nmodule.exports = {\n  error: error,\n  info: info,\n  log: log,\n};\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction checkIgnore(item, settings) {\n  if (_.get(settings, 'plugins.jquery.ignoreAjaxErrors')) {\n    return !_.get(item, 'body.message.extra.isAjax');\n  }\n  return true;\n}\n\nmodule.exports = {\n  checkIgnore: checkIgnore,\n};\n", "'use strict';\n\nvar Rollbar = require('./core');\nvar telemeter = require('../telemetry');\nvar instrumenter = require('./telemetry');\nvar polyfillJSON = require('../utility/polyfillJSON');\nvar wrapGlobals = require('./wrapGlobals');\nvar scrub = require('../scrub');\nvar truncation = require('../truncation');\n\nRollbar.setComponents({\n  telemeter: telemeter,\n  instrumenter: instrumenter,\n  polyfillJSON: polyfillJSON,\n  wrapGlobals: wrapGlobals,\n  scrub: scrub,\n  truncation: truncation,\n});\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('../utility');\nvar headers = require('../utility/headers');\nvar replace = require('../utility/replace');\nvar scrub = require('../scrub');\nvar urlparser = require('./url');\nvar domUtil = require('./domUtility');\n\nvar defaults = {\n  network: true,\n  networkResponseHeaders: false,\n  networkResponseBody: false,\n  networkRequestHeaders: false,\n  networkRequestBody: false,\n  networkErrorOnHttp5xx: false,\n  networkErrorOnHttp4xx: false,\n  networkErrorOnHttp0: false,\n  log: true,\n  dom: true,\n  navigation: true,\n  connectivity: true,\n  contentSecurityPolicy: true,\n  errorOnContentSecurityPolicy: false,\n};\n\nfunction restore(replacements, type) {\n  var b;\n  while (replacements[type].length) {\n    b = replacements[type].shift();\n    b[0][b[1]] = b[2];\n  }\n}\n\nfunction nameFromDescription(description) {\n  if (!description || !description.attributes) {\n    return null;\n  }\n  var attrs = description.attributes;\n  for (var a = 0; a < attrs.length; ++a) {\n    if (attrs[a].key === 'name') {\n      return attrs[a].value;\n    }\n  }\n  return null;\n}\n\nfunction defaultValueScrubber(scrubFields) {\n  var patterns = [];\n  for (var i = 0; i < scrubFields.length; ++i) {\n    patterns.push(new RegExp(scrubFields[i], 'i'));\n  }\n  return function (description) {\n    var name = nameFromDescription(description);\n    if (!name) {\n      return false;\n    }\n    for (var i = 0; i < patterns.length; ++i) {\n      if (patterns[i].test(name)) {\n        return true;\n      }\n    }\n    return false;\n  };\n}\n\nfunction Instrumenter(options, telemeter, rollbar, _window, _document) {\n  this.options = options;\n  var autoInstrument = options.autoInstrument;\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  this.telemetryScrubber = options.telemetryScrubber;\n  this.defaultValueScrubber = defaultValueScrubber(options.scrubFields);\n  this.telemeter = telemeter;\n  this.rollbar = rollbar;\n  this.diagnostic = rollbar.client.notifier.diagnostic;\n  this._window = _window || {};\n  this._document = _document || {};\n  this.replacements = {\n    network: [],\n    log: [],\n    navigation: [],\n    connectivity: [],\n  };\n  this.eventRemovers = {\n    dom: [],\n    connectivity: [],\n    contentsecuritypolicy: [],\n  };\n\n  this._location = this._window.location;\n  this._lastHref = this._location && this._location.href;\n}\n\nInstrumenter.prototype.configure = function (options) {\n  this.options = _.merge(this.options, options);\n  var autoInstrument = options.autoInstrument;\n  var oldSettings = _.merge(this.autoInstrument);\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.instrument(oldSettings);\n  if (options.scrubTelemetryInputs !== undefined) {\n    this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  }\n  if (options.telemetryScrubber !== undefined) {\n    this.telemetryScrubber = options.telemetryScrubber;\n  }\n};\n\n// eslint-disable-next-line complexity\nInstrumenter.prototype.instrument = function (oldSettings) {\n  if (this.autoInstrument.network && !(oldSettings && oldSettings.network)) {\n    this.instrumentNetwork();\n  } else if (\n    !this.autoInstrument.network &&\n    oldSettings &&\n    oldSettings.network\n  ) {\n    this.deinstrumentNetwork();\n  }\n\n  if (this.autoInstrument.log && !(oldSettings && oldSettings.log)) {\n    this.instrumentConsole();\n  } else if (!this.autoInstrument.log && oldSettings && oldSettings.log) {\n    this.deinstrumentConsole();\n  }\n\n  if (this.autoInstrument.dom && !(oldSettings && oldSettings.dom)) {\n    this.instrumentDom();\n  } else if (!this.autoInstrument.dom && oldSettings && oldSettings.dom) {\n    this.deinstrumentDom();\n  }\n\n  if (\n    this.autoInstrument.navigation &&\n    !(oldSettings && oldSettings.navigation)\n  ) {\n    this.instrumentNavigation();\n  } else if (\n    !this.autoInstrument.navigation &&\n    oldSettings &&\n    oldSettings.navigation\n  ) {\n    this.deinstrumentNavigation();\n  }\n\n  if (\n    this.autoInstrument.connectivity &&\n    !(oldSettings && oldSettings.connectivity)\n  ) {\n    this.instrumentConnectivity();\n  } else if (\n    !this.autoInstrument.connectivity &&\n    oldSettings &&\n    oldSettings.connectivity\n  ) {\n    this.deinstrumentConnectivity();\n  }\n\n  if (\n    this.autoInstrument.contentSecurityPolicy &&\n    !(oldSettings && oldSettings.contentSecurityPolicy)\n  ) {\n    this.instrumentContentSecurityPolicy();\n  } else if (\n    !this.autoInstrument.contentSecurityPolicy &&\n    oldSettings &&\n    oldSettings.contentSecurityPolicy\n  ) {\n    this.deinstrumentContentSecurityPolicy();\n  }\n};\n\nInstrumenter.prototype.deinstrumentNetwork = function () {\n  restore(this.replacements, 'network');\n};\n\nInstrumenter.prototype.instrumentNetwork = function () {\n  var self = this;\n\n  function wrapProp(prop, xhr) {\n    if (prop in xhr && _.isFunction(xhr[prop])) {\n      replace(xhr, prop, function (orig) {\n        return self.rollbar.wrap(orig);\n      });\n    }\n  }\n\n  if ('XMLHttpRequest' in this._window) {\n    var xhrp = this._window.XMLHttpRequest.prototype;\n    replace(\n      xhrp,\n      'open',\n      function (orig) {\n        return function (method, url) {\n          var isUrlObject = _isUrlObject(url);\n          if (_.isType(url, 'string') || isUrlObject) {\n            url = isUrlObject ? url.toString() : url;\n            if (this.__rollbar_xhr) {\n              this.__rollbar_xhr.method = method;\n              this.__rollbar_xhr.url = url;\n              this.__rollbar_xhr.status_code = null;\n              this.__rollbar_xhr.start_time_ms = _.now();\n              this.__rollbar_xhr.end_time_ms = null;\n            } else {\n              this.__rollbar_xhr = {\n                method: method,\n                url: url,\n                status_code: null,\n                start_time_ms: _.now(),\n                end_time_ms: null,\n              };\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'setRequestHeader',\n      function (orig) {\n        return function (header, value) {\n          // If xhr.open is async, __rollbar_xhr may not be initialized yet.\n          if (!this.__rollbar_xhr) {\n            this.__rollbar_xhr = {};\n          }\n          if (_.isType(header, 'string') && _.isType(value, 'string')) {\n            if (self.autoInstrument.networkRequestHeaders) {\n              if (!this.__rollbar_xhr.request_headers) {\n                this.__rollbar_xhr.request_headers = {};\n              }\n              this.__rollbar_xhr.request_headers[header] = value;\n            }\n            // We want the content type even if request header telemetry is off.\n            if (header.toLowerCase() === 'content-type') {\n              this.__rollbar_xhr.request_content_type = value;\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'send',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (data) {\n          /* eslint-enable no-unused-vars */\n          var xhr = this;\n\n          function onreadystatechangeHandler() {\n            if (xhr.__rollbar_xhr) {\n              if (xhr.__rollbar_xhr.status_code === null) {\n                xhr.__rollbar_xhr.status_code = 0;\n                if (self.autoInstrument.networkRequestBody) {\n                  xhr.__rollbar_xhr.request = data;\n                }\n                xhr.__rollbar_event = self.captureNetwork(\n                  xhr.__rollbar_xhr,\n                  'xhr',\n                  undefined,\n                );\n              }\n              if (xhr.readyState < 2) {\n                xhr.__rollbar_xhr.start_time_ms = _.now();\n              }\n              if (xhr.readyState > 3) {\n                xhr.__rollbar_xhr.end_time_ms = _.now();\n\n                var headers = null;\n                xhr.__rollbar_xhr.response_content_type =\n                  xhr.getResponseHeader('Content-Type');\n                if (self.autoInstrument.networkResponseHeaders) {\n                  var headersConfig =\n                    self.autoInstrument.networkResponseHeaders;\n                  headers = {};\n                  try {\n                    var header, i;\n                    if (headersConfig === true) {\n                      var allHeaders = xhr.getAllResponseHeaders();\n                      if (allHeaders) {\n                        var arr = allHeaders.trim().split(/[\\r\\n]+/);\n                        var parts, value;\n                        for (i = 0; i < arr.length; i++) {\n                          parts = arr[i].split(': ');\n                          header = parts.shift();\n                          value = parts.join(': ');\n                          headers[header] = value;\n                        }\n                      }\n                    } else {\n                      for (i = 0; i < headersConfig.length; i++) {\n                        header = headersConfig[i];\n                        headers[header] = xhr.getResponseHeader(header);\n                      }\n                    }\n                  } catch (e) {\n                    /* we ignore the errors here that could come from different\n                     * browser issues with the xhr methods */\n                  }\n                }\n                var body = null;\n                if (self.autoInstrument.networkResponseBody) {\n                  try {\n                    body = xhr.responseText;\n                  } catch (e) {\n                    /* ignore errors from reading responseText */\n                  }\n                }\n                var response = null;\n                if (body || headers) {\n                  response = {};\n                  if (body) {\n                    if (\n                      self.isJsonContentType(\n                        xhr.__rollbar_xhr.response_content_type,\n                      )\n                    ) {\n                      response.body = self.scrubJson(body);\n                    } else {\n                      response.body = body;\n                    }\n                  }\n                  if (headers) {\n                    response.headers = headers;\n                  }\n                }\n                if (response) {\n                  xhr.__rollbar_xhr.response = response;\n                }\n                try {\n                  var code = xhr.status;\n                  code = code === 1223 ? 204 : code;\n                  xhr.__rollbar_xhr.status_code = code;\n                  xhr.__rollbar_event.level =\n                    self.telemeter.levelFromStatus(code);\n                  self.errorOnHttpStatus(xhr.__rollbar_xhr);\n                } catch (e) {\n                  /* ignore possible exception from xhr.status */\n                }\n              }\n            }\n          }\n\n          wrapProp('onload', xhr);\n          wrapProp('onerror', xhr);\n          wrapProp('onprogress', xhr);\n\n          if (\n            'onreadystatechange' in xhr &&\n            _.isFunction(xhr.onreadystatechange)\n          ) {\n            replace(xhr, 'onreadystatechange', function (orig) {\n              return self.rollbar.wrap(\n                orig,\n                undefined,\n                onreadystatechangeHandler,\n              );\n            });\n          } else {\n            xhr.onreadystatechange = onreadystatechangeHandler;\n          }\n          if (xhr.__rollbar_xhr && self.trackHttpErrors()) {\n            xhr.__rollbar_xhr.stack = new Error().stack;\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n\n  if ('fetch' in this._window) {\n    replace(\n      this._window,\n      'fetch',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (fn, t) {\n          /* eslint-enable no-unused-vars */\n          var args = new Array(arguments.length);\n          for (var i = 0, len = args.length; i < len; i++) {\n            args[i] = arguments[i];\n          }\n          var input = args[0];\n          var method = 'GET';\n          var url;\n          var isUrlObject = _isUrlObject(input);\n          if (_.isType(input, 'string') || isUrlObject) {\n            url = isUrlObject ? input.toString() : input;\n          } else if (input) {\n            url = input.url;\n            if (input.method) {\n              method = input.method;\n            }\n          }\n          if (args[1] && args[1].method) {\n            method = args[1].method;\n          }\n          var metadata = {\n            method: method,\n            url: url,\n            status_code: null,\n            start_time_ms: _.now(),\n            end_time_ms: null,\n          };\n          if (args[1] && args[1].headers) {\n            // Argument may be a Headers object, or plain object. Ensure here that\n            // we are working with a Headers object with case-insensitive keys.\n            var reqHeaders = headers(args[1].headers);\n\n            metadata.request_content_type = reqHeaders.get('Content-Type');\n\n            if (self.autoInstrument.networkRequestHeaders) {\n              metadata.request_headers = self.fetchHeaders(\n                reqHeaders,\n                self.autoInstrument.networkRequestHeaders,\n              );\n            }\n          }\n\n          if (self.autoInstrument.networkRequestBody) {\n            if (args[1] && args[1].body) {\n              metadata.request = args[1].body;\n            } else if (\n              args[0] &&\n              !_.isType(args[0], 'string') &&\n              args[0].body\n            ) {\n              metadata.request = args[0].body;\n            }\n          }\n          self.captureNetwork(metadata, 'fetch', undefined);\n          if (self.trackHttpErrors()) {\n            metadata.stack = new Error().stack;\n          }\n\n          // Start our handler before returning the promise. This allows resp.clone()\n          // to execute before other handlers touch the response.\n          return orig.apply(this, args).then(function (resp) {\n            metadata.end_time_ms = _.now();\n            metadata.status_code = resp.status;\n            metadata.response_content_type = resp.headers.get('Content-Type');\n            var headers = null;\n            if (self.autoInstrument.networkResponseHeaders) {\n              headers = self.fetchHeaders(\n                resp.headers,\n                self.autoInstrument.networkResponseHeaders,\n              );\n            }\n            var body = null;\n            if (self.autoInstrument.networkResponseBody) {\n              if (typeof resp.text === 'function') {\n                // Response.text() is not implemented on some platforms\n                // The response must be cloned to prevent reading (and locking) the original stream.\n                // This must be done before other handlers touch the response.\n                body = resp.clone().text(); //returns a Promise\n              }\n            }\n            if (headers || body) {\n              metadata.response = {};\n              if (body) {\n                // Test to ensure body is a Promise, which it should always be.\n                if (typeof body.then === 'function') {\n                  body.then(function (text) {\n                    if (\n                      text &&\n                      self.isJsonContentType(metadata.response_content_type)\n                    ) {\n                      metadata.response.body = self.scrubJson(text);\n                    } else {\n                      metadata.response.body = text;\n                    }\n                  });\n                } else {\n                  metadata.response.body = body;\n                }\n              }\n              if (headers) {\n                metadata.response.headers = headers;\n              }\n            }\n            self.errorOnHttpStatus(metadata);\n            return resp;\n          });\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n};\n\nInstrumenter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n) {\n  if (\n    metadata.request &&\n    this.isJsonContentType(metadata.request_content_type)\n  ) {\n    metadata.request = this.scrubJson(metadata.request);\n  }\n  return this.telemeter.captureNetwork(metadata, subtype, rollbarUUID);\n};\n\nInstrumenter.prototype.isJsonContentType = function (contentType) {\n  return contentType &&\n    _.isType(contentType, 'string') &&\n    contentType.toLowerCase().includes('json')\n    ? true\n    : false;\n};\n\nInstrumenter.prototype.scrubJson = function (json) {\n  return JSON.stringify(scrub(JSON.parse(json), this.options.scrubFields));\n};\n\nInstrumenter.prototype.fetchHeaders = function (inHeaders, headersConfig) {\n  var outHeaders = {};\n  try {\n    var i;\n    if (headersConfig === true) {\n      if (typeof inHeaders.entries === 'function') {\n        // Headers.entries() is not implemented in IE\n        var allHeaders = inHeaders.entries();\n        var currentHeader = allHeaders.next();\n        while (!currentHeader.done) {\n          outHeaders[currentHeader.value[0]] = currentHeader.value[1];\n          currentHeader = allHeaders.next();\n        }\n      }\n    } else {\n      for (i = 0; i < headersConfig.length; i++) {\n        var header = headersConfig[i];\n        outHeaders[header] = inHeaders.get(header);\n      }\n    }\n  } catch (e) {\n    /* ignore probable IE errors */\n  }\n  return outHeaders;\n};\n\nInstrumenter.prototype.trackHttpErrors = function () {\n  return (\n    this.autoInstrument.networkErrorOnHttp5xx ||\n    this.autoInstrument.networkErrorOnHttp4xx ||\n    this.autoInstrument.networkErrorOnHttp0\n  );\n};\n\nInstrumenter.prototype.errorOnHttpStatus = function (metadata) {\n  var status = metadata.status_code;\n\n  if (\n    (status >= 500 && this.autoInstrument.networkErrorOnHttp5xx) ||\n    (status >= 400 && this.autoInstrument.networkErrorOnHttp4xx) ||\n    (status === 0 && this.autoInstrument.networkErrorOnHttp0)\n  ) {\n    var error = new Error('HTTP request failed with Status ' + status);\n    error.stack = metadata.stack;\n    this.rollbar.error(error, { skipFrames: 1 });\n  }\n};\n\nInstrumenter.prototype.deinstrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n  var b;\n  while (this.replacements['log'].length) {\n    b = this.replacements['log'].shift();\n    this._window.console[b[0]] = b[1];\n  }\n};\n\nInstrumenter.prototype.instrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n\n  var self = this;\n  var c = this._window.console;\n\n  function wrapConsole(method) {\n    'use strict'; // See https://github.com/rollbar/rollbar.js/pull/778\n\n    var orig = c[method];\n    var origConsole = c;\n    var level = method === 'warn' ? 'warning' : method;\n    c[method] = function () {\n      var args = Array.prototype.slice.call(arguments);\n      var message = _.formatArgsAsString(args);\n      self.telemeter.captureLog(message, level);\n      if (orig) {\n        Function.prototype.apply.call(orig, origConsole, args);\n      }\n    };\n    self.replacements['log'].push([method, orig]);\n  }\n  var methods = ['debug', 'info', 'warn', 'error', 'log'];\n  try {\n    for (var i = 0, len = methods.length; i < len; i++) {\n      wrapConsole(methods[i]);\n    }\n  } catch (e) {\n    this.diagnostic.instrumentConsole = { error: e.message };\n  }\n};\n\nInstrumenter.prototype.deinstrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  this.removeListeners('dom');\n};\n\nInstrumenter.prototype.instrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  var clickHandler = this.handleClick.bind(this);\n  var blurHandler = this.handleBlur.bind(this);\n  this.addListener('dom', this._window, 'click', 'onclick', clickHandler, true);\n  this.addListener(\n    'dom',\n    this._window,\n    'blur',\n    'onfocusout',\n    blurHandler,\n    true,\n  );\n};\n\nInstrumenter.prototype.handleClick = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    var hasTag = e && e.tagName;\n    var anchorOrButton =\n      domUtil.isDescribedElement(e, 'a') ||\n      domUtil.isDescribedElement(e, 'button');\n    if (\n      hasTag &&\n      (anchorOrButton ||\n        domUtil.isDescribedElement(e, 'input', ['button', 'submit']))\n    ) {\n      this.captureDomEvent('click', e);\n    } else if (domUtil.isDescribedElement(e, 'input', ['checkbox', 'radio'])) {\n      this.captureDomEvent('input', e, e.value, e.checked);\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleBlur = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    if (e && e.tagName) {\n      if (domUtil.isDescribedElement(e, 'textarea')) {\n        this.captureDomEvent('input', e, e.value);\n      } else if (\n        domUtil.isDescribedElement(e, 'select') &&\n        e.options &&\n        e.options.length\n      ) {\n        this.handleSelectInputChanged(e);\n      } else if (\n        domUtil.isDescribedElement(e, 'input') &&\n        !domUtil.isDescribedElement(e, 'input', [\n          'button',\n          'submit',\n          'hidden',\n          'checkbox',\n          'radio',\n        ])\n      ) {\n        this.captureDomEvent('input', e, e.value);\n      }\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleSelectInputChanged = function (elem) {\n  if (elem.multiple) {\n    for (var i = 0; i < elem.options.length; i++) {\n      if (elem.options[i].selected) {\n        this.captureDomEvent('input', elem, elem.options[i].value);\n      }\n    }\n  } else if (elem.selectedIndex >= 0 && elem.options[elem.selectedIndex]) {\n    this.captureDomEvent('input', elem, elem.options[elem.selectedIndex].value);\n  }\n};\n\nInstrumenter.prototype.captureDomEvent = function (\n  subtype,\n  element,\n  value,\n  isChecked,\n) {\n  if (value !== undefined) {\n    if (\n      this.scrubTelemetryInputs ||\n      domUtil.getElementType(element) === 'password'\n    ) {\n      value = '[scrubbed]';\n    } else {\n      var description = domUtil.describeElement(element);\n      if (this.telemetryScrubber) {\n        if (this.telemetryScrubber(description)) {\n          value = '[scrubbed]';\n        }\n      } else if (this.defaultValueScrubber(description)) {\n        value = '[scrubbed]';\n      }\n    }\n  }\n  var elementString = domUtil.elementArrayToString(\n    domUtil.treeToArray(element),\n  );\n  this.telemeter.captureDom(subtype, elementString, value, isChecked);\n};\n\nInstrumenter.prototype.deinstrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  restore(this.replacements, 'navigation');\n};\n\nInstrumenter.prototype.instrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  var self = this;\n  replace(\n    this._window,\n    'onpopstate',\n    function (orig) {\n      return function () {\n        var current = self._location.href;\n        self.handleUrlChange(self._lastHref, current);\n        if (orig) {\n          orig.apply(this, arguments);\n        }\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n\n  replace(\n    this._window.history,\n    'pushState',\n    function (orig) {\n      return function () {\n        var url = arguments.length > 2 ? arguments[2] : undefined;\n        if (url) {\n          self.handleUrlChange(self._lastHref, url + '');\n        }\n        return orig.apply(this, arguments);\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n};\n\nInstrumenter.prototype.handleUrlChange = function (from, to) {\n  var parsedHref = urlparser.parse(this._location.href);\n  var parsedTo = urlparser.parse(to);\n  var parsedFrom = urlparser.parse(from);\n  this._lastHref = to;\n  if (\n    parsedHref.protocol === parsedTo.protocol &&\n    parsedHref.host === parsedTo.host\n  ) {\n    to = parsedTo.path + (parsedTo.hash || '');\n  }\n  if (\n    parsedHref.protocol === parsedFrom.protocol &&\n    parsedHref.host === parsedFrom.host\n  ) {\n    from = parsedFrom.path + (parsedFrom.hash || '');\n  }\n  this.telemeter.captureNavigation(from, to);\n};\n\nInstrumenter.prototype.deinstrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.removeListeners('connectivity');\n  } else {\n    restore(this.replacements, 'connectivity');\n  }\n};\n\nInstrumenter.prototype.instrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.addListener(\n      'connectivity',\n      this._window,\n      'online',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('online');\n      }.bind(this),\n      true,\n    );\n    this.addListener(\n      'connectivity',\n      this._window,\n      'offline',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('offline');\n      }.bind(this),\n      true,\n    );\n  } else {\n    var self = this;\n    replace(\n      this._document.body,\n      'ononline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('online');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n    replace(\n      this._document.body,\n      'onoffline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('offline');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n  }\n};\n\nInstrumenter.prototype.handleCspEvent = function (cspEvent) {\n  var message =\n    'Security Policy Violation: ' +\n    'blockedURI: ' +\n    cspEvent.blockedURI +\n    ', ' +\n    'violatedDirective: ' +\n    cspEvent.violatedDirective +\n    ', ' +\n    'effectiveDirective: ' +\n    cspEvent.effectiveDirective +\n    ', ';\n\n  if (cspEvent.sourceFile) {\n    message +=\n      'location: ' +\n      cspEvent.sourceFile +\n      ', ' +\n      'line: ' +\n      cspEvent.lineNumber +\n      ', ' +\n      'col: ' +\n      cspEvent.columnNumber +\n      ', ';\n  }\n\n  message += 'originalPolicy: ' + cspEvent.originalPolicy;\n\n  this.telemeter.captureLog(message, 'error');\n  this.handleCspError(message);\n};\n\nInstrumenter.prototype.handleCspError = function (message) {\n  if (this.autoInstrument.errorOnContentSecurityPolicy) {\n    this.rollbar.error(message);\n  }\n};\n\nInstrumenter.prototype.deinstrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  this.removeListeners('contentsecuritypolicy');\n};\n\nInstrumenter.prototype.instrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  var cspHandler = this.handleCspEvent.bind(this);\n  this.addListener(\n    'contentsecuritypolicy',\n    this._document,\n    'securitypolicyviolation',\n    null,\n    cspHandler,\n    false,\n  );\n};\n\nInstrumenter.prototype.addListener = function (\n  section,\n  obj,\n  type,\n  altType,\n  handler,\n  capture,\n) {\n  if (obj.addEventListener) {\n    obj.addEventListener(type, handler, capture);\n    this.eventRemovers[section].push(function () {\n      obj.removeEventListener(type, handler, capture);\n    });\n  } else if (altType) {\n    obj.attachEvent(altType, handler);\n    this.eventRemovers[section].push(function () {\n      obj.detachEvent(altType, handler);\n    });\n  }\n};\n\nInstrumenter.prototype.removeListeners = function (section) {\n  var r;\n  while (this.eventRemovers[section].length) {\n    r = this.eventRemovers[section].shift();\n    r();\n  }\n};\n\nfunction _isUrlObject(input) {\n  return typeof URL !== 'undefined' && input instanceof URL;\n}\n\nmodule.exports = Instrumenter;\n", "'use strict';\n\nvar _ = require('../utility');\nvar errorParser = require('../errorParser');\nvar logger = require('./logger');\n\nfunction handleDomException(item, options, callback) {\n  if (item.err && errorParser.Stack(item.err).name === 'DOMException') {\n    var originalError = new Error();\n    originalError.name = item.err.name;\n    originalError.message = item.err.message;\n    originalError.stack = item.err.stack;\n    originalError.nested = item.err;\n    item.err = originalError;\n  }\n  callback(null, item);\n}\n\nfunction handleItemWithError(item, options, callback) {\n  item.data = item.data || {};\n  if (item.err) {\n    try {\n      item.stackInfo =\n        item.err._savedStackTrace ||\n        errorParser.parse(item.err, item.skipFrames);\n\n      if (options.addErrorContext) {\n        addErrorContext(item);\n      }\n    } catch (e) {\n      logger.error('Error while parsing the error object.', e);\n      try {\n        item.message =\n          item.err.message ||\n          item.err.description ||\n          item.message ||\n          String(item.err);\n      } catch (e2) {\n        item.message = String(item.err) || String(e2);\n      }\n      delete item.err;\n    }\n  }\n  callback(null, item);\n}\n\nfunction addErrorContext(item) {\n  var chain = [];\n  var err = item.err;\n\n  chain.push(err);\n\n  while (err.nested || err.cause) {\n    err = err.nested || err.cause;\n    chain.push(err);\n  }\n\n  _.addErrorContext(item, chain);\n}\n\nfunction ensureItemHasSomethingToSay(item, options, callback) {\n  if (!item.message && !item.stackInfo && !item.custom) {\n    callback(new Error('No message, stack info, or custom data'), null);\n  }\n  callback(null, item);\n}\n\nfunction addBaseInfo(item, options, callback) {\n  var environment =\n    (options.payload && options.payload.environment) || options.environment;\n  item.data = _.merge(item.data, {\n    environment: environment,\n    level: item.level,\n    endpoint: options.endpoint,\n    platform: 'browser',\n    framework: 'browser-js',\n    language: 'javascript',\n    server: {},\n    uuid: item.uuid,\n    notifier: {\n      name: 'rollbar-browser-js',\n      version: options.version,\n    },\n    custom: item.custom,\n  });\n  callback(null, item);\n}\n\nfunction addRequestInfo(window) {\n  return function (item, options, callback) {\n    var requestInfo = {};\n\n    if (window && window.location) {\n      requestInfo.url = window.location.href;\n      requestInfo.query_string = window.location.search;\n    }\n\n    var remoteString = '$remote_ip';\n    if (!options.captureIp) {\n      remoteString = null;\n    } else if (options.captureIp !== true) {\n      remoteString += '_anonymize';\n    }\n    if (remoteString) requestInfo.user_ip = remoteString;\n\n    if (Object.keys(requestInfo).length > 0) {\n      _.set(item, 'data.request', requestInfo);\n    }\n\n    callback(null, item);\n  };\n}\n\nfunction addClientInfo(window) {\n  return function (item, options, callback) {\n    if (!window) {\n      return callback(null, item);\n    }\n    var nav = window.navigator || {};\n    var scr = window.screen || {};\n    _.set(item, 'data.client', {\n      runtime_ms: item.timestamp - window._rollbarStartTime,\n      timestamp: Math.round(item.timestamp / 1000),\n      javascript: {\n        browser: nav.userAgent,\n        language: nav.language,\n        cookie_enabled: nav.cookieEnabled,\n        screen: {\n          width: scr.width,\n          height: scr.height,\n        },\n      },\n    });\n    callback(null, item);\n  };\n}\n\nfunction addPluginInfo(window) {\n  return function (item, options, callback) {\n    if (!window || !window.navigator) {\n      return callback(null, item);\n    }\n    var plugins = [];\n    var navPlugins = window.navigator.plugins || [];\n    var cur;\n    for (var i = 0, l = navPlugins.length; i < l; ++i) {\n      cur = navPlugins[i];\n      plugins.push({ name: cur.name, description: cur.description });\n    }\n    _.set(item, 'data.client.javascript.plugins', plugins);\n    callback(null, item);\n  };\n}\n\nfunction addBody(item, options, callback) {\n  if (item.stackInfo) {\n    if (item.stackInfo.traceChain) {\n      addBodyTraceChain(item, options, callback);\n    } else {\n      addBodyTrace(item, options, callback);\n    }\n  } else {\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction addBodyMessage(item, options, callback) {\n  var message = item.message;\n  var custom = item.custom;\n\n  if (!message) {\n    message = 'Item sent with null or missing arguments.';\n  }\n  var result = {\n    body: message,\n  };\n\n  if (custom) {\n    result.extra = _.merge(custom);\n  }\n\n  _.set(item, 'data.body', { message: result });\n  callback(null, item);\n}\n\nfunction stackFromItem(item) {\n  // Transform a TraceKit stackInfo object into a Rollbar trace\n  var stack = item.stackInfo.stack;\n  if (\n    stack &&\n    stack.length === 0 &&\n    item._unhandledStackInfo &&\n    item._unhandledStackInfo.stack\n  ) {\n    stack = item._unhandledStackInfo.stack;\n  }\n  return stack;\n}\n\nfunction addBodyTraceChain(item, options, callback) {\n  var traceChain = item.stackInfo.traceChain;\n  var traces = [];\n\n  var traceChainLength = traceChain.length;\n  for (var i = 0; i < traceChainLength; i++) {\n    var trace = buildTrace(item, traceChain[i], options);\n    traces.push(trace);\n  }\n\n  _.set(item, 'data.body', { trace_chain: traces });\n  callback(null, item);\n}\n\nfunction addBodyTrace(item, options, callback) {\n  var stack = stackFromItem(item);\n\n  if (stack) {\n    var trace = buildTrace(item, item.stackInfo, options);\n    _.set(item, 'data.body', { trace: trace });\n    callback(null, item);\n  } else {\n    var stackInfo = item.stackInfo;\n    var guess = errorParser.guessErrorClass(stackInfo.message);\n    var className = errorClass(stackInfo, guess[0], options);\n    var message = guess[1];\n\n    item.message = className + ': ' + message;\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction buildTrace(item, stackInfo, options) {\n  var description = item && item.data.description;\n  var custom = item && item.custom;\n  var stack = stackFromItem(item);\n\n  var guess = errorParser.guessErrorClass(stackInfo.message);\n  var className = errorClass(stackInfo, guess[0], options);\n  var message = guess[1];\n  var trace = {\n    exception: {\n      class: className,\n      message: message,\n    },\n  };\n\n  if (description) {\n    trace.exception.description = description;\n  }\n\n  if (stack) {\n    if (stack.length === 0) {\n      trace.exception.stack = stackInfo.rawStack;\n      trace.exception.raw = String(stackInfo.rawException);\n    }\n    var stackFrame;\n    var frame;\n    var code;\n    var pre;\n    var post;\n    var contextLength;\n    var i, mid;\n\n    trace.frames = [];\n    for (i = 0; i < stack.length; ++i) {\n      stackFrame = stack[i];\n      frame = {\n        filename: stackFrame.url ? _.sanitizeUrl(stackFrame.url) : '(unknown)',\n        lineno: stackFrame.line || null,\n        method:\n          !stackFrame.func || stackFrame.func === '?'\n            ? '[anonymous]'\n            : stackFrame.func,\n        colno: stackFrame.column,\n      };\n      if (options.sendFrameUrl) {\n        frame.url = stackFrame.url;\n      }\n      if (\n        frame.method &&\n        frame.method.endsWith &&\n        frame.method.endsWith('_rollbar_wrapped')\n      ) {\n        continue;\n      }\n\n      code = pre = post = null;\n      contextLength = stackFrame.context ? stackFrame.context.length : 0;\n      if (contextLength) {\n        mid = Math.floor(contextLength / 2);\n        pre = stackFrame.context.slice(0, mid);\n        code = stackFrame.context[mid];\n        post = stackFrame.context.slice(mid);\n      }\n\n      if (code) {\n        frame.code = code;\n      }\n\n      if (pre || post) {\n        frame.context = {};\n        if (pre && pre.length) {\n          frame.context.pre = pre;\n        }\n        if (post && post.length) {\n          frame.context.post = post;\n        }\n      }\n\n      if (stackFrame.args) {\n        frame.args = stackFrame.args;\n      }\n\n      trace.frames.push(frame);\n    }\n\n    // NOTE(cory): reverse the frames since rollbar.com expects the most recent call last\n    trace.frames.reverse();\n\n    if (custom) {\n      trace.extra = _.merge(custom);\n    }\n  }\n\n  return trace;\n}\n\nfunction errorClass(stackInfo, guess, options) {\n  if (stackInfo.name) {\n    return stackInfo.name;\n  } else if (options.guessErrorClass) {\n    return guess;\n  } else {\n    return '(unknown)';\n  }\n}\n\nfunction addScrubber(scrubFn) {\n  return function (item, options, callback) {\n    if (scrubFn) {\n      var scrubFields = options.scrubFields || [];\n      var scrubPaths = options.scrubPaths || [];\n      item.data = scrubFn(item.data, scrubFields, scrubPaths);\n    }\n    callback(null, item);\n  };\n}\n\nmodule.exports = {\n  handleDomException: handleDomException,\n  handleItemWithError: handleItemWithError,\n  ensureItemHasSomethingToSay: ensureItemHasSomethingToSay,\n  addBaseInfo: addBaseInfo,\n  addRequestInfo: addRequestInfo,\n  addClientInfo: addClientInfo,\n  addPluginInfo: addPluginInfo,\n  addBody: addBody,\n  addScrubber: addScrubber,\n};\n", "'use strict';\n\nvar _ = require('../utility');\nvar makeFetchRequest = require('./transport/fetch');\nvar makeXhrRequest = require('./transport/xhr');\n\n/*\n * accessToken may be embedded in payload but that should not\n *   be assumed\n *\n * options: {\n *   hostname\n *   protocol\n *   path\n *   port\n *   method\n *   transport ('xhr' | 'fetch')\n * }\n *\n *  params is an object containing key/value pairs. These\n *    will be appended to the path as 'key=value&key=value'\n *\n * payload is an unserialized object\n */\nfunction Transport(truncation) {\n  this.truncation = truncation;\n}\n\nTransport.prototype.get = function (\n  accessToken,\n  options,\n  params,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n  _.addParamsAndAccessTokenToPath(accessToken, options, params);\n\n  var method = 'GET';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    null,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.post = function (\n  accessToken,\n  options,\n  payload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!payload) {\n    return callback(new Error('Cannot send empty request'));\n  }\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n  if (stringifyResult.error) {\n    return callback(stringifyResult.error);\n  }\n\n  var writeData = stringifyResult.value;\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    writeData,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.postJsonPayload = function (\n  accessToken,\n  options,\n  jsonPayload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    jsonPayload,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\n// Wraps _makeRequest and if Angular 2+ Zone.js is detected, changes scope\n// so Angular change detection isn't triggered on each API call.\n// This is the equivalent of runOutsideAngular().\n//\nTransport.prototype._makeZoneRequest = function () {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var currentZone = gWindow && gWindow.Zone && gWindow.Zone.current;\n  var args = Array.prototype.slice.call(arguments);\n\n  if (currentZone && currentZone._name === 'angular') {\n    var rootZone = currentZone._parent;\n    var self = this;\n    rootZone.run(function () {\n      self._makeRequest.apply(undefined, args);\n    });\n  } else {\n    this._makeRequest.apply(undefined, args);\n  }\n};\n\nTransport.prototype._makeRequest = function (\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n  transport,\n) {\n  if (typeof RollbarProxy !== 'undefined') {\n    return _proxyRequest(data, callback);\n  }\n\n  if (transport === 'fetch') {\n    makeFetchRequest(accessToken, url, method, data, callback, timeout);\n  } else {\n    makeXhrRequest(\n      accessToken,\n      url,\n      method,\n      data,\n      callback,\n      requestFactory,\n      timeout,\n    );\n  }\n};\n\n/* global RollbarProxy */\nfunction _proxyRequest(json, callback) {\n  var rollbarProxy = new RollbarProxy();\n  rollbarProxy.sendJsonPayload(\n    json,\n    function (_msg) {\n      /* do nothing */\n    }, // eslint-disable-line no-unused-vars\n    function (err) {\n      callback(new Error(err));\n    },\n  );\n}\n\nmodule.exports = Transport;\n", "'use strict';\n\nvar logger = require('../logger');\nvar _ = require('../../utility');\n\nfunction makeFetchRequest(accessToken, url, method, data, callback, timeout) {\n  var controller;\n  var timeoutId;\n\n  if (_.isFiniteNumber(timeout)) {\n    controller = new AbortController();\n    timeoutId = setTimeout(function () {\n      controller.abort();\n    }, timeout);\n  }\n\n  fetch(url, {\n    method: method,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Rollbar-Access-Token': accessToken,\n      signal: controller && controller.signal,\n    },\n    body: data,\n  })\n    .then(function (response) {\n      if (timeoutId) clearTimeout(timeoutId);\n      return response.json();\n    })\n    .then(function (data) {\n      callback(null, data);\n    })\n    .catch(function (error) {\n      logger.error(error.message);\n      callback(error);\n    });\n}\n\nmodule.exports = makeFetchRequest;\n", "'use strict';\n\n/*global XDomainRequest*/\n\nvar _ = require('../../utility');\nvar logger = require('../logger');\n\nfunction makeXhrRequest(\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n) {\n  var request;\n  if (requestFactory) {\n    request = requestFactory();\n  } else {\n    request = _createXMLHTTPObject();\n  }\n  if (!request) {\n    // Give up, no way to send requests\n    return callback(new Error('No way to send a request'));\n  }\n  try {\n    try {\n      var onreadystatechange = function () {\n        try {\n          if (onreadystatechange && request.readyState === 4) {\n            onreadystatechange = undefined;\n\n            var parseResponse = _.jsonParse(request.responseText);\n            if (_isSuccess(request)) {\n              callback(parseResponse.error, parseResponse.value);\n              return;\n            } else if (_isNormalFailure(request)) {\n              if (request.status === 403) {\n                // likely caused by using a server access token\n                var message =\n                  parseResponse.value && parseResponse.value.message;\n                logger.error(message);\n              }\n              // return valid http status codes\n              callback(new Error(String(request.status)));\n            } else {\n              // IE will return a status 12000+ on some sort of connection failure,\n              // so we return a blank error\n              // http://msdn.microsoft.com/en-us/library/aa383770%28VS.85%29.aspx\n              var msg =\n                'XHR response had no status code (likely connection failure)';\n              callback(_newRetriableError(msg));\n            }\n          }\n        } catch (ex) {\n          //jquery source mentions firefox may error out while accessing the\n          //request members if there is a network error\n          //https://github.com/jquery/jquery/blob/a938d7b1282fc0e5c52502c225ae8f0cef219f0a/src/ajax/xhr.js#L111\n          var exc;\n          if (ex && ex.stack) {\n            exc = ex;\n          } else {\n            exc = new Error(ex);\n          }\n          callback(exc);\n        }\n      };\n\n      request.open(method, url, true);\n      if (request.setRequestHeader) {\n        request.setRequestHeader('Content-Type', 'application/json');\n        request.setRequestHeader('X-Rollbar-Access-Token', accessToken);\n      }\n\n      if (_.isFiniteNumber(timeout)) {\n        request.timeout = timeout;\n      }\n\n      request.onreadystatechange = onreadystatechange;\n      request.send(data);\n    } catch (e1) {\n      // Sending using the normal xmlhttprequest object didn't work, try XDomainRequest\n      if (typeof XDomainRequest !== 'undefined') {\n        // Assume we are in a really old browser which has a bunch of limitations:\n        // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\n        // Extreme paranoia: if we have XDomainRequest then we have a window, but just in case\n        if (!window || !window.location) {\n          return callback(\n            new Error(\n              'No window available during request, unknown environment',\n            ),\n          );\n        }\n\n        // If the current page is http, try and send over http\n        if (\n          window.location.href.substring(0, 5) === 'http:' &&\n          url.substring(0, 5) === 'https'\n        ) {\n          url = 'http' + url.substring(5);\n        }\n\n        var xdomainrequest = new XDomainRequest();\n        xdomainrequest.onprogress = function () {};\n        xdomainrequest.ontimeout = function () {\n          var msg = 'Request timed out';\n          var code = 'ETIMEDOUT';\n          callback(_newRetriableError(msg, code));\n        };\n        xdomainrequest.onerror = function () {\n          callback(new Error('Error during request'));\n        };\n        xdomainrequest.onload = function () {\n          var parseResponse = _.jsonParse(xdomainrequest.responseText);\n          callback(parseResponse.error, parseResponse.value);\n        };\n        xdomainrequest.open(method, url, true);\n        xdomainrequest.send(data);\n      } else {\n        callback(new Error('Cannot find a method to transport a request'));\n      }\n    }\n  } catch (e2) {\n    callback(e2);\n  }\n}\n\nfunction _createXMLHTTPObject() {\n  /* global ActiveXObject:false */\n\n  var factories = [\n    function () {\n      return new XMLHttpRequest();\n    },\n    function () {\n      return new ActiveXObject('Msxml2.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Msxml3.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Microsoft.XMLHTTP');\n    },\n  ];\n  var xmlhttp;\n  var i;\n  var numFactories = factories.length;\n  for (i = 0; i < numFactories; i++) {\n    /* eslint-disable no-empty */\n    try {\n      xmlhttp = factories[i]();\n      break;\n    } catch (e) {\n      // pass\n    }\n    /* eslint-enable no-empty */\n  }\n  return xmlhttp;\n}\n\nfunction _isSuccess(r) {\n  return r && r.status && r.status === 200;\n}\n\nfunction _isNormalFailure(r) {\n  return r && _.isType(r.status, 'number') && r.status >= 400 && r.status < 600;\n}\n\nfunction _newRetriableError(message, code) {\n  var err = new Error(message);\n  err.code = code || 'ENOTFOUND';\n  return err;\n}\n\nmodule.exports = makeXhrRequest;\n", "'use strict';\n\n// See https://nodejs.org/docs/latest/api/url.html\nfunction parse(url) {\n  var result = {\n    protocol: null,\n    auth: null,\n    host: null,\n    path: null,\n    hash: null,\n    href: url,\n    hostname: null,\n    port: null,\n    pathname: null,\n    search: null,\n    query: null,\n  };\n\n  var i, last;\n  i = url.indexOf('//');\n  if (i !== -1) {\n    result.protocol = url.substring(0, i);\n    last = i + 2;\n  } else {\n    last = 0;\n  }\n\n  i = url.indexOf('@', last);\n  if (i !== -1) {\n    result.auth = url.substring(last, i);\n    last = i + 1;\n  }\n\n  i = url.indexOf('/', last);\n  if (i === -1) {\n    i = url.indexOf('?', last);\n    if (i === -1) {\n      i = url.indexOf('#', last);\n      if (i === -1) {\n        result.host = url.substring(last);\n      } else {\n        result.host = url.substring(last, i);\n        result.hash = url.substring(i);\n      }\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      return result;\n    } else {\n      result.host = url.substring(last, i);\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      last = i;\n    }\n  } else {\n    result.host = url.substring(last, i);\n    result.hostname = result.host.split(':')[0];\n    result.port = result.host.split(':')[1];\n    if (result.port) {\n      result.port = parseInt(result.port, 10);\n    }\n    last = i;\n  }\n\n  i = url.indexOf('#', last);\n  if (i === -1) {\n    result.path = url.substring(last);\n  } else {\n    result.path = url.substring(last, i);\n    result.hash = url.substring(i);\n  }\n\n  if (result.path) {\n    var pathParts = result.path.split('?');\n    result.pathname = pathParts[0];\n    result.query = pathParts[1];\n    result.search = result.query ? '?' + result.query : null;\n  }\n  return result;\n}\n\nmodule.exports = {\n  parse: parse,\n};\n", "'use strict';\n\nfunction wrapGlobals(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  // Adapted from https://github.com/bugsnag/bugsnag-js\n  var globals =\n    'EventTarget,Window,Node,ApplicationCache,AudioTrackList,ChannelMergerNode,CryptoOperation,EventSource,FileReader,HTMLUnknownElement,IDBDatabase,IDBRequest,IDBTransaction,KeyOperation,MediaController,MessagePort,ModalWindow,Notification,SVGElementInstance,Screen,TextTrack,TextTrackCue,TextTrackList,WebSocket,WebSocketWorker,Worker,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload'.split(\n      ',',\n    );\n  var i, global;\n  for (i = 0; i < globals.length; ++i) {\n    global = globals[i];\n\n    if (window[global] && window[global].prototype) {\n      _extendListenerPrototype(handler, window[global].prototype, shim);\n    }\n  }\n}\n\nfunction _extendListenerPrototype(handler, prototype, shim) {\n  if (\n    prototype.hasOwnProperty &&\n    prototype.hasOwnProperty('addEventListener')\n  ) {\n    var oldAddEventListener = prototype.addEventListener;\n    while (\n      oldAddEventListener._rollbarOldAdd &&\n      oldAddEventListener.belongsToShim\n    ) {\n      oldAddEventListener = oldAddEventListener._rollbarOldAdd;\n    }\n    var addFn = function (event, callback, bubble) {\n      oldAddEventListener.call(this, event, handler.wrap(callback), bubble);\n    };\n    addFn._rollbarOldAdd = oldAddEventListener;\n    addFn.belongsToShim = shim;\n    prototype.addEventListener = addFn;\n\n    var oldRemoveEventListener = prototype.removeEventListener;\n    while (\n      oldRemoveEventListener._rollbarOldRemove &&\n      oldRemoveEventListener.belongsToShim\n    ) {\n      oldRemoveEventListener = oldRemoveEventListener._rollbarOldRemove;\n    }\n    var removeFn = function (event, callback, bubble) {\n      oldRemoveEventListener.call(\n        this,\n        event,\n        (callback && callback._rollbar_wrapped) || callback,\n        bubble,\n      );\n    };\n    removeFn._rollbarOldRemove = oldRemoveEventListener;\n    removeFn.belongsToShim = shim;\n    prototype.removeEventListener = removeFn;\n  }\n}\n\nmodule.exports = wrapGlobals;\n", "'use strict';\n\nmodule.exports = {\n  version: '2.26.4',\n  endpoint: 'api.rollbar.com/api/1/item/',\n  logLevel: 'debug',\n  reportLevel: 'debug',\n  uncaughtErrorLevel: 'error',\n  maxItems: 0,\n  itemsPerMin: 60,\n};\n", "'use strict';\n\nvar ErrorStackParser = require('error-stack-parser');\n\nvar UNKNOWN_FUNCTION = '?';\nvar ERR_CLASS_REGEXP = new RegExp(\n  '^(([a-zA-Z0-9-_$ ]*): *)?(Uncaught )?([a-zA-Z0-9-_$ ]*): ',\n);\n\nfunction guessFunctionName() {\n  return UNKNOWN_FUNCTION;\n}\n\nfunction gatherContext() {\n  return null;\n}\n\nfunction Frame(stackFrame) {\n  var data = {};\n\n  data._stackFrame = stackFrame;\n\n  data.url = stackFrame.fileName;\n  data.line = stackFrame.lineNumber;\n  data.func = stackFrame.functionName;\n  data.column = stackFrame.columnNumber;\n  data.args = stackFrame.args;\n\n  data.context = gatherContext();\n\n  return data;\n}\n\nfunction Stack(exception, skip) {\n  function getStack() {\n    var parserStack = [];\n\n    skip = skip || 0;\n\n    try {\n      parserStack = ErrorStackParser.parse(exception);\n    } catch (e) {\n      parserStack = [];\n    }\n\n    var stack = [];\n\n    for (var i = skip; i < parserStack.length; i++) {\n      stack.push(new Frame(parserStack[i]));\n    }\n\n    return stack;\n  }\n\n  return {\n    stack: getStack(),\n    message: exception.message,\n    name: _mostSpecificErrorName(exception),\n    rawStack: exception.stack,\n    rawException: exception,\n  };\n}\n\nfunction parse(e, skip) {\n  var err = e;\n\n  if (err.nested || err.cause) {\n    var traceChain = [];\n    while (err) {\n      traceChain.push(new Stack(err, skip));\n      err = err.nested || err.cause;\n\n      skip = 0; // Only apply skip value to primary error\n    }\n\n    // Return primary error with full trace chain attached.\n    traceChain[0].traceChain = traceChain;\n    return traceChain[0];\n  } else {\n    return new Stack(err, skip);\n  }\n}\n\nfunction guessErrorClass(errMsg) {\n  if (!errMsg || !errMsg.match) {\n    return ['Unknown error. There was no error message to display.', ''];\n  }\n  var errClassMatch = errMsg.match(ERR_CLASS_REGEXP);\n  var errClass = '(unknown)';\n\n  if (errClassMatch) {\n    errClass = errClassMatch[errClassMatch.length - 1];\n    errMsg = errMsg.replace(\n      (errClassMatch[errClassMatch.length - 2] || '') + errClass + ':',\n      '',\n    );\n    errMsg = errMsg.replace(/(^[\\s]+|[\\s]+$)/g, '');\n  }\n  return [errClass, errMsg];\n}\n\n// * Prefers any value over an empty string\n// * Prefers any value over 'Error' where possible\n// * Prefers name over constructor.name when both are more specific than 'Error'\nfunction _mostSpecificErrorName(error) {\n  var name = error.name && error.name.length && error.name;\n  var constructorName =\n    error.constructor.name &&\n    error.constructor.name.length &&\n    error.constructor.name;\n\n  if (!name || !constructorName) {\n    return name || constructorName;\n  }\n\n  if (name === 'Error') {\n    return constructorName;\n  }\n  return name;\n}\n\nmodule.exports = {\n  guessFunctionName: guessFunctionName,\n  guessErrorClass: guessErrorClass,\n  gatherContext: gatherContext,\n  parse: parse,\n  Stack: Stack,\n  Frame: Frame,\n};\n", "'use strict';\n\n'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\n\nvar isPlainObject = function isPlainObject(obj) {\n  if (!obj || toStr.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n  var hasIsPrototypeOf =\n    obj.constructor &&\n    obj.constructor.prototype &&\n    hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n  // Not own constructor property must be Object\n  if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n    return false;\n  }\n\n  // Own properties are enumerated firstly, so to speed up,\n  // if last one is own, then all properties are own.\n  var key;\n  for (key in obj) {\n    /**/\n  }\n\n  return typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\nfunction merge() {\n  var i,\n    src,\n    copy,\n    clone,\n    name,\n    result = {},\n    current = null,\n    length = arguments.length;\n\n  for (i = 0; i < length; i++) {\n    current = arguments[i];\n    if (current == null) {\n      continue;\n    }\n\n    for (name in current) {\n      src = result[name];\n      copy = current[name];\n      if (result !== copy) {\n        if (copy && isPlainObject(copy)) {\n          clone = src && isPlainObject(src) ? src : {};\n          result[name] = merge(clone, copy);\n        } else if (typeof copy !== 'undefined') {\n          result[name] = copy;\n        }\n      }\n    }\n  }\n  return result;\n}\n\nmodule.exports = merge;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Notifier - the internal object responsible for delegating between the client exposed API, the\n * chain of transforms necessary to turn an item into something that can be sent to Rollbar, and the\n * queue which handles the communcation with the Rollbar API servers.\n *\n * @param queue - an object that conforms to the interface: addItem(item, callback)\n * @param options - an object representing the options to be set for this notifier, this should have\n * any defaults already set by the caller\n */\nfunction Notifier(queue, options) {\n  this.queue = queue;\n  this.options = options;\n  this.transforms = [];\n  this.diagnostic = {};\n}\n\n/*\n * configure - updates the options for this notifier with the passed in object\n *\n * @param options - an object which gets merged with the current options set on this notifier\n * @returns this\n */\nNotifier.prototype.configure = function (options) {\n  this.queue && this.queue.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addTransform - adds a transform onto the end of the queue of transforms for this notifier\n *\n * @param transform - a function which takes three arguments:\n *    * item: An Object representing the data to eventually be sent to Rollbar\n *    * options: The current value of the options for this notifier\n *    * callback: function(err: (Null|Error), item: (Null|Object)) the transform must call this\n *    callback with a null value for error if it wants the processing chain to continue, otherwise\n *    with an error to terminate the processing. The item should be the updated item after this\n *    transform is finished modifying it.\n */\nNotifier.prototype.addTransform = function (transform) {\n  if (_.isFunction(transform)) {\n    this.transforms.push(transform);\n  }\n  return this;\n};\n\n/*\n * log - the internal log function which applies the configured transforms and then pushes onto the\n * queue to be sent to the backend.\n *\n * @param item - An object with the following structure:\n *    message [String] - An optional string to be sent to rollbar\n *    error [Error] - An optional error\n *\n * @param callback - A function of type function(err, resp) which will be called with exactly one\n * null argument and one non-null argument. The callback will be called once, either during the\n * transform stage if an error occurs inside a transform, or in response to the communication with\n * the backend. The second argument will be the response from the backend in case of success.\n */\nNotifier.prototype.log = function (item, callback) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!this.options.enabled) {\n    return callback(new Error('Rollbar is not enabled'));\n  }\n\n  this.queue.addPendingItem(item);\n  var originalError = item.err;\n  this._applyTransforms(\n    item,\n    function (err, i) {\n      if (err) {\n        this.queue.removePendingItem(item);\n        return callback(err, null);\n      }\n      this.queue.addItem(i, callback, originalError, item);\n    }.bind(this),\n  );\n};\n\n/* Internal */\n\n/*\n * _applyTransforms - Applies the transforms that have been added to this notifier sequentially. See\n * `addTransform` for more information.\n *\n * @param item - An item to be transformed\n * @param callback - A function of type function(err, item) which will be called with a non-null\n * error and a null item in the case of a transform failure, or a null error and non-null item after\n * all transforms have been applied.\n */\nNotifier.prototype._applyTransforms = function (item, callback) {\n  var transformIndex = -1;\n  var transformsLength = this.transforms.length;\n  var transforms = this.transforms;\n  var options = this.options;\n\n  var cb = function (err, i) {\n    if (err) {\n      callback(err, null);\n      return;\n    }\n\n    transformIndex++;\n\n    if (transformIndex === transformsLength) {\n      callback(null, i);\n      return;\n    }\n\n    transforms[transformIndex](i, options, cb);\n  };\n\n  cb(null, item);\n};\n\nmodule.exports = Notifier;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction checkLevel(item, settings) {\n  var level = item.level;\n  var levelVal = _.LEVELS[level] || 0;\n  var reportLevel = settings.reportLevel;\n  var reportLevelVal = _.LEVELS[reportLevel] || 0;\n\n  if (levelVal < reportLevelVal) {\n    return false;\n  }\n  return true;\n}\n\nfunction userCheckIgnore(logger) {\n  return function (item, settings) {\n    var isUncaught = !!item._isUncaught;\n    delete item._isUncaught;\n    var args = item._originalArgs;\n    delete item._originalArgs;\n    try {\n      if (_.isFunction(settings.onSendCallback)) {\n        settings.onSendCallback(isUncaught, args, item);\n      }\n    } catch (e) {\n      settings.onSendCallback = null;\n      logger.error('Error while calling onSendCallback, removing', e);\n    }\n    try {\n      if (\n        _.isFunction(settings.checkIgnore) &&\n        settings.checkIgnore(isUncaught, args, item)\n      ) {\n        return false;\n      }\n    } catch (e) {\n      settings.checkIgnore = null;\n      logger.error('Error while calling custom checkIgnore(), removing', e);\n    }\n    return true;\n  };\n}\n\nfunction urlIsNotBlockListed(logger) {\n  return function (item, settings) {\n    return !urlIsOnAList(item, settings, 'blocklist', logger);\n  };\n}\n\nfunction urlIsSafeListed(logger) {\n  return function (item, settings) {\n    return urlIsOnAList(item, settings, 'safelist', logger);\n  };\n}\n\nfunction matchFrames(trace, list, block) {\n  if (!trace) {\n    return !block;\n  }\n\n  var frames = trace.frames;\n\n  if (!frames || frames.length === 0) {\n    return !block;\n  }\n\n  var frame, filename, url, urlRegex;\n  var listLength = list.length;\n  var frameLength = frames.length;\n  for (var i = 0; i < frameLength; i++) {\n    frame = frames[i];\n    filename = frame.filename;\n\n    if (!_.isType(filename, 'string')) {\n      return !block;\n    }\n\n    for (var j = 0; j < listLength; j++) {\n      url = list[j];\n      urlRegex = new RegExp(url);\n\n      if (urlRegex.test(filename)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction urlIsOnAList(item, settings, safeOrBlock, logger) {\n  // safelist is the default\n  var block = false;\n  if (safeOrBlock === 'blocklist') {\n    block = true;\n  }\n\n  var list, traces;\n  try {\n    list = block ? settings.hostBlockList : settings.hostSafeList;\n    traces = _.get(item, 'body.trace_chain') || [_.get(item, 'body.trace')];\n\n    // These two checks are important to come first as they are defaults\n    // in case the list is missing or the trace is missing or not well-formed\n    if (!list || list.length === 0) {\n      return !block;\n    }\n    if (traces.length === 0 || !traces[0]) {\n      return !block;\n    }\n\n    var tracesLength = traces.length;\n    for (var i = 0; i < tracesLength; i++) {\n      if (matchFrames(traces[i], list, block)) {\n        return true;\n      }\n    }\n  } catch (\n    e\n    /* istanbul ignore next */\n  ) {\n    if (block) {\n      settings.hostBlockList = null;\n    } else {\n      settings.hostSafeList = null;\n    }\n    var listName = block ? 'hostBlockList' : 'hostSafeList';\n    logger.error(\n      \"Error while reading your configuration's \" +\n        listName +\n        ' option. Removing custom ' +\n        listName +\n        '.',\n      e,\n    );\n    return !block;\n  }\n  return false;\n}\n\nfunction messageIsIgnored(logger) {\n  return function (item, settings) {\n    var i, j, ignoredMessages, len, messageIsIgnored, rIgnoredMessage, messages;\n\n    try {\n      messageIsIgnored = false;\n      ignoredMessages = settings.ignoredMessages;\n\n      if (!ignoredMessages || ignoredMessages.length === 0) {\n        return true;\n      }\n\n      messages = messagesFromItem(item);\n\n      if (messages.length === 0) {\n        return true;\n      }\n\n      len = ignoredMessages.length;\n      for (i = 0; i < len; i++) {\n        rIgnoredMessage = new RegExp(ignoredMessages[i], 'gi');\n\n        for (j = 0; j < messages.length; j++) {\n          messageIsIgnored = rIgnoredMessage.test(messages[j]);\n\n          if (messageIsIgnored) {\n            return false;\n          }\n        }\n      }\n    } catch (\n      e\n      /* istanbul ignore next */\n    ) {\n      settings.ignoredMessages = null;\n      logger.error(\n        \"Error while reading your configuration's ignoredMessages option. Removing custom ignoredMessages.\",\n      );\n    }\n\n    return true;\n  };\n}\n\nfunction messagesFromItem(item) {\n  var body = item.body;\n  var messages = [];\n\n  // The payload schema only allows one of trace_chain, message, or trace.\n  // However, existing test cases are based on having both trace and message present.\n  // So here we preserve the ability to collect strings from any combination of these keys.\n  if (body.trace_chain) {\n    var traceChain = body.trace_chain;\n    for (var i = 0; i < traceChain.length; i++) {\n      var trace = traceChain[i];\n      messages.push(_.get(trace, 'exception.message'));\n    }\n  }\n  if (body.trace) {\n    messages.push(_.get(body, 'trace.exception.message'));\n  }\n  if (body.message) {\n    messages.push(_.get(body, 'message.body'));\n  }\n  return messages;\n}\n\nmodule.exports = {\n  checkLevel: checkLevel,\n  userCheckIgnore: userCheckIgnore,\n  urlIsNotBlockListed: urlIsNotBlockListed,\n  urlIsSafeListed: urlIsSafeListed,\n  messageIsIgnored: messageIsIgnored,\n};\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Queue - an object which handles which handles a queue of items to be sent to Rollbar.\n *   This object handles rate limiting via a passed in rate limiter, retries based on connection\n *   errors, and filtering of items based on a set of configurable predicates. The communication to\n *   the backend is performed via a given API object.\n *\n * @param rateLimiter - An object which conforms to the interface\n *    rateLimiter.shouldSend(item) -> bool\n * @param api - An object which conforms to the interface\n *    api.postItem(payload, function(err, response))\n * @param logger - An object used to log verbose messages if desired\n * @param options - see Queue.prototype.configure\n */\nfunction Queue(rateLimiter, api, logger, options) {\n  this.rateLimiter = rateLimiter;\n  this.api = api;\n  this.logger = logger;\n  this.options = options;\n  this.predicates = [];\n  this.pendingItems = [];\n  this.pendingRequests = [];\n  this.retryQueue = [];\n  this.retryHandle = null;\n  this.waitCallback = null;\n  this.waitIntervalID = null;\n}\n\n/*\n * configure - updates the options this queue uses\n *\n * @param options\n */\nQueue.prototype.configure = function (options) {\n  this.api && this.api.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addPredicate - adds a predicate to the end of the list of predicates for this queue\n *\n * @param predicate - function(item, options) -> (bool|{err: Error})\n *  Returning true means that this predicate passes and the item is okay to go on the queue\n *  Returning false means do not add the item to the queue, but it is not an error\n *  Returning {err: Error} means do not add the item to the queue, and the given error explains why\n *  Returning {err: undefined} is equivalent to returning true but don't do that\n */\nQueue.prototype.addPredicate = function (predicate) {\n  if (_.isFunction(predicate)) {\n    this.predicates.push(predicate);\n  }\n  return this;\n};\n\nQueue.prototype.addPendingItem = function (item) {\n  this.pendingItems.push(item);\n};\n\nQueue.prototype.removePendingItem = function (item) {\n  var idx = this.pendingItems.indexOf(item);\n  if (idx !== -1) {\n    this.pendingItems.splice(idx, 1);\n  }\n};\n\n/*\n * addItem - Send an item to the Rollbar API if all of the predicates are satisfied\n *\n * @param item - The payload to send to the backend\n * @param callback - function(error, repsonse) which will be called with the response from the API\n *  in the case of a success, otherwise response will be null and error will have a value. If both\n *  error and response are null then the item was stopped by a predicate which did not consider this\n *  to be an error condition, but nonetheless did not send the item to the API.\n *  @param originalError - The original error before any transformations that is to be logged if any\n */\nQueue.prototype.addItem = function (\n  item,\n  callback,\n  originalError,\n  originalItem,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {\n      return;\n    };\n  }\n  var predicateResult = this._applyPredicates(item);\n  if (predicateResult.stop) {\n    this.removePendingItem(originalItem);\n    callback(predicateResult.err);\n    return;\n  }\n  this._maybeLog(item, originalError);\n  this.removePendingItem(originalItem);\n  if (!this.options.transmit) {\n    callback(new Error('Transmit disabled'));\n    return;\n  }\n  this.pendingRequests.push(item);\n  try {\n    this._makeApiRequest(\n      item,\n      function (err, resp) {\n        this._dequeuePendingRequest(item);\n        callback(err, resp);\n      }.bind(this),\n    );\n  } catch (e) {\n    this._dequeuePendingRequest(item);\n    callback(e);\n  }\n};\n\n/*\n * wait - Stop any further errors from being added to the queue, and get called back when all items\n *   currently processing have finished sending to the backend.\n *\n * @param callback - function() called when all pending items have been sent\n */\nQueue.prototype.wait = function (callback) {\n  if (!_.isFunction(callback)) {\n    return;\n  }\n  this.waitCallback = callback;\n  if (this._maybeCallWait()) {\n    return;\n  }\n  if (this.waitIntervalID) {\n    this.waitIntervalID = clearInterval(this.waitIntervalID);\n  }\n  this.waitIntervalID = setInterval(\n    function () {\n      this._maybeCallWait();\n    }.bind(this),\n    500,\n  );\n};\n\n/* _applyPredicates - Sequentially applies the predicates that have been added to the queue to the\n *   given item with the currently configured options.\n *\n * @param item - An item in the queue\n * @returns {stop: bool, err: (Error|null)} - stop being true means do not add item to the queue,\n *   the error value should be passed up to a callbak if we are stopping.\n */\nQueue.prototype._applyPredicates = function (item) {\n  var p = null;\n  for (var i = 0, len = this.predicates.length; i < len; i++) {\n    p = this.predicates[i](item, this.options);\n    if (!p || p.err !== undefined) {\n      return { stop: true, err: p.err };\n    }\n  }\n  return { stop: false, err: null };\n};\n\n/*\n * _makeApiRequest - Send an item to Rollbar, callback when done, if there is an error make an\n *   effort to retry if we are configured to do so.\n *\n * @param item - an item ready to send to the backend\n * @param callback - function(err, response)\n */\nQueue.prototype._makeApiRequest = function (item, callback) {\n  var rateLimitResponse = this.rateLimiter.shouldSend(item);\n  if (rateLimitResponse.shouldSend) {\n    this.api.postItem(\n      item,\n      function (err, resp) {\n        if (err) {\n          this._maybeRetry(err, item, callback);\n        } else {\n          callback(err, resp);\n        }\n      }.bind(this),\n    );\n  } else if (rateLimitResponse.error) {\n    callback(rateLimitResponse.error);\n  } else {\n    this.api.postItem(rateLimitResponse.payload, callback);\n  }\n};\n\n// These are errors basically mean there is no internet connection\nvar RETRIABLE_ERRORS = [\n  'ECONNRESET',\n  'ENOTFOUND',\n  'ESOCKETTIMEDOUT',\n  'ETIMEDOUT',\n  'ECONNREFUSED',\n  'EHOSTUNREACH',\n  'EPIPE',\n  'EAI_AGAIN',\n];\n\n/*\n * _maybeRetry - Given the error returned by the API, decide if we should retry or just callback\n *   with the error.\n *\n * @param err - an error returned by the API transport\n * @param item - the item that was trying to be sent when this error occured\n * @param callback - function(err, response)\n */\nQueue.prototype._maybeRetry = function (err, item, callback) {\n  var shouldRetry = false;\n  if (this.options.retryInterval) {\n    for (var i = 0, len = RETRIABLE_ERRORS.length; i < len; i++) {\n      if (err.code === RETRIABLE_ERRORS[i]) {\n        shouldRetry = true;\n        break;\n      }\n    }\n    if (shouldRetry && _.isFiniteNumber(this.options.maxRetries)) {\n      item.retries = item.retries ? item.retries + 1 : 1;\n      if (item.retries > this.options.maxRetries) {\n        shouldRetry = false;\n      }\n    }\n  }\n  if (shouldRetry) {\n    this._retryApiRequest(item, callback);\n  } else {\n    callback(err);\n  }\n};\n\n/*\n * _retryApiRequest - Add an item and a callback to a queue and possibly start a timer to process\n *   that queue based on the retryInterval in the options for this queue.\n *\n * @param item - an item that failed to send due to an error we deem retriable\n * @param callback - function(err, response)\n */\nQueue.prototype._retryApiRequest = function (item, callback) {\n  this.retryQueue.push({ item: item, callback: callback });\n\n  if (!this.retryHandle) {\n    this.retryHandle = setInterval(\n      function () {\n        while (this.retryQueue.length) {\n          var retryObject = this.retryQueue.shift();\n          this._makeApiRequest(retryObject.item, retryObject.callback);\n        }\n      }.bind(this),\n      this.options.retryInterval,\n    );\n  }\n};\n\n/*\n * _dequeuePendingRequest - Removes the item from the pending request queue, this queue is used to\n *   enable to functionality of providing a callback that clients can pass to `wait` to be notified\n *   when the pending request queue has been emptied. This must be called when the API finishes\n *   processing this item. If a `wait` callback is configured, it is called by this function.\n *\n * @param item - the item previously added to the pending request queue\n */\nQueue.prototype._dequeuePendingRequest = function (item) {\n  var idx = this.pendingRequests.indexOf(item);\n  if (idx !== -1) {\n    this.pendingRequests.splice(idx, 1);\n    this._maybeCallWait();\n  }\n};\n\nQueue.prototype._maybeLog = function (data, originalError) {\n  if (this.logger && this.options.verbose) {\n    var message = originalError;\n    message = message || _.get(data, 'body.trace.exception.message');\n    message = message || _.get(data, 'body.trace_chain.0.exception.message');\n    if (message) {\n      this.logger.error(message);\n      return;\n    }\n    message = _.get(data, 'body.message.body');\n    if (message) {\n      this.logger.log(message);\n    }\n  }\n};\n\nQueue.prototype._maybeCallWait = function () {\n  if (\n    _.isFunction(this.waitCallback) &&\n    this.pendingItems.length === 0 &&\n    this.pendingRequests.length === 0\n  ) {\n    if (this.waitIntervalID) {\n      this.waitIntervalID = clearInterval(this.waitIntervalID);\n    }\n    this.waitCallback();\n    return true;\n  }\n  return false;\n};\n\nmodule.exports = Queue;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * RateLimiter - an object that encapsulates the logic for counting items sent to Rollbar\n *\n * @param options - the same options that are accepted by configureGlobal offered as a convenience\n */\nfunction RateLimiter(options) {\n  this.startTime = _.now();\n  this.counter = 0;\n  this.perMinCounter = 0;\n  this.platform = null;\n  this.platformOptions = {};\n  this.configureGlobal(options);\n}\n\nRateLimiter.globalSettings = {\n  startTime: _.now(),\n  maxItems: undefined,\n  itemsPerMinute: undefined,\n};\n\n/*\n * configureGlobal - set the global rate limiter options\n *\n * @param options - Only the following values are recognized:\n *    startTime: a timestamp of the form returned by (new Date()).getTime()\n *    maxItems: the maximum items\n *    itemsPerMinute: the max number of items to send in a given minute\n */\nRateLimiter.prototype.configureGlobal = function (options) {\n  if (options.startTime !== undefined) {\n    RateLimiter.globalSettings.startTime = options.startTime;\n  }\n  if (options.maxItems !== undefined) {\n    RateLimiter.globalSettings.maxItems = options.maxItems;\n  }\n  if (options.itemsPerMinute !== undefined) {\n    RateLimiter.globalSettings.itemsPerMinute = options.itemsPerMinute;\n  }\n};\n\n/*\n * shouldSend - determine if we should send a given item based on rate limit settings\n *\n * @param item - the item we are about to send\n * @returns An object with the following structure:\n *  error: (Error|null)\n *  shouldSend: bool\n *  payload: (Object|null)\n *  If shouldSend is false, the item passed as a parameter should not be sent to Rollbar, and\n *  exactly one of error or payload will be non-null. If error is non-null, the returned Error will\n *  describe the situation, but it means that we were already over a rate limit (either globally or\n *  per minute) when this item was checked. If error is null, and therefore payload is non-null, it\n *  means this item put us over the global rate limit and the payload should be sent to Rollbar in\n *  place of the passed in item.\n */\nRateLimiter.prototype.shouldSend = function (item, now) {\n  now = now || _.now();\n  var elapsedTime = now - this.startTime;\n  if (elapsedTime < 0 || elapsedTime >= 60000) {\n    this.startTime = now;\n    this.perMinCounter = 0;\n  }\n\n  var globalRateLimit = RateLimiter.globalSettings.maxItems;\n  var globalRateLimitPerMin = RateLimiter.globalSettings.itemsPerMinute;\n\n  if (checkRate(item, globalRateLimit, this.counter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimit + ' max items reached',\n      false,\n    );\n  } else if (checkRate(item, globalRateLimitPerMin, this.perMinCounter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimitPerMin + ' items per minute reached',\n      false,\n    );\n  }\n  this.counter++;\n  this.perMinCounter++;\n\n  var shouldSend = !checkRate(item, globalRateLimit, this.counter);\n  var perMinute = shouldSend;\n  shouldSend =\n    shouldSend && !checkRate(item, globalRateLimitPerMin, this.perMinCounter);\n  return shouldSendValue(\n    this.platform,\n    this.platformOptions,\n    null,\n    shouldSend,\n    globalRateLimit,\n    globalRateLimitPerMin,\n    perMinute,\n  );\n};\n\nRateLimiter.prototype.setPlatformOptions = function (platform, options) {\n  this.platform = platform;\n  this.platformOptions = options;\n};\n\n/* Helpers */\n\nfunction checkRate(item, limit, counter) {\n  return !item.ignoreRateLimit && limit >= 1 && counter > limit;\n}\n\nfunction shouldSendValue(\n  platform,\n  options,\n  error,\n  shouldSend,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var payload = null;\n  if (error) {\n    error = new Error(error);\n  }\n  if (!error && !shouldSend) {\n    payload = rateLimitPayload(\n      platform,\n      options,\n      globalRateLimit,\n      limitPerMin,\n      perMinute,\n    );\n  }\n  return { error: error, shouldSend: shouldSend, payload: payload };\n}\n\nfunction rateLimitPayload(\n  platform,\n  options,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var environment =\n    options.environment || (options.payload && options.payload.environment);\n  var msg;\n  if (perMinute) {\n    msg = 'item per minute limit reached, ignoring errors until timeout';\n  } else {\n    msg = 'maxItems has been hit, ignoring errors until reset.';\n  }\n  var item = {\n    body: {\n      message: {\n        body: msg,\n        extra: {\n          maxItems: globalRateLimit,\n          itemsPerMinute: limitPerMin,\n        },\n      },\n    },\n    language: 'javascript',\n    environment: environment,\n    notifier: {\n      version:\n        (options.notifier && options.notifier.version) || options.version,\n    },\n  };\n  if (platform === 'browser') {\n    item.platform = 'browser';\n    item.framework = 'browser-js';\n    item.notifier.name = 'rollbar-browser-js';\n  } else if (platform === 'server') {\n    item.framework = options.framework || 'node-js';\n    item.notifier.name = options.notifier.name;\n  } else if (platform === 'react-native') {\n    item.framework = options.framework || 'react-native';\n    item.notifier.name = options.notifier.name;\n  }\n  return item;\n}\n\nmodule.exports = RateLimiter;\n", "'use strict';\n\nvar RateLimiter = require('./rateLimiter');\nvar Queue = require('./queue');\nvar Notifier = require('./notifier');\nvar _ = require('./utility');\n\n/*\n * Rollbar - the interface to Rollbar\n *\n * @param options\n * @param api\n * @param logger\n */\nfunction Rollbar(options, api, logger, telemeter, platform) {\n  this.options = _.merge(options);\n  this.logger = logger;\n  Rollbar.rateLimiter.configureGlobal(this.options);\n  Rollbar.rateLimiter.setPlatformOptions(platform, this.options);\n  this.api = api;\n  this.queue = new Queue(Rollbar.rateLimiter, api, logger, this.options);\n\n  // This must happen before the Notifier is created\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier = new Notifier(this.queue, this.options);\n  this.telemeter = telemeter;\n  setStackTraceLimit(options);\n  this.lastError = null;\n  this.lastErrorHash = 'none';\n}\n\nvar defaultOptions = {\n  maxItems: 0,\n  itemsPerMinute: 60,\n};\n\nRollbar.rateLimiter = new RateLimiter(defaultOptions);\n\nRollbar.prototype.global = function (options) {\n  Rollbar.rateLimiter.configureGlobal(options);\n  return this;\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n\n  this.options = _.merge(oldOptions, options, payload);\n\n  // This must happen before the Notifier is configured\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier && this.notifier.configure(this.options);\n  this.telemeter && this.telemeter.configure(this.options);\n  setStackTraceLimit(options);\n  this.global(this.options);\n\n  if (validateTracer(options.tracer)) {\n    this.tracer = options.tracer;\n  }\n\n  return this;\n};\n\nRollbar.prototype.log = function (item) {\n  var level = this._defaultLogLevel();\n  return this._log(level, item);\n};\n\nRollbar.prototype.debug = function (item) {\n  this._log('debug', item);\n};\n\nRollbar.prototype.info = function (item) {\n  this._log('info', item);\n};\n\nRollbar.prototype.warn = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.warning = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.error = function (item) {\n  this._log('error', item);\n};\n\nRollbar.prototype.critical = function (item) {\n  this._log('critical', item);\n};\n\nRollbar.prototype.wait = function (callback) {\n  this.queue.wait(callback);\n};\n\nRollbar.prototype.captureEvent = function (type, metadata, level) {\n  return this.telemeter && this.telemeter.captureEvent(type, metadata, level);\n};\n\nRollbar.prototype.captureDomContentLoaded = function (ts) {\n  return this.telemeter && this.telemeter.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (ts) {\n  return this.telemeter && this.telemeter.captureLoad(ts);\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.api.buildJsonPayload(item);\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  this.api.postJsonPayload(jsonPayload);\n};\n\n/* Internal */\n\nRollbar.prototype._log = function (defaultLevel, item) {\n  var callback;\n  if (item.callback) {\n    callback = item.callback;\n    delete item.callback;\n  }\n  if (this.options.ignoreDuplicateErrors && this._sameAsLastError(item)) {\n    if (callback) {\n      var error = new Error('ignored identical item');\n      error.item = item;\n      callback(error);\n    }\n    return;\n  }\n  try {\n    this._addTracingInfo(item);\n    item.level = item.level || defaultLevel;\n    this.telemeter && this.telemeter._captureRollbarItem(item);\n    item.telemetryEvents =\n      (this.telemeter && this.telemeter.copyEvents()) || [];\n    this.notifier.log(item, callback);\n  } catch (e) {\n    if (callback) {\n      callback(e);\n    }\n    this.logger.error(e);\n  }\n};\n\nRollbar.prototype._defaultLogLevel = function () {\n  return this.options.logLevel || 'debug';\n};\n\nRollbar.prototype._sameAsLastError = function (item) {\n  if (!item._isUncaught) {\n    return false;\n  }\n  var itemHash = generateItemHash(item);\n  if (this.lastErrorHash === itemHash) {\n    return true;\n  }\n  this.lastError = item.err;\n  this.lastErrorHash = itemHash;\n  return false;\n};\n\nRollbar.prototype._addTracingInfo = function (item) {\n  // Tracer validation occurs in the constructor\n  // or in the Rollbar.prototype.configure methods\n  if (this.tracer) {\n    // add rollbar occurrence uuid to span\n    var span = this.tracer.scope().active();\n\n    if (validateSpan(span)) {\n      span.setTag('rollbar.error_uuid', item.uuid);\n      span.setTag('rollbar.has_error', true);\n      span.setTag('error', true);\n      span.setTag(\n        'rollbar.item_url',\n        `https://rollbar.com/item/uuid/?uuid=${item.uuid}`,\n      );\n      span.setTag(\n        'rollbar.occurrence_url',\n        `https://rollbar.com/occurrence/uuid/?uuid=${item.uuid}`,\n      );\n\n      // add span ID & trace ID to occurrence\n      var opentracingSpanId = span.context().toSpanId();\n      var opentracingTraceId = span.context().toTraceId();\n\n      if (item.custom) {\n        item.custom.opentracing_span_id = opentracingSpanId;\n        item.custom.opentracing_trace_id = opentracingTraceId;\n      } else {\n        item.custom = {\n          opentracing_span_id: opentracingSpanId,\n          opentracing_trace_id: opentracingTraceId,\n        };\n      }\n    }\n  }\n};\n\nfunction generateItemHash(item) {\n  var message = item.message || '';\n  var stack = (item.err || {}).stack || String(item.err);\n  return message + '::' + stack;\n}\n\n// Node.js, Chrome, Safari, and some other browsers support this property\n// which globally sets the number of stack frames returned in an Error object.\n// If a browser can't use it, no harm done.\nfunction setStackTraceLimit(options) {\n  if (options.stackTraceLimit) {\n    Error.stackTraceLimit = options.stackTraceLimit;\n  }\n}\n\n/**\n * Validate the Tracer object provided to the Client\n * is valid for our Opentracing use case.\n * @param {opentracer.Tracer} tracer\n */\nfunction validateTracer(tracer) {\n  if (!tracer) {\n    return false;\n  }\n\n  if (!tracer.scope || typeof tracer.scope !== 'function') {\n    return false;\n  }\n\n  var scope = tracer.scope();\n\n  if (!scope || !scope.active || typeof scope.active !== 'function') {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Validate the Span object provided\n * @param {opentracer.Span} span\n */\nfunction validateSpan(span) {\n  if (!span || !span.context || typeof span.context !== 'function') {\n    return false;\n  }\n\n  var spanContext = span.context();\n\n  if (\n    !spanContext ||\n    !spanContext.toSpanId ||\n    !spanContext.toTraceId ||\n    typeof spanContext.toSpanId !== 'function' ||\n    typeof spanContext.toTraceId !== 'function'\n  ) {\n    return false;\n  }\n\n  return true;\n}\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction scrub(data, scrubFields, scrubPaths) {\n  scrubFields = scrubFields || [];\n\n  if (scrubPaths) {\n    for (var i = 0; i < scrubPaths.length; ++i) {\n      scrubPath(data, scrubPaths[i]);\n    }\n  }\n\n  var paramRes = _getScrubFieldRegexs(scrubFields);\n  var queryRes = _getScrubQueryParamRegexs(scrubFields);\n\n  function redactQueryParam(dummy0, paramPart) {\n    return paramPart + _.redact();\n  }\n\n  function paramScrubber(v) {\n    var i;\n    if (_.isType(v, 'string')) {\n      for (i = 0; i < queryRes.length; ++i) {\n        v = v.replace(queryRes[i], redactQueryParam);\n      }\n    }\n    return v;\n  }\n\n  function valScrubber(k, v) {\n    var i;\n    for (i = 0; i < paramRes.length; ++i) {\n      if (paramRes[i].test(k)) {\n        v = _.redact();\n        break;\n      }\n    }\n    return v;\n  }\n\n  function scrubber(k, v, seen) {\n    var tmpV = valScrubber(k, v);\n    if (tmpV === v) {\n      if (_.isType(v, 'object') || _.isType(v, 'array')) {\n        return traverse(v, scrubber, seen);\n      }\n      return paramScrubber(tmpV);\n    } else {\n      return tmpV;\n    }\n  }\n\n  return traverse(data, scrubber);\n}\n\nfunction scrubPath(obj, path) {\n  var keys = path.split('.');\n  var last = keys.length - 1;\n  try {\n    for (var i = 0; i <= last; ++i) {\n      if (i < last) {\n        obj = obj[keys[i]];\n      } else {\n        obj[keys[i]] = _.redact();\n      }\n    }\n  } catch (e) {\n    // Missing key is OK;\n  }\n}\n\nfunction _getScrubFieldRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '^\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?$';\n    ret.push(new RegExp(pat, 'i'));\n  }\n  return ret;\n}\n\nfunction _getScrubQueryParamRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?';\n    ret.push(new RegExp('(' + pat + '=)([^&\\\\n]+)', 'igm'));\n  }\n  return ret;\n}\n\nmodule.exports = scrub;\n", "'use strict';\n\nvar _ = require('./utility');\n\nvar MAX_EVENTS = 100;\n\nfunction Telemeter(options) {\n  this.queue = [];\n  this.options = _.merge(options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  this.maxQueueSize = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n}\n\nTelemeter.prototype.configure = function (options) {\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  var newMaxEvents = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n  var deleteCount = 0;\n  if (this.queue.length > newMaxEvents) {\n    deleteCount = this.queue.length - newMaxEvents;\n  }\n  this.maxQueueSize = newMaxEvents;\n  this.queue.splice(0, deleteCount);\n};\n\nTelemeter.prototype.copyEvents = function () {\n  var events = Array.prototype.slice.call(this.queue, 0);\n  if (_.isFunction(this.options.filterTelemetry)) {\n    try {\n      var i = events.length;\n      while (i--) {\n        if (this.options.filterTelemetry(events[i])) {\n          events.splice(i, 1);\n        }\n      }\n    } catch (e) {\n      this.options.filterTelemetry = null;\n    }\n  }\n  return events;\n};\n\nTelemeter.prototype.capture = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var e = {\n    level: getLevel(type, level),\n    type: type,\n    timestamp_ms: timestamp || _.now(),\n    body: metadata,\n    source: 'client',\n  };\n  if (rollbarUUID) {\n    e.uuid = rollbarUUID;\n  }\n\n  try {\n    if (\n      _.isFunction(this.options.filterTelemetry) &&\n      this.options.filterTelemetry(e)\n    ) {\n      return false;\n    }\n  } catch (exc) {\n    this.options.filterTelemetry = null;\n  }\n\n  this.push(e);\n  return e;\n};\n\nTelemeter.prototype.captureEvent = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n) {\n  return this.capture(type, metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.captureError = function (\n  err,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var metadata = {\n    message: err.message || String(err),\n  };\n  if (err.stack) {\n    metadata.stack = err.stack;\n  }\n  return this.capture('error', metadata, level, rollbarUUID, timestamp);\n};\n\nTelemeter.prototype.captureLog = function (\n  message,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  return this.capture(\n    'log',\n    {\n      message: message,\n    },\n    level,\n    rollbarUUID,\n    timestamp,\n  );\n};\n\nTelemeter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n  requestData,\n) {\n  subtype = subtype || 'xhr';\n  metadata.subtype = metadata.subtype || subtype;\n  if (requestData) {\n    metadata.request = requestData;\n  }\n  var level = this.levelFromStatus(metadata.status_code);\n  return this.capture('network', metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.levelFromStatus = function (statusCode) {\n  if (statusCode >= 200 && statusCode < 400) {\n    return 'info';\n  }\n  if (statusCode === 0 || statusCode >= 400) {\n    return 'error';\n  }\n  return 'info';\n};\n\nTelemeter.prototype.captureDom = function (\n  subtype,\n  element,\n  value,\n  checked,\n  rollbarUUID,\n) {\n  var metadata = {\n    subtype: subtype,\n    element: element,\n  };\n  if (value !== undefined) {\n    metadata.value = value;\n  }\n  if (checked !== undefined) {\n    metadata.checked = checked;\n  }\n  return this.capture('dom', metadata, 'info', rollbarUUID);\n};\n\nTelemeter.prototype.captureNavigation = function (from, to, rollbarUUID) {\n  return this.capture(\n    'navigation',\n    { from: from, to: to },\n    'info',\n    rollbarUUID,\n  );\n};\n\nTelemeter.prototype.captureDomContentLoaded = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'DOMContentLoaded' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'DOMContentLoaded'}, 'info', undefined, ts && ts.getTime());\n  */\n};\nTelemeter.prototype.captureLoad = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'load' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'load'}, 'info', undefined, ts && ts.getTime());\n  */\n};\n\nTelemeter.prototype.captureConnectivityChange = function (type, rollbarUUID) {\n  return this.captureNetwork({ change: type }, 'connectivity', rollbarUUID);\n};\n\n// Only intended to be used internally by the notifier\nTelemeter.prototype._captureRollbarItem = function (item) {\n  if (!this.options.includeItemsInTelemetry) {\n    return;\n  }\n  if (item.err) {\n    return this.captureError(item.err, item.level, item.uuid, item.timestamp);\n  }\n  if (item.message) {\n    return this.captureLog(item.message, item.level, item.uuid, item.timestamp);\n  }\n  if (item.custom) {\n    return this.capture(\n      'log',\n      item.custom,\n      item.level,\n      item.uuid,\n      item.timestamp,\n    );\n  }\n};\n\nTelemeter.prototype.push = function (e) {\n  this.queue.push(e);\n  if (this.queue.length > this.maxQueueSize) {\n    this.queue.shift();\n  }\n};\n\nfunction getLevel(type, level) {\n  if (level) {\n    return level;\n  }\n  var defaultLevel = {\n    error: 'error',\n    manual: 'info',\n  };\n  return defaultLevel[type] || 'info';\n}\n\nmodule.exports = Telemeter;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction itemToPayload(item, options, callback) {\n  var data = item.data;\n\n  if (item._isUncaught) {\n    data._isUncaught = true;\n  }\n  if (item._originalArgs) {\n    data._originalArgs = item._originalArgs;\n  }\n  callback(null, data);\n}\n\nfunction addPayloadOptions(item, options, callback) {\n  var payloadOptions = options.payload || {};\n  if (payloadOptions.body) {\n    delete payloadOptions.body;\n  }\n\n  item.data = _.merge(item.data, payloadOptions);\n  callback(null, item);\n}\n\nfunction addTelemetryData(item, options, callback) {\n  if (item.telemetryEvents) {\n    _.set(item, 'data.body.telemetry', item.telemetryEvents);\n  }\n  callback(null, item);\n}\n\nfunction addMessageWithError(item, options, callback) {\n  if (!item.message) {\n    callback(null, item);\n    return;\n  }\n  var tracePath = 'data.body.trace_chain.0';\n  var trace = _.get(item, tracePath);\n  if (!trace) {\n    tracePath = 'data.body.trace';\n    trace = _.get(item, tracePath);\n  }\n  if (trace) {\n    if (!(trace.exception && trace.exception.description)) {\n      _.set(item, tracePath + '.exception.description', item.message);\n      callback(null, item);\n      return;\n    }\n    var extra = _.get(item, tracePath + '.extra') || {};\n    var newExtra = _.merge(extra, { message: item.message });\n    _.set(item, tracePath + '.extra', newExtra);\n  }\n  callback(null, item);\n}\n\nfunction userTransform(logger) {\n  return function (item, options, callback) {\n    var newItem = _.merge(item);\n    var response = null;\n    try {\n      if (_.isFunction(options.transform)) {\n        response = options.transform(newItem.data, item);\n      }\n    } catch (e) {\n      options.transform = null;\n      logger.error(\n        'Error while calling custom transform() function. Removing custom transform().',\n        e,\n      );\n      callback(null, item);\n      return;\n    }\n    if (_.isPromise(response)) {\n      response.then(\n        function (promisedItem) {\n          if (promisedItem) {\n            newItem.data = promisedItem;\n          }\n          callback(null, newItem);\n        },\n        function (error) {\n          callback(error, item);\n        },\n      );\n    } else {\n      callback(null, newItem);\n    }\n  };\n}\n\nfunction addConfigToPayload(item, options, callback) {\n  if (!options.sendConfig) {\n    return callback(null, item);\n  }\n  var configKey = '_rollbarConfig';\n  var custom = _.get(item, 'data.custom') || {};\n  custom[configKey] = options;\n  item.data.custom = custom;\n  callback(null, item);\n}\n\nfunction addFunctionOption(options, name) {\n  if (_.isFunction(options[name])) {\n    options[name] = options[name].toString();\n  }\n}\n\nfunction addConfiguredOptions(item, options, callback) {\n  var configuredOptions = options._configuredOptions;\n\n  // These must be stringified or they'll get dropped during serialization.\n  addFunctionOption(configuredOptions, 'transform');\n  addFunctionOption(configuredOptions, 'checkIgnore');\n  addFunctionOption(configuredOptions, 'onSendCallback');\n\n  delete configuredOptions.accessToken;\n  item.data.notifier.configured_options = configuredOptions;\n  callback(null, item);\n}\n\nfunction addDiagnosticKeys(item, options, callback) {\n  var diagnostic = _.merge(\n    item.notifier.client.notifier.diagnostic,\n    item.diagnostic,\n  );\n\n  if (_.get(item, 'err._isAnonymous')) {\n    diagnostic.is_anonymous = true;\n  }\n\n  if (item._isUncaught) {\n    diagnostic.is_uncaught = item._isUncaught;\n  }\n\n  if (item.err) {\n    try {\n      diagnostic.raw_error = {\n        message: item.err.message,\n        name: item.err.name,\n        constructor_name: item.err.constructor && item.err.constructor.name,\n        filename: item.err.fileName,\n        line: item.err.lineNumber,\n        column: item.err.columnNumber,\n        stack: item.err.stack,\n      };\n    } catch (e) {\n      diagnostic.raw_error = { failed: String(e) };\n    }\n  }\n\n  item.data.notifier.diagnostic = _.merge(\n    item.data.notifier.diagnostic,\n    diagnostic,\n  );\n  callback(null, item);\n}\n\nmodule.exports = {\n  itemToPayload: itemToPayload,\n  addPayloadOptions: addPayloadOptions,\n  addTelemetryData: addTelemetryData,\n  addMessageWithError: addMessageWithError,\n  userTransform: userTransform,\n  addConfigToPayload: addConfigToPayload,\n  addConfiguredOptions: addConfiguredOptions,\n  addDiagnosticKeys: addDiagnosticKeys,\n};\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction raw(payload, jsonBackup) {\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction selectFrames(frames, range) {\n  var len = frames.length;\n  if (len > range * 2) {\n    return frames.slice(0, range).concat(frames.slice(len - range));\n  }\n  return frames;\n}\n\nfunction truncateFrames(payload, jsonBackup, range) {\n  range = typeof range === 'undefined' ? 30 : range;\n  var body = payload.data.body;\n  var frames;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      frames = chain[i].frames;\n      frames = selectFrames(frames, range);\n      chain[i].frames = frames;\n    }\n  } else if (body.trace) {\n    frames = body.trace.frames;\n    frames = selectFrames(frames, range);\n    body.trace.frames = frames;\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction maybeTruncateValue(len, val) {\n  if (!val) {\n    return val;\n  }\n  if (val.length > len) {\n    return val.slice(0, len - 3).concat('...');\n  }\n  return val;\n}\n\nfunction truncateStrings(len, payload, jsonBackup) {\n  function truncator(k, v, seen) {\n    switch (_.typeName(v)) {\n      case 'string':\n        return maybeTruncateValue(len, v);\n      case 'object':\n      case 'array':\n        return traverse(v, truncator, seen);\n      default:\n        return v;\n    }\n  }\n  payload = traverse(payload, truncator);\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction truncateTraceData(traceData) {\n  if (traceData.exception) {\n    delete traceData.exception.description;\n    traceData.exception.message = maybeTruncateValue(\n      255,\n      traceData.exception.message,\n    );\n  }\n  traceData.frames = selectFrames(traceData.frames, 1);\n  return traceData;\n}\n\nfunction minBody(payload, jsonBackup) {\n  var body = payload.data.body;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      chain[i] = truncateTraceData(chain[i]);\n    }\n  } else if (body.trace) {\n    body.trace = truncateTraceData(body.trace);\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction needsTruncation(payload, maxSize) {\n  return _.maxByteSize(payload) > maxSize;\n}\n\nfunction truncate(payload, jsonBackup, maxSize) {\n  maxSize = typeof maxSize === 'undefined' ? 512 * 1024 : maxSize;\n  var strategies = [\n    raw,\n    truncateFrames,\n    truncateStrings.bind(null, 1024),\n    truncateStrings.bind(null, 512),\n    truncateStrings.bind(null, 256),\n    minBody,\n  ];\n  var strategy, results, result;\n\n  while ((strategy = strategies.shift())) {\n    results = strategy(payload, jsonBackup);\n    payload = results[0];\n    result = results[1];\n    if (result.error || !needsTruncation(result.value, maxSize)) {\n      return result;\n    }\n  }\n  return result;\n}\n\nmodule.exports = {\n  truncate: truncate,\n\n  /* for testing */\n  raw: raw,\n  truncateFrames: truncateFrames,\n  truncateStrings: truncateStrings,\n  maybeTruncateValue: maybeTruncateValue,\n};\n", "'use strict';\n\nvar merge = require('./merge');\n\nvar RollbarJSON = {};\nfunction setupJSON(polyfillJSON) {\n  if (isFunction(RollbarJSON.stringify) && isFunction(RollbarJSON.parse)) {\n    return;\n  }\n\n  if (isDefined(JSON)) {\n    // If polyfill is provided, prefer it over existing non-native shims.\n    if (polyfillJSON) {\n      if (isNativeFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isNativeFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    } else {\n      // else accept any interface that is present.\n      if (isFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    }\n  }\n  if (!isFunction(RollbarJSON.stringify) || !isFunction(RollbarJSON.parse)) {\n    polyfillJSON && polyfillJSON(RollbarJSON);\n  }\n}\n\n/*\n * isType - Given a Javascript value and a string, returns true if the type of the value matches the\n * given string.\n *\n * @param x - any value\n * @param t - a lowercase string containing one of the following type names:\n *    - undefined\n *    - null\n *    - error\n *    - number\n *    - boolean\n *    - string\n *    - symbol\n *    - function\n *    - object\n *    - array\n * @returns true if x is of type t, otherwise false\n */\nfunction isType(x, t) {\n  return t === typeName(x);\n}\n\n/*\n * typeName - Given a Javascript value, returns the type of the object as a string\n */\nfunction typeName(x) {\n  var name = typeof x;\n  if (name !== 'object') {\n    return name;\n  }\n  if (!x) {\n    return 'null';\n  }\n  if (x instanceof Error) {\n    return 'error';\n  }\n  return {}.toString\n    .call(x)\n    .match(/\\s([a-zA-Z]+)/)[1]\n    .toLowerCase();\n}\n\n/* isFunction - a convenience function for checking if a value is a function\n *\n * @param f - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isFunction(f) {\n  return isType(f, 'function');\n}\n\n/* isNativeFunction - a convenience function for checking if a value is a native JS function\n *\n * @param f - any value\n * @returns true if f is a native JS function, otherwise false\n */\nfunction isNativeFunction(f) {\n  var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n  var funcMatchString = Function.prototype.toString\n    .call(Object.prototype.hasOwnProperty)\n    .replace(reRegExpChar, '\\\\$&')\n    .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?');\n  var reIsNative = RegExp('^' + funcMatchString + '$');\n  return isObject(f) && reIsNative.test(f);\n}\n\n/* isObject - Checks if the argument is an object\n *\n * @param value - any value\n * @returns true is value is an object function is an object)\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\n/* isString - Checks if the argument is a string\n *\n * @param value - any value\n * @returns true if value is a string\n */\nfunction isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\n\n/**\n * isFiniteNumber - determines whether the passed value is a finite number\n *\n * @param {*} n - any value\n * @returns true if value is a finite number\n */\nfunction isFiniteNumber(n) {\n  return Number.isFinite(n);\n}\n\n/*\n * isDefined - a convenience function for checking if a value is not equal to undefined\n *\n * @param u - any value\n * @returns true if u is anything other than undefined\n */\nfunction isDefined(u) {\n  return !isType(u, 'undefined');\n}\n\n/*\n * isIterable - convenience function for checking if a value can be iterated, essentially\n * whether it is an object or an array.\n *\n * @param i - any value\n * @returns true if i is an object or an array as determined by `typeName`\n */\nfunction isIterable(i) {\n  var type = typeName(i);\n  return type === 'object' || type === 'array';\n}\n\n/*\n * isError - convenience function for checking if a value is of an error type\n *\n * @param e - any value\n * @returns true if e is an error\n */\nfunction isError(e) {\n  // Detect both Error and Firefox Exception type\n  return isType(e, 'error') || isType(e, 'exception');\n}\n\n/* isPromise - a convenience function for checking if a value is a promise\n *\n * @param p - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isPromise(p) {\n  return isObject(p) && isType(p.then, 'function');\n}\n\nfunction redact() {\n  return '********';\n}\n\n// from http://stackoverflow.com/a/8809472/1138191\nfunction uuid4() {\n  var d = now();\n  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(\n    /[xy]/g,\n    function (c) {\n      var r = (d + Math.random() * 16) % 16 | 0;\n      d = Math.floor(d / 16);\n      return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);\n    },\n  );\n  return uuid;\n}\n\nvar LEVELS = {\n  debug: 0,\n  info: 1,\n  warning: 2,\n  error: 3,\n  critical: 4,\n};\n\nfunction sanitizeUrl(url) {\n  var baseUrlParts = parseUri(url);\n  if (!baseUrlParts) {\n    return '(unknown)';\n  }\n\n  // remove a trailing # if there is no anchor\n  if (baseUrlParts.anchor === '') {\n    baseUrlParts.source = baseUrlParts.source.replace('#', '');\n  }\n\n  url = baseUrlParts.source.replace('?' + baseUrlParts.query, '');\n  return url;\n}\n\nvar parseUriOptions = {\n  strictMode: false,\n  key: [\n    'source',\n    'protocol',\n    'authority',\n    'userInfo',\n    'user',\n    'password',\n    'host',\n    'port',\n    'relative',\n    'path',\n    'directory',\n    'file',\n    'query',\n    'anchor',\n  ],\n  q: {\n    name: 'queryKey',\n    parser: /(?:^|&)([^&=]*)=?([^&]*)/g,\n  },\n  parser: {\n    strict:\n      /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n    loose:\n      /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n  },\n};\n\nfunction parseUri(str) {\n  if (!isType(str, 'string')) {\n    return undefined;\n  }\n\n  var o = parseUriOptions;\n  var m = o.parser[o.strictMode ? 'strict' : 'loose'].exec(str);\n  var uri = {};\n\n  for (var i = 0, l = o.key.length; i < l; ++i) {\n    uri[o.key[i]] = m[i] || '';\n  }\n\n  uri[o.q.name] = {};\n  uri[o.key[12]].replace(o.q.parser, function ($0, $1, $2) {\n    if ($1) {\n      uri[o.q.name][$1] = $2;\n    }\n  });\n\n  return uri;\n}\n\nfunction addParamsAndAccessTokenToPath(accessToken, options, params) {\n  params = params || {};\n  params.access_token = accessToken;\n  var paramsArray = [];\n  var k;\n  for (k in params) {\n    if (Object.prototype.hasOwnProperty.call(params, k)) {\n      paramsArray.push([k, params[k]].join('='));\n    }\n  }\n  var query = '?' + paramsArray.sort().join('&');\n\n  options = options || {};\n  options.path = options.path || '';\n  var qs = options.path.indexOf('?');\n  var h = options.path.indexOf('#');\n  var p;\n  if (qs !== -1 && (h === -1 || h > qs)) {\n    p = options.path;\n    options.path = p.substring(0, qs) + query + '&' + p.substring(qs + 1);\n  } else {\n    if (h !== -1) {\n      p = options.path;\n      options.path = p.substring(0, h) + query + p.substring(h);\n    } else {\n      options.path = options.path + query;\n    }\n  }\n}\n\nfunction formatUrl(u, protocol) {\n  protocol = protocol || u.protocol;\n  if (!protocol && u.port) {\n    if (u.port === 80) {\n      protocol = 'http:';\n    } else if (u.port === 443) {\n      protocol = 'https:';\n    }\n  }\n  protocol = protocol || 'https:';\n\n  if (!u.hostname) {\n    return null;\n  }\n  var result = protocol + '//' + u.hostname;\n  if (u.port) {\n    result = result + ':' + u.port;\n  }\n  if (u.path) {\n    result = result + u.path;\n  }\n  return result;\n}\n\nfunction stringify(obj, backup) {\n  var value, error;\n  try {\n    value = RollbarJSON.stringify(obj);\n  } catch (jsonError) {\n    if (backup && isFunction(backup)) {\n      try {\n        value = backup(obj);\n      } catch (backupError) {\n        error = backupError;\n      }\n    } else {\n      error = jsonError;\n    }\n  }\n  return { error: error, value: value };\n}\n\nfunction maxByteSize(string) {\n  // The transport will use utf-8, so assume utf-8 encoding.\n  //\n  // This minimal implementation will accurately count bytes for all UCS-2 and\n  // single code point UTF-16. If presented with multi code point UTF-16,\n  // which should be rare, it will safely overcount, not undercount.\n  //\n  // While robust utf-8 encoders exist, this is far smaller and far more performant.\n  // For quickly counting payload size for truncation, smaller is better.\n\n  var count = 0;\n  var length = string.length;\n\n  for (var i = 0; i < length; i++) {\n    var code = string.charCodeAt(i);\n    if (code < 128) {\n      // up to 7 bits\n      count = count + 1;\n    } else if (code < 2048) {\n      // up to 11 bits\n      count = count + 2;\n    } else if (code < 65536) {\n      // up to 16 bits\n      count = count + 3;\n    }\n  }\n\n  return count;\n}\n\nfunction jsonParse(s) {\n  var value, error;\n  try {\n    value = RollbarJSON.parse(s);\n  } catch (e) {\n    error = e;\n  }\n  return { error: error, value: value };\n}\n\nfunction makeUnhandledStackInfo(\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  mode,\n  backupMessage,\n  errorParser,\n) {\n  var location = {\n    url: url || '',\n    line: lineno,\n    column: colno,\n  };\n  location.func = errorParser.guessFunctionName(location.url, location.line);\n  location.context = errorParser.gatherContext(location.url, location.line);\n  var href =\n    typeof document !== 'undefined' &&\n    document &&\n    document.location &&\n    document.location.href;\n  var useragent =\n    typeof window !== 'undefined' &&\n    window &&\n    window.navigator &&\n    window.navigator.userAgent;\n  return {\n    mode: mode,\n    message: error ? String(error) : message || backupMessage,\n    url: href,\n    stack: [location],\n    useragent: useragent,\n  };\n}\n\nfunction wrapCallback(logger, f) {\n  return function (err, resp) {\n    try {\n      f(err, resp);\n    } catch (e) {\n      logger.error(e);\n    }\n  };\n}\n\nfunction nonCircularClone(obj) {\n  var seen = [obj];\n\n  function clone(obj, seen) {\n    var value,\n      name,\n      newSeen,\n      result = {};\n\n    try {\n      for (name in obj) {\n        value = obj[name];\n\n        if (value && (isType(value, 'object') || isType(value, 'array'))) {\n          if (seen.includes(value)) {\n            result[name] = 'Removed circular reference: ' + typeName(value);\n          } else {\n            newSeen = seen.slice();\n            newSeen.push(value);\n            result[name] = clone(value, newSeen);\n          }\n          continue;\n        }\n\n        result[name] = value;\n      }\n    } catch (e) {\n      result = 'Failed cloning custom data: ' + e.message;\n    }\n    return result;\n  }\n  return clone(obj, seen);\n}\n\nfunction createItem(args, logger, notifier, requestKeys, lambdaContext) {\n  var message, err, custom, callback, request;\n  var arg;\n  var extraArgs = [];\n  var diagnostic = {};\n  var argTypes = [];\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    argTypes.push(typ);\n    switch (typ) {\n      case 'undefined':\n        break;\n      case 'string':\n        message ? extraArgs.push(arg) : (message = arg);\n        break;\n      case 'function':\n        callback = wrapCallback(logger, arg);\n        break;\n      case 'date':\n        extraArgs.push(arg);\n        break;\n      case 'error':\n      case 'domexception':\n      case 'exception': // Firefox Exception type\n        err ? extraArgs.push(arg) : (err = arg);\n        break;\n      case 'object':\n      case 'array':\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        if (requestKeys && typ === 'object' && !request) {\n          for (var j = 0, len = requestKeys.length; j < len; ++j) {\n            if (arg[requestKeys[j]] !== undefined) {\n              request = arg;\n              break;\n            }\n          }\n          if (request) {\n            break;\n          }\n        }\n        custom ? extraArgs.push(arg) : (custom = arg);\n        break;\n      default:\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        extraArgs.push(arg);\n    }\n  }\n\n  // if custom is an array this turns it into an object with integer keys\n  if (custom) custom = nonCircularClone(custom);\n\n  if (extraArgs.length > 0) {\n    if (!custom) custom = nonCircularClone({});\n    custom.extraArgs = nonCircularClone(extraArgs);\n  }\n\n  var item = {\n    message: message,\n    err: err,\n    custom: custom,\n    timestamp: now(),\n    callback: callback,\n    notifier: notifier,\n    diagnostic: diagnostic,\n    uuid: uuid4(),\n  };\n\n  setCustomItemKeys(item, custom);\n\n  if (requestKeys && request) {\n    item.request = request;\n  }\n  if (lambdaContext) {\n    item.lambdaContext = lambdaContext;\n  }\n  item._originalArgs = args;\n  item.diagnostic.original_arg_types = argTypes;\n  return item;\n}\n\nfunction setCustomItemKeys(item, custom) {\n  if (custom && custom.level !== undefined) {\n    item.level = custom.level;\n    delete custom.level;\n  }\n  if (custom && custom.skipFrames !== undefined) {\n    item.skipFrames = custom.skipFrames;\n    delete custom.skipFrames;\n  }\n}\n\nfunction addErrorContext(item, errors) {\n  var custom = item.data.custom || {};\n  var contextAdded = false;\n\n  try {\n    for (var i = 0; i < errors.length; ++i) {\n      if (errors[i].hasOwnProperty('rollbarContext')) {\n        custom = merge(custom, nonCircularClone(errors[i].rollbarContext));\n        contextAdded = true;\n      }\n    }\n\n    // Avoid adding an empty object to the data.\n    if (contextAdded) {\n      item.data.custom = custom;\n    }\n  } catch (e) {\n    item.diagnostic.error_context = 'Failed: ' + e.message;\n  }\n}\n\nvar TELEMETRY_TYPES = [\n  'log',\n  'network',\n  'dom',\n  'navigation',\n  'error',\n  'manual',\n];\nvar TELEMETRY_LEVELS = ['critical', 'error', 'warning', 'info', 'debug'];\n\nfunction arrayIncludes(arr, val) {\n  for (var k = 0; k < arr.length; ++k) {\n    if (arr[k] === val) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction createTelemetryEvent(args) {\n  var type, metadata, level;\n  var arg;\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    switch (typ) {\n      case 'string':\n        if (!type && arrayIncludes(TELEMETRY_TYPES, arg)) {\n          type = arg;\n        } else if (!level && arrayIncludes(TELEMETRY_LEVELS, arg)) {\n          level = arg;\n        }\n        break;\n      case 'object':\n        metadata = arg;\n        break;\n      default:\n        break;\n    }\n  }\n  var event = {\n    type: type || 'manual',\n    metadata: metadata || {},\n    level: level,\n  };\n\n  return event;\n}\n\n/*\n * get - given an obj/array and a keypath, return the value at that keypath or\n *       undefined if not possible.\n *\n * @param obj - an object or array\n * @param path - a string of keys separated by '.' such as 'plugin.jquery.0.message'\n *    which would correspond to 42 in `{plugin: {jquery: [{message: 42}]}}`\n */\nfunction get(obj, path) {\n  if (!obj) {\n    return undefined;\n  }\n  var keys = path.split('.');\n  var result = obj;\n  try {\n    for (var i = 0, len = keys.length; i < len; ++i) {\n      result = result[keys[i]];\n    }\n  } catch (e) {\n    result = undefined;\n  }\n  return result;\n}\n\nfunction set(obj, path, value) {\n  if (!obj) {\n    return;\n  }\n  var keys = path.split('.');\n  var len = keys.length;\n  if (len < 1) {\n    return;\n  }\n  if (len === 1) {\n    obj[keys[0]] = value;\n    return;\n  }\n  try {\n    var temp = obj[keys[0]] || {};\n    var replacement = temp;\n    for (var i = 1; i < len - 1; ++i) {\n      temp[keys[i]] = temp[keys[i]] || {};\n      temp = temp[keys[i]];\n    }\n    temp[keys[len - 1]] = value;\n    obj[keys[0]] = replacement;\n  } catch (e) {\n    return;\n  }\n}\n\nfunction formatArgsAsString(args) {\n  var i, len, arg;\n  var result = [];\n  for (i = 0, len = args.length; i < len; ++i) {\n    arg = args[i];\n    switch (typeName(arg)) {\n      case 'object':\n        arg = stringify(arg);\n        arg = arg.error || arg.value;\n        if (arg.length > 500) {\n          arg = arg.substr(0, 497) + '...';\n        }\n        break;\n      case 'null':\n        arg = 'null';\n        break;\n      case 'undefined':\n        arg = 'undefined';\n        break;\n      case 'symbol':\n        arg = arg.toString();\n        break;\n    }\n    result.push(arg);\n  }\n  return result.join(' ');\n}\n\nfunction now() {\n  if (Date.now) {\n    return +Date.now();\n  }\n  return +new Date();\n}\n\nfunction filterIp(requestData, captureIp) {\n  if (!requestData || !requestData['user_ip'] || captureIp === true) {\n    return;\n  }\n  var newIp = requestData['user_ip'];\n  if (!captureIp) {\n    newIp = null;\n  } else {\n    try {\n      var parts;\n      if (newIp.indexOf('.') !== -1) {\n        parts = newIp.split('.');\n        parts.pop();\n        parts.push('0');\n        newIp = parts.join('.');\n      } else if (newIp.indexOf(':') !== -1) {\n        parts = newIp.split(':');\n        if (parts.length > 2) {\n          var beginning = parts.slice(0, 3);\n          var slashIdx = beginning[2].indexOf('/');\n          if (slashIdx !== -1) {\n            beginning[2] = beginning[2].substring(0, slashIdx);\n          }\n          var terminal = '0000:0000:0000:0000:0000';\n          newIp = beginning.concat(terminal).join(':');\n        }\n      } else {\n        newIp = null;\n      }\n    } catch (e) {\n      newIp = null;\n    }\n  }\n  requestData['user_ip'] = newIp;\n}\n\nfunction handleOptions(current, input, payload, logger) {\n  var result = merge(current, input, payload);\n  result = updateDeprecatedOptions(result, logger);\n  if (!input || input.overwriteScrubFields) {\n    return result;\n  }\n  if (input.scrubFields) {\n    result.scrubFields = (current.scrubFields || []).concat(input.scrubFields);\n  }\n  return result;\n}\n\nfunction updateDeprecatedOptions(options, logger) {\n  if (options.hostWhiteList && !options.hostSafeList) {\n    options.hostSafeList = options.hostWhiteList;\n    options.hostWhiteList = undefined;\n    logger && logger.log('hostWhiteList is deprecated. Use hostSafeList.');\n  }\n  if (options.hostBlackList && !options.hostBlockList) {\n    options.hostBlockList = options.hostBlackList;\n    options.hostBlackList = undefined;\n    logger && logger.log('hostBlackList is deprecated. Use hostBlockList.');\n  }\n  return options;\n}\n\nmodule.exports = {\n  addParamsAndAccessTokenToPath: addParamsAndAccessTokenToPath,\n  createItem: createItem,\n  addErrorContext: addErrorContext,\n  createTelemetryEvent: createTelemetryEvent,\n  filterIp: filterIp,\n  formatArgsAsString: formatArgsAsString,\n  formatUrl: formatUrl,\n  get: get,\n  handleOptions: handleOptions,\n  isError: isError,\n  isFiniteNumber: isFiniteNumber,\n  isFunction: isFunction,\n  isIterable: isIterable,\n  isNativeFunction: isNativeFunction,\n  isObject: isObject,\n  isString: isString,\n  isType: isType,\n  isPromise: isPromise,\n  jsonParse: jsonParse,\n  LEVELS: LEVELS,\n  makeUnhandledStackInfo: makeUnhandledStackInfo,\n  merge: merge,\n  now: now,\n  redact: redact,\n  RollbarJSON: RollbarJSON,\n  sanitizeUrl: sanitizeUrl,\n  set: set,\n  setupJSON: setupJSON,\n  stringify: stringify,\n  maxByteSize: maxByteSize,\n  typeName: typeName,\n  uuid4: uuid4,\n};\n", "'use strict';\n\n/*\n * headers - Detect when fetch Headers are undefined and use a partial polyfill.\n *\n * A full polyfill is not used in order to keep package size as small as possible.\n * Since this is only used internally and is not added to the window object,\n * the full interface doesn't need to be supported.\n *\n * This implementation is modified from whatwg-fetch:\n * https://github.com/github/fetch\n */\nfunction headers(headers) {\n  if (typeof Headers === 'undefined') {\n    return new FetchHeaders(headers);\n  }\n\n  return new Headers(headers);\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name);\n  }\n  return name.toLowerCase();\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value);\n  }\n  return value;\n}\n\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function () {\n      var value = items.shift();\n      return { done: value === undefined, value: value };\n    },\n  };\n\n  return iterator;\n}\n\nfunction FetchHeaders(headers) {\n  this.map = {};\n\n  if (headers instanceof FetchHeaders) {\n    headers.forEach(function (value, name) {\n      this.append(name, value);\n    }, this);\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function (header) {\n      this.append(header[0], header[1]);\n    }, this);\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function (name) {\n      this.append(name, headers[name]);\n    }, this);\n  }\n}\n\nFetchHeaders.prototype.append = function (name, value) {\n  name = normalizeName(name);\n  value = normalizeValue(value);\n  var oldValue = this.map[name];\n  this.map[name] = oldValue ? oldValue + ', ' + value : value;\n};\n\nFetchHeaders.prototype.get = function (name) {\n  name = normalizeName(name);\n  return this.has(name) ? this.map[name] : null;\n};\n\nFetchHeaders.prototype.has = function (name) {\n  return this.map.hasOwnProperty(normalizeName(name));\n};\n\nFetchHeaders.prototype.forEach = function (callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this);\n    }\n  }\n};\n\nFetchHeaders.prototype.entries = function () {\n  var items = [];\n  this.forEach(function (value, name) {\n    items.push([name, value]);\n  });\n  return iteratorFor(items);\n};\n\nmodule.exports = headers;\n", "'use strict';\n\nvar polyfillJSON = require('../../vendor/JSON-js/json3');\n\nmodule.exports = polyfillJSON;\n", "'use strict';\n\nfunction replace(obj, name, replacement, replacements, type) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (replacements) {\n    replacements[type].push([obj, name, orig]);\n  }\n}\n\nmodule.exports = replace;\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction traverse(obj, func, seen) {\n  var k, v, i;\n  var isObj = _.isType(obj, 'object');\n  var isArray = _.isType(obj, 'array');\n  var keys = [];\n  var seenIndex;\n\n  // Best might be to use Map here with `obj` as the keys, but we want to support IE < 11.\n  seen = seen || { obj: [], mapped: [] };\n\n  if (isObj) {\n    seenIndex = seen.obj.indexOf(obj);\n\n    if (isObj && seenIndex !== -1) {\n      // Prefer the mapped object if there is one.\n      return seen.mapped[seenIndex] || seen.obj[seenIndex];\n    }\n\n    seen.obj.push(obj);\n    seenIndex = seen.obj.length - 1;\n  }\n\n  if (isObj) {\n    for (k in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, k)) {\n        keys.push(k);\n      }\n    }\n  } else if (isArray) {\n    for (i = 0; i < obj.length; ++i) {\n      keys.push(i);\n    }\n  }\n\n  var result = isObj ? {} : [];\n  var same = true;\n  for (i = 0; i < keys.length; ++i) {\n    k = keys[i];\n    v = obj[k];\n    result[k] = func(k, v, seen);\n    same = same && result[k] === obj[k];\n  }\n\n  if (isObj && !same) {\n    seen.mapped[seenIndex] = result;\n  }\n\n  return !same ? result : obj;\n}\n\nmodule.exports = traverse;\n", "//  json3.js\n//  2017-02-21\n//  Public Domain.\n//  NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n//  See http://www.JSON.org/js.html\n//  This code should be minified before deployment.\n//  See http://javascript.crockford.com/jsmin.html\n\n//  USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n//  NOT CONTROL.\n\n//  This file creates a global JSON object containing two methods: stringify\n//  and parse. This file provides the ES5 JSON capability to ES3 systems.\n//  If a project might run on IE8 or earlier, then this file should be included.\n//  This file does nothing on ES5 systems.\n\n//      JSON.stringify(value, replacer, space)\n//          value       any JavaScript value, usually an object or array.\n//          replacer    an optional parameter that determines how object\n//                      values are stringified for objects. It can be a\n//                      function or an array of strings.\n//          space       an optional parameter that specifies the indentation\n//                      of nested structures. If it is omitted, the text will\n//                      be packed without extra whitespace. If it is a number,\n//                      it will specify the number of spaces to indent at each\n//                      level. If it is a string (such as \"\\t\" or \"&nbsp;\"),\n//                      it contains the characters used to indent at each level.\n//          This method produces a JSON text from a JavaScript value.\n//          When an object value is found, if the object contains a toJSON\n//          method, its toJSON method will be called and the result will be\n//          stringified. A toJSON method does not serialize: it returns the\n//          value represented by the name/value pair that should be serialized,\n//          or undefined if nothing should be serialized. The toJSON method\n//          will be passed the key associated with the value, and this will be\n//          bound to the value.\n\n//          For example, this would serialize Dates as ISO strings.\n\n//              Date.prototype.toJSON = function (key) {\n//                  function f(n) {\n//                      // Format integers to have at least two digits.\n//                      return (n < 10)\n//                          ? \"0\" + n\n//                          : n;\n//                  }\n//                  return this.getUTCFullYear()   + \"-\" +\n//                       f(this.getUTCMonth() + 1) + \"-\" +\n//                       f(this.getUTCDate())      + \"T\" +\n//                       f(this.getUTCHours())     + \":\" +\n//                       f(this.getUTCMinutes())   + \":\" +\n//                       f(this.getUTCSeconds())   + \"Z\";\n//              };\n\n//          You can provide an optional replacer method. It will be passed the\n//          key and value of each member, with this bound to the containing\n//          object. The value that is returned from your method will be\n//          serialized. If your method returns undefined, then the member will\n//          be excluded from the serialization.\n\n//          If the replacer parameter is an array of strings, then it will be\n//          used to select the members to be serialized. It filters the results\n//          such that only members with keys listed in the replacer array are\n//          stringified.\n\n//          Values that do not have JSON representations, such as undefined or\n//          functions, will not be serialized. Such values in objects will be\n//          dropped; in arrays they will be replaced with null. You can use\n//          a replacer function to replace those with JSON values.\n\n//          JSON.stringify(undefined) returns undefined.\n\n//          The optional space parameter produces a stringification of the\n//          value that is filled with line breaks and indentation to make it\n//          easier to read.\n\n//          If the space parameter is a non-empty string, then that string will\n//          be used for indentation. If the space parameter is a number, then\n//          the indentation will be that many spaces.\n\n//          Example:\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}]);\n//          // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}], null, \"\\t\");\n//          // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n//          text = JSON.stringify([new Date()], function (key, value) {\n//              return this[key] instanceof Date\n//                  ? \"Date(\" + this[key] + \")\"\n//                  : value;\n//          });\n//          // text is '[\"Date(---current time---)\"]'\n\n//      JSON.parse(text, reviver)\n//          This method parses a JSON text to produce an object or array.\n//          It can throw a SyntaxError exception.\n//          This has been modified to use JSON-js/json_parse_state.js as the\n//          parser instead of the one built around eval found in JSON-js/json2.js\n\n//          The optional reviver parameter is a function that can filter and\n//          transform the results. It receives each of the keys and values,\n//          and its return value is used instead of the original value.\n//          If it returns what it received, then the structure is not modified.\n//          If it returns undefined then the member is deleted.\n\n//          Example:\n\n//          // Parse the text. Values that look like ISO date strings will\n//          // be converted to Date objects.\n\n//          myData = JSON.parse(text, function (key, value) {\n//              var a;\n//              if (typeof value === \"string\") {\n//                  a =\n//   /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n//                  if (a) {\n//                      return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n//                          +a[5], +a[6]));\n//                  }\n//              }\n//              return value;\n//          });\n\n//          myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n//              var d;\n//              if (typeof value === \"string\" &&\n//                      value.slice(0, 5) === \"Date(\" &&\n//                      value.slice(-1) === \")\") {\n//                  d = new Date(value.slice(5, -1));\n//                  if (d) {\n//                      return d;\n//                  }\n//              }\n//              return value;\n//          });\n\n//  This is a reference implementation. You are free to copy, modify, or\n//  redistribute.\n\n/*jslint\n  for, this\n  */\n\n/*property\n  JSON, apply, call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n  getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n  lastIndex, length, parse, prototype, push, replace, slice, stringify,\n  test, toJSON, toString, valueOf\n  */\n\nvar setupCustomJSON = function(JSON) {\n\n  var rx_one = /^[\\],:{}\\s]*$/;\n  var rx_two = /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9a-fA-F]{4})/g;\n  var rx_three = /\"[^\"\\\\\\n\\r]*\"|true|false|null|-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\n  var rx_four = /(?:^|:|,)(?:\\s*\\[)+/g;\n  var rx_escapable = /[\\\\\"\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n  var rx_dangerous = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n  function f(n) {\n    // Format integers to have at least two digits.\n    return n < 10\n      ? \"0\" + n\n      : n;\n  }\n\n  function this_value() {\n    return this.valueOf();\n  }\n\n  if (typeof Date.prototype.toJSON !== \"function\") {\n\n    Date.prototype.toJSON = function () {\n\n      return isFinite(this.valueOf())\n        ? this.getUTCFullYear() + \"-\" +\n        f(this.getUTCMonth() + 1) + \"-\" +\n        f(this.getUTCDate()) + \"T\" +\n        f(this.getUTCHours()) + \":\" +\n        f(this.getUTCMinutes()) + \":\" +\n        f(this.getUTCSeconds()) + \"Z\"\n        : null;\n    };\n\n    Boolean.prototype.toJSON = this_value;\n    Number.prototype.toJSON = this_value;\n    String.prototype.toJSON = this_value;\n  }\n\n  var gap;\n  var indent;\n  var meta;\n  var rep;\n\n\n  function quote(string) {\n\n    // If the string contains no control characters, no quote characters, and no\n    // backslash characters, then we can safely slap some quotes around it.\n    // Otherwise we must also replace the offending characters with safe escape\n    // sequences.\n\n    rx_escapable.lastIndex = 0;\n    return rx_escapable.test(string)\n      ? \"\\\"\" + string.replace(rx_escapable, function (a) {\n        var c = meta[a];\n        return typeof c === \"string\"\n          ? c\n          : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n      }) + \"\\\"\"\n    : \"\\\"\" + string + \"\\\"\";\n  }\n\n\n  function str(key, holder) {\n\n    // Produce a string from holder[key].\n\n    var i;          // The loop counter.\n    var k;          // The member key.\n    var v;          // The member value.\n    var length;\n    var mind = gap;\n    var partial;\n    var value = holder[key];\n\n    // If the value has a toJSON method, call it to obtain a replacement value.\n\n    if (value && typeof value === \"object\" &&\n        typeof value.toJSON === \"function\") {\n      value = value.toJSON(key);\n    }\n\n    // If we were called with a replacer function, then call the replacer to\n    // obtain a replacement value.\n\n    if (typeof rep === \"function\") {\n      value = rep.call(holder, key, value);\n    }\n\n    // What happens next depends on the value's type.\n\n    switch (typeof value) {\n      case \"string\":\n        return quote(value);\n\n      case \"number\":\n\n        // JSON numbers must be finite. Encode non-finite numbers as null.\n\n        return isFinite(value)\n          ? String(value)\n          : \"null\";\n\n      case \"boolean\":\n      case \"null\":\n\n        // If the value is a boolean or null, convert it to a string. Note:\n        // typeof null does not produce \"null\". The case is included here in\n        // the remote chance that this gets fixed someday.\n\n        return String(value);\n\n        // If the type is \"object\", we might be dealing with an object or an array or\n        // null.\n\n      case \"object\":\n\n        // Due to a specification blunder in ECMAScript, typeof null is \"object\",\n        // so watch out for that case.\n\n        if (!value) {\n          return \"null\";\n        }\n\n        // Make an array to hold the partial results of stringifying this object value.\n\n        gap += indent;\n        partial = [];\n\n        // Is the value an array?\n\n        if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n\n          // The value is an array. Stringify every element. Use null as a placeholder\n          // for non-JSON values.\n\n          length = value.length;\n          for (i = 0; i < length; i += 1) {\n            partial[i] = str(i, value) || \"null\";\n          }\n\n          // Join all of the elements together, separated with commas, and wrap them in\n          // brackets.\n\n          v = partial.length === 0\n            ? \"[]\"\n            : gap\n            ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\"\n            : \"[\" + partial.join(\",\") + \"]\";\n          gap = mind;\n          return v;\n        }\n\n        // If the replacer is an array, use it to select the members to be stringified.\n\n        if (rep && typeof rep === \"object\") {\n          length = rep.length;\n          for (i = 0; i < length; i += 1) {\n            if (typeof rep[i] === \"string\") {\n              k = rep[i];\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        } else {\n\n          // Otherwise, iterate through all of the keys in the object.\n\n          for (k in value) {\n            if (Object.prototype.hasOwnProperty.call(value, k)) {\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        }\n\n        // Join all of the member texts together, separated with commas,\n        // and wrap them in braces.\n\n        v = partial.length === 0\n          ? \"{}\"\n          : gap\n          ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n          : \"{\" + partial.join(\",\") + \"}\";\n        gap = mind;\n        return v;\n    }\n  }\n\n  // If the JSON object does not yet have a stringify method, give it one.\n\n  if (typeof JSON.stringify !== \"function\") {\n    meta = {    // table of character substitutions\n      \"\\b\": \"\\\\b\",\n      \"\\t\": \"\\\\t\",\n      \"\\n\": \"\\\\n\",\n      \"\\f\": \"\\\\f\",\n      \"\\r\": \"\\\\r\",\n      \"\\\"\": \"\\\\\\\"\",\n      \"\\\\\": \"\\\\\\\\\"\n    };\n    JSON.stringify = function (value, replacer, space) {\n\n      // The stringify method takes a value and an optional replacer, and an optional\n      // space parameter, and returns a JSON text. The replacer can be a function\n      // that can replace values, or an array of strings that will select the keys.\n      // A default replacer method can be provided. Use of the space parameter can\n      // produce text that is more easily readable.\n\n      var i;\n      gap = \"\";\n      indent = \"\";\n\n      // If the space parameter is a number, make an indent string containing that\n      // many spaces.\n\n      if (typeof space === \"number\") {\n        for (i = 0; i < space; i += 1) {\n          indent += \" \";\n        }\n\n        // If the space parameter is a string, it will be used as the indent string.\n\n      } else if (typeof space === \"string\") {\n        indent = space;\n      }\n\n      // If there is a replacer, it must be a function or an array.\n      // Otherwise, throw an error.\n\n      rep = replacer;\n      if (replacer && typeof replacer !== \"function\" &&\n          (typeof replacer !== \"object\" ||\n           typeof replacer.length !== \"number\")) {\n        throw new Error(\"JSON.stringify\");\n      }\n\n      // Make a fake root object containing our value under the key of \"\".\n      // Return the result of stringifying the value.\n\n      return str(\"\", {\"\": value});\n    };\n  }\n\n\n  // If the JSON object does not yet have a parse method, give it one.\n\n  if (typeof JSON.parse !== \"function\") {\n    JSON.parse = (function () {\n\n      // This function creates a JSON parse function that uses a state machine rather\n      // than the dangerous eval function to parse a JSON text.\n\n      var state;      // The state of the parser, one of\n      // 'go'         The starting state\n      // 'ok'         The final, accepting state\n      // 'firstokey'  Ready for the first key of the object or\n      //              the closing of an empty object\n      // 'okey'       Ready for the next key of the object\n      // 'colon'      Ready for the colon\n      // 'ovalue'     Ready for the value half of a key/value pair\n      // 'ocomma'     Ready for a comma or closing }\n      // 'firstavalue' Ready for the first value of an array or\n      //              an empty array\n      // 'avalue'     Ready for the next value of an array\n      // 'acomma'     Ready for a comma or closing ]\n      var stack;      // The stack, for controlling nesting.\n      var container;  // The current container object or array\n      var key;        // The current key\n      var value;      // The current value\n      var escapes = { // Escapement translation table\n        \"\\\\\": \"\\\\\",\n        \"\\\"\": \"\\\"\",\n        \"/\": \"/\",\n        \"t\": \"\\t\",\n        \"n\": \"\\n\",\n        \"r\": \"\\r\",\n        \"f\": \"\\f\",\n        \"b\": \"\\b\"\n      };\n      var string = {   // The actions for string tokens\n        go: function () {\n          state = \"ok\";\n        },\n        firstokey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        okey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var number = {   // The actions for number tokens\n        go: function () {\n          state = \"ok\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var action = {\n\n        // The action table describes the behavior of the machine. It contains an\n        // object for each token. Each object contains a method that is called when\n        // a token is matched in a state. An object will lack a method for illegal\n        // states.\n\n        \"{\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = {};\n            state = \"firstokey\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          }\n        },\n        \"}\": {\n          firstokey: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          ocomma: function () {\n            var pop = stack.pop();\n            container[key] = value;\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \"[\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = [];\n            state = \"firstavalue\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          }\n        },\n        \"]\": {\n          firstavalue: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          acomma: function () {\n            var pop = stack.pop();\n            container.push(value);\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \":\": {\n          colon: function () {\n            if (Object.hasOwnProperty.call(container, key)) {\n              throw new SyntaxError(\"Duplicate key '\" + key + \"\\\"\");\n            }\n            state = \"ovalue\";\n          }\n        },\n        \",\": {\n          ocomma: function () {\n            container[key] = value;\n            state = \"okey\";\n          },\n          acomma: function () {\n            container.push(value);\n            state = \"avalue\";\n          }\n        },\n        \"true\": {\n          go: function () {\n            value = true;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = true;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = true;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = true;\n            state = \"acomma\";\n          }\n        },\n        \"false\": {\n          go: function () {\n            value = false;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = false;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = false;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = false;\n            state = \"acomma\";\n          }\n        },\n        \"null\": {\n          go: function () {\n            value = null;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = null;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = null;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = null;\n            state = \"acomma\";\n          }\n        }\n      };\n\n      function debackslashify(text) {\n\n        // Remove and replace any backslash escapement.\n\n        return text.replace(/\\\\(?:u(.{4})|([^u]))/g, function (ignore, b, c) {\n          return b\n            ? String.fromCharCode(parseInt(b, 16))\n            : escapes[c];\n        });\n      }\n\n      return function (source, reviver) {\n\n        // A regular expression is used to extract tokens from the JSON text.\n        // The extraction process is cautious.\n\n        var result;\n        var tx = /^[\\u0020\\t\\n\\r]*(?:([,:\\[\\]{}]|true|false|null)|(-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)|\"((?:[^\\r\\n\\t\\\\\\\"]|\\\\(?:[\"\\\\\\/trnfb]|u[0-9a-fA-F]{4}))*)\")/;\n\n        // Set the starting state.\n\n        state = \"go\";\n\n        // The stack records the container, key, and state for each object or array\n        // that contains another object or array while processing nested structures.\n\n        stack = [];\n\n        // If any error occurs, we will catch it and ultimately throw a syntax error.\n\n        try {\n\n          // For each token...\n\n          while (true) {\n            result = tx.exec(source);\n            if (!result) {\n              break;\n            }\n\n            // result is the result array from matching the tokenizing regular expression.\n            //  result[0] contains everything that matched, including any initial whitespace.\n            //  result[1] contains any punctuation that was matched, or true, false, or null.\n            //  result[2] contains a matched number, still in string form.\n            //  result[3] contains a matched string, without quotes but with escapement.\n\n            if (result[1]) {\n\n              // Token: Execute the action for this state and token.\n\n              action[result[1]][state]();\n\n            } else if (result[2]) {\n\n              // Number token: Convert the number string into a number value and execute\n              // the action for this state and number.\n\n              value = +result[2];\n              number[state]();\n            } else {\n\n              // String token: Replace the escapement sequences and execute the action for\n              // this state and string.\n\n              value = debackslashify(result[3]);\n              string[state]();\n            }\n\n            // Remove the token from the string. The loop will continue as long as there\n            // are tokens. This is a slow process, but it allows the use of ^ matching,\n            // which assures that no illegal tokens slip through.\n\n            source = source.slice(result[0].length);\n          }\n\n          // If we find a state/token combination that is illegal, then the action will\n          // cause an error. We handle the error by simply changing the state.\n\n        } catch (e) {\n          state = e;\n        }\n\n        // The parsing is finished. If we are not in the final \"ok\" state, or if the\n        // remaining source contains anything except whitespace, then we did not have\n        //a well-formed JSON text.\n\n        if (state !== \"ok\" || (/[^\\u0020\\t\\n\\r]/.test(source))) {\n          throw (state instanceof SyntaxError)\n            ? state\n            : new SyntaxError(\"JSON\");\n        }\n\n        // If there is a reviver function, we recursively walk the new structure,\n        // passing each name/value pair to the reviver function for possible\n        // transformation, starting with a temporary root object that holds the current\n        // value in an empty key. If there is not a reviver function, we simply return\n        // that value.\n\n        return (typeof reviver === \"function\")\n          ? (function walk(holder, key) {\n            var k;\n            var v;\n            var val = holder[key];\n            if (val && typeof val === \"object\") {\n              for (k in value) {\n                if (Object.prototype.hasOwnProperty.call(val, k)) {\n                  v = walk(val, k);\n                  if (v !== undefined) {\n                    val[k] = v;\n                  } else {\n                    delete val[k];\n                  }\n                }\n              }\n            }\n            return reviver.call(holder, key, val);\n          }({\"\": value}, \"\"))\n        : value;\n      };\n    }());\n  }\n}\n\nmodule.exports = setupCustomJSON;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(409);\n", ""], "names": [], "sourceRoot": ""}