{"version": 3, "file": "rollbar.umd.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAiB,QAAID,IAErBD,EAAc,QAAIC,GACnB,CATD,CASGK,MAAM,KACT,O,mBCPA,SAAUC,GACR,aACKA,EAAOC,UACVD,EAAOC,QAAU,CAAC,GASpB,IAPA,IACIC,EAAMC,EADNC,EAAMJ,EAAOC,QAEbI,EAAQ,WAAY,EACpBC,EAAa,CAAC,UACdC,EAAU,wMAE0DC,MAAM,KACvEN,EAAOI,EAAWG,OAAYL,EAAIF,KAAOE,EAAIF,GAAQ,CAAC,GAC7D,KAAOC,EAASI,EAAQE,OAAYL,EAAID,KAASC,EAAID,GAAUE,EAEhE,CAfD,CAeqB,oBAAXK,OAAyBX,KAAOW,O,sBClB1C,yBACI,aAKI,EAA6B,CAAC,aAAsB,0BAAP,EAM7C,SAA0BC,GAG9B,IAAIC,EAA8B,eAC9BC,EAAyB,iCACzBC,EAA4B,8BAEhC,MAAO,CAOHC,MAAO,SAAiCC,GACpC,QAAgC,IAArBA,EAAMC,iBAAkE,IAA7BD,EAAM,mBACxD,OAAOjB,KAAKmB,WAAWF,GACpB,GAAIA,EAAMG,OAASH,EAAMG,MAAMC,MAAMP,GACxC,OAAOd,KAAKsB,YAAYL,GACrB,GAAIA,EAAMG,MACb,OAAOpB,KAAKuB,gBAAgBN,GAE5B,MAAM,IAAIO,MAAM,kCAExB,EAGAC,gBAAiB,SAA2CC,GAExD,IAA8B,IAA1BA,EAAQC,QAAQ,KAChB,MAAO,CAACD,GAGZ,IACIE,EADS,+BACMC,KAAKH,EAAQI,QAAQ,QAAS,KACjD,MAAO,CAACF,EAAM,GAAIA,EAAM,SAAMG,EAAWH,EAAM,SAAMG,EACzD,EAEAT,YAAa,SAAuCL,GAKhD,OAJeA,EAAMG,MAAMX,MAAM,MAAMuB,QAAO,SAASC,GACnD,QAASA,EAAKZ,MAAMP,EACxB,GAAGd,MAEakC,KAAI,SAASD,GACrBA,EAAKN,QAAQ,WAAa,IAE1BM,EAAOA,EAAKH,QAAQ,aAAc,QAAQA,QAAQ,+BAAgC,KAEtF,IAAIK,EAAgBF,EAAKH,QAAQ,OAAQ,IAAIA,QAAQ,eAAgB,KAIjEM,EAAWD,EAAcd,MAAM,4BAK/BgB,GAFJF,EAAgBC,EAAWD,EAAcL,QAAQM,EAAS,GAAI,IAAMD,GAEzC1B,MAAM,OAAO6B,MAAM,GAE1CC,EAAgBvC,KAAKyB,gBAAgBW,EAAWA,EAAS,GAAKC,EAAO3B,OACrE8B,EAAeH,EAAOI,KAAK,WAAQV,EACnCW,EAAW,CAAC,OAAQ,eAAef,QAAQY,EAAc,KAAO,OAAIR,EAAYQ,EAAc,GAElG,OAAO,IAAI3B,EAAW,CAClB4B,aAAcA,EACdE,SAAUA,EACVC,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAEhB,GAAGjC,KACP,EAEAuB,gBAAiB,SAA2CN,GAKxD,OAJeA,EAAMG,MAAMX,MAAM,MAAMuB,QAAO,SAASC,GACnD,OAAQA,EAAKZ,MAAMN,EACvB,GAAGf,MAEakC,KAAI,SAASD,GAMzB,GAJIA,EAAKN,QAAQ,YAAc,IAC3BM,EAAOA,EAAKH,QAAQ,mDAAoD,SAGjD,IAAvBG,EAAKN,QAAQ,OAAsC,IAAvBM,EAAKN,QAAQ,KAEzC,OAAO,IAAIf,EAAW,CAClB4B,aAAcP,IAGlB,IAAIa,EAAoB,6BACpBC,EAAUd,EAAKZ,MAAMyB,GACrBN,EAAeO,GAAWA,EAAQ,GAAKA,EAAQ,QAAKhB,EACpDQ,EAAgBvC,KAAKyB,gBAAgBQ,EAAKH,QAAQgB,EAAmB,KAEzE,OAAO,IAAIlC,EAAW,CAClB4B,aAAcA,EACdE,SAAUH,EAAc,GACxBI,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAGpB,GAAGjC,KACP,EAEAmB,WAAY,SAAsC6B,GAC9C,OAAKA,EAAE9B,YAAe8B,EAAEC,QAAQtB,QAAQ,OAAS,GAC7CqB,EAAEC,QAAQxC,MAAM,MAAMyC,OAASF,EAAE9B,WAAWT,MAAM,MAAMyC,OACjDlD,KAAKmD,YAAYH,GAChBA,EAAE5B,MAGHpB,KAAKoD,aAAaJ,GAFlBhD,KAAKqD,aAAaL,EAIjC,EAEAG,YAAa,SAAuCH,GAKhD,IAJA,IAAIM,EAAS,oCACTC,EAAQP,EAAEC,QAAQxC,MAAM,MACxB+C,EAAS,GAEJC,EAAI,EAAGC,EAAMH,EAAML,OAAQO,EAAIC,EAAKD,GAAK,EAAG,CACjD,IAAIpC,EAAQiC,EAAOzB,KAAK0B,EAAME,IAC1BpC,GACAmC,EAAOG,KAAK,IAAI/C,EAAW,CACvB8B,SAAUrB,EAAM,GAChBsB,WAAYtB,EAAM,GAClBwB,OAAQU,EAAME,KAG1B,CAEA,OAAOD,CACX,EAEAH,aAAc,SAAwCL,GAKlD,IAJA,IAAIM,EAAS,6DACTC,EAAQP,EAAE9B,WAAWT,MAAM,MAC3B+C,EAAS,GAEJC,EAAI,EAAGC,EAAMH,EAAML,OAAQO,EAAIC,EAAKD,GAAK,EAAG,CACjD,IAAIpC,EAAQiC,EAAOzB,KAAK0B,EAAME,IAC1BpC,GACAmC,EAAOG,KACH,IAAI/C,EAAW,CACX4B,aAAcnB,EAAM,SAAMU,EAC1BW,SAAUrB,EAAM,GAChBsB,WAAYtB,EAAM,GAClBwB,OAAQU,EAAME,KAI9B,CAEA,OAAOD,CACX,EAGAJ,aAAc,SAAwCnC,GAKlD,OAJeA,EAAMG,MAAMX,MAAM,MAAMuB,QAAO,SAASC,GACnD,QAASA,EAAKZ,MAAMR,KAAiCoB,EAAKZ,MAAM,oBACpE,GAAGrB,MAEakC,KAAI,SAASD,GACzB,IAMI2B,EANAvB,EAASJ,EAAKxB,MAAM,KACpB8B,EAAgBvC,KAAKyB,gBAAgBY,EAAO3B,OAC5CmD,EAAgBxB,EAAOyB,SAAW,GAClCtB,EAAeqB,EACd/B,QAAQ,iCAAkC,MAC1CA,QAAQ,aAAc,UAAOC,EAE9B8B,EAAaxC,MAAM,iBACnBuC,EAAUC,EAAa/B,QAAQ,qBAAsB,OAEzD,IAAIiC,OAAoBhC,IAAZ6B,GAAqC,8BAAZA,OACjC7B,EAAY6B,EAAQnD,MAAM,KAE9B,OAAO,IAAIG,EAAW,CAClB4B,aAAcA,EACduB,KAAMA,EACNrB,SAAUH,EAAc,GACxBI,WAAYJ,EAAc,GAC1BK,aAAcL,EAAc,GAC5BM,OAAQZ,GAEhB,GAAGjC,KACP,EAER,GAnM4D,8BAM5D,CAZA,E,oBCAA,yBACI,aAKI,EAAqB,QAAW,0BAAP,EAMzB,WAEJ,SAASgE,EAAUC,GACf,OAAQC,MAAMC,WAAWF,KAAOG,SAASH,EAC7C,CAEA,SAASI,EAAYC,GACjB,OAAOA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,UAAU,EACvD,CAEA,SAASC,EAAQC,GACb,OAAO,WACH,OAAO3E,KAAK2E,EAChB,CACJ,CAEA,IAAIC,EAAe,CAAC,gBAAiB,SAAU,WAAY,cACvDC,EAAe,CAAC,eAAgB,cAChCC,EAAc,CAAC,WAAY,eAAgB,UAC3CC,EAAa,CAAC,QACdC,EAAc,CAAC,cAEfC,EAAQL,EAAaM,OAAOL,EAAcC,EAAaC,EAAYC,GAEvE,SAASpE,EAAWuE,GAChB,GAAKA,EACL,IAAK,IAAI1B,EAAI,EAAGA,EAAIwB,EAAM/B,OAAQO,SACR1B,IAAlBoD,EAAIF,EAAMxB,KACVzD,KAAK,MAAQqE,EAAYY,EAAMxB,KAAK0B,EAAIF,EAAMxB,IAG1D,CAEA7C,EAAWwE,UAAY,CACnBC,QAAS,WACL,OAAOrF,KAAK+D,IAChB,EACAuB,QAAS,SAASC,GACd,GAA0C,mBAAtCC,OAAOJ,UAAUK,SAASC,KAAKH,GAC/B,MAAM,IAAII,UAAU,yBAExB3F,KAAK+D,KAAOwB,CAChB,EAEAK,cAAe,WACX,OAAO5F,KAAK6F,UAChB,EACAC,cAAe,SAASP,GACpB,GAAIA,aAAa3E,EACbZ,KAAK6F,WAAaN,MACf,MAAIA,aAAaC,QAGpB,MAAM,IAAIG,UAAU,+CAFpB3F,KAAK6F,WAAa,IAAIjF,EAAW2E,EAGrC,CACJ,EAEAE,SAAU,WACN,IAAI/C,EAAW1C,KAAK+F,eAAiB,GACjCpD,EAAa3C,KAAKgG,iBAAmB,GACrCpD,EAAe5C,KAAKiG,mBAAqB,GACzCzD,EAAexC,KAAKkG,mBAAqB,GAC7C,OAAIlG,KAAKmG,YACDzD,EACO,WAAaA,EAAW,IAAMC,EAAa,IAAMC,EAAe,IAEpE,UAAYD,EAAa,IAAMC,EAEtCJ,EACOA,EAAe,KAAOE,EAAW,IAAMC,EAAa,IAAMC,EAAe,IAE7EF,EAAW,IAAMC,EAAa,IAAMC,CAC/C,GAGJhC,EAAWwF,WAAa,SAAgC9B,GACpD,IAAI+B,EAAiB/B,EAAI3C,QAAQ,KAC7B2E,EAAehC,EAAIiC,YAAY,KAE/B/D,EAAe8B,EAAIG,UAAU,EAAG4B,GAChCtC,EAAOO,EAAIG,UAAU4B,EAAiB,EAAGC,GAAc7F,MAAM,KAC7D+F,EAAiBlC,EAAIG,UAAU6B,EAAe,GAElD,GAAoC,IAAhCE,EAAe7E,QAAQ,KACvB,IAAIC,EAAQ,gCAAgCC,KAAK2E,EAAgB,IAC7D9D,EAAWd,EAAM,GACjBe,EAAaf,EAAM,GACnBgB,EAAehB,EAAM,GAG7B,OAAO,IAAIhB,EAAW,CAClB4B,aAAcA,EACduB,KAAMA,QAAQhC,EACdW,SAAUA,EACVC,WAAYA,QAAcZ,EAC1Ba,aAAcA,QAAgBb,GAEtC,EAEA,IAAK,IAAI0B,EAAI,EAAGA,EAAImB,EAAa1B,OAAQO,IACrC7C,EAAWwE,UAAU,MAAQf,EAAYO,EAAanB,KAAOiB,EAAQE,EAAanB,IAClF7C,EAAWwE,UAAU,MAAQf,EAAYO,EAAanB,KAAO,SAAUkB,GACnE,OAAO,SAASY,GACZvF,KAAK2E,GAAK8B,QAAQlB,EACtB,CACH,CAJ4D,CAI1DX,EAAanB,IAGpB,IAAK,IAAIiD,EAAI,EAAGA,EAAI7B,EAAa3B,OAAQwD,IACrC9F,EAAWwE,UAAU,MAAQf,EAAYQ,EAAa6B,KAAOhC,EAAQG,EAAa6B,IAClF9F,EAAWwE,UAAU,MAAQf,EAAYQ,EAAa6B,KAAO,SAAU/B,GACnE,OAAO,SAASY,GACZ,IAAKvB,EAAUuB,GACX,MAAM,IAAII,UAAUhB,EAAI,qBAE5B3E,KAAK2E,GAAKgC,OAAOpB,EACrB,CACH,CAP4D,CAO1DV,EAAa6B,IAGpB,IAAK,IAAIE,EAAI,EAAGA,EAAI9B,EAAY5B,OAAQ0D,IACpChG,EAAWwE,UAAU,MAAQf,EAAYS,EAAY8B,KAAOlC,EAAQI,EAAY8B,IAChFhG,EAAWwE,UAAU,MAAQf,EAAYS,EAAY8B,KAAO,SAAUjC,GAClE,OAAO,SAASY,GACZvF,KAAK2E,GAAKkC,OAAOtB,EACrB,CACH,CAJ2D,CAIzDT,EAAY8B,IAGnB,OAAOhG,CACX,GAxIwC,8BAMxC,CAZA,E,6BCEA,IAAIkG,EAAI,EAAQ,KACZC,EAAU,EAAQ,KAElBC,EAAiB,CACnBC,SAAU,kBACVC,KAAM,eACNC,OAAQ,KACRC,QAAS,IACTC,SAAU,SACVC,KAAM,KAsBR,SAASC,EAAIC,EAASC,EAAWC,EAAQC,EAAYC,GACnD5H,KAAKwH,QAAUA,EACfxH,KAAKyH,UAAYA,EACjBzH,KAAK6H,IAAMH,EACX1H,KAAK2H,WAAaA,EAClB3H,KAAK4H,WAAaA,EAClB5H,KAAK8H,YAAcN,EAAQM,YAC3B9H,KAAK+H,iBAAmBC,EAAcR,EAASE,EACjD,CA0EA,SAASM,EAAcR,EAASK,GAC9B,OAAOd,EAAQkB,wBAAwBT,EAASR,EAAgBa,EAClE,CArEAN,EAAInC,UAAU8C,SAAW,SAAUC,EAAMC,GACvC,IAAIL,EAAmBhB,EAAQgB,iBAC7B/H,KAAK+H,iBACL,QAEEM,EAAUtB,EAAQuB,aAAatI,KAAK8H,YAAaK,EAAMnI,KAAK4H,YAC5DW,EAAOvI,KAGXwI,YAAW,WACTD,EAAKd,UAAUgB,KAAKF,EAAKT,YAAaC,EAAkBM,EAASD,EACnE,GAAG,EACL,EAOAb,EAAInC,UAAUsD,iBAAmB,SAAUP,EAAMC,GAC/C,IAEIO,EAFAN,EAAUtB,EAAQuB,aAAatI,KAAK8H,YAAaK,EAAMnI,KAAK4H,YAShE,OALEe,EADE3I,KAAK2H,WACW3H,KAAK2H,WAAWiB,SAASP,GAEzBvB,EAAE+B,UAAUR,IAGZpH,OACdmH,GACFA,EAASO,EAAgB1H,OAEpB,MAGF0H,EAAgBG,KACzB,EAOAvB,EAAInC,UAAU2D,gBAAkB,SAAUC,EAAaZ,GACrD,IAAIL,EAAmBhB,EAAQgB,iBAC7B/H,KAAK+H,iBACL,QAEF/H,KAAKyH,UAAUsB,gBACb/I,KAAK8H,YACLC,EACAiB,EACAZ,EAEJ,EAEAb,EAAInC,UAAU6D,UAAY,SAAUzB,GAClC,IAAI0B,EAAalJ,KAAKkJ,WAMtB,OALAlJ,KAAKwH,QAAUV,EAAEqC,MAAMD,EAAY1B,GACnCxH,KAAK+H,iBAAmBC,EAAchI,KAAKwH,QAASxH,KAAK6H,UACxB9F,IAA7B/B,KAAKwH,QAAQM,cACf9H,KAAK8H,YAAc9H,KAAKwH,QAAQM,aAE3B9H,IACT,EAMAH,EAAOD,QAAU2H,C,6BCrHjB,IAAIT,EAAI,EAAQ,KAsGhBjH,EAAOD,QAAU,CACf0I,aArGF,SAAsBR,EAAaK,EAAMP,GACvC,IAAKd,EAAEsC,OAAOjB,EAAKkB,QAAS,UAAW,CACrC,IAAIC,EAAgBxC,EAAE+B,UAAUV,EAAKkB,QAASzB,GAC1C0B,EAAcrI,MAChBkH,EAAKkB,QAAU,uCAEflB,EAAKkB,QAAUC,EAAcR,OAAS,GAEpCX,EAAKkB,QAAQnG,OAAS,MACxBiF,EAAKkB,QAAUlB,EAAKkB,QAAQE,OAAO,EAAG,KAE1C,CACA,MAAO,CACLC,aAAc1B,EACdK,KAAMA,EAEV,EAsFEF,wBApFF,SAAiCT,EAASiC,EAAU5B,GAClD,IAAIZ,EAAWwC,EAASxC,SACpBI,EAAWoC,EAASpC,SACpBC,EAAOmC,EAASnC,KAChBJ,EAAOuC,EAASvC,KAChBC,EAASsC,EAAStC,OAClBuC,EAAUlC,EAAQkC,QAClBjC,EAuBN,SAAyBD,GACvB,IAAImC,EACgB,oBAAVhJ,QAAyBA,QACjB,oBAAR4H,MAAuBA,KAC7Bd,EAAYD,EAAQoC,kBAAoB,MAG5C,YAF6B,IAAlBD,EAAQE,QAAuBpC,EAAY,YAChB,IAA3BkC,EAAQG,iBAAgCrC,EAAY,SACxDA,CACT,CA/BkBsC,CAAgBvC,GAE5BwC,EAAQxC,EAAQwC,MACpB,GAAIxC,EAAQyC,SAAU,CACpB,IAAIC,EAAOrC,EAAI7G,MAAMwG,EAAQyC,UAC7BhD,EAAWiD,EAAKjD,SAChBI,EAAW6C,EAAK7C,SAChBC,EAAO4C,EAAK5C,KACZJ,EAAOgD,EAAKC,SACZhD,EAAS+C,EAAK/C,MAChB,CACA,MAAO,CACLuC,QAASA,EACTzC,SAAUA,EACVI,SAAUA,EACVC,KAAMA,EACNJ,KAAMA,EACNC,OAAQA,EACR6C,MAAOA,EACPvC,UAAWA,EAEf,EAyDEM,iBA7CF,SAA0BN,EAAWrH,GACnC,IAAIiH,EAAWI,EAAUJ,UAAY,SACjCC,EACFG,EAAUH,OACI,UAAbD,EAAuB,GAAkB,WAAbA,EAAwB,SAAMtF,GACzDkF,EAAWQ,EAAUR,SACrBC,EAAOO,EAAUP,KACjBwC,EAAUjC,EAAUiC,QACpBU,EAAe3C,EAAUA,UAU7B,OATIA,EAAUN,SACZD,GAAcO,EAAUN,QAEtBM,EAAUuC,QACZ9C,EAAOG,EAAW,KAAOJ,EAAWC,EACpCD,EAAWQ,EAAUuC,MAAMK,MAAQ5C,EAAUuC,MAAM/C,SACnDK,EAAOG,EAAUuC,MAAM1C,KACvBD,EAAWI,EAAUuC,MAAM3C,UAAYA,GAElC,CACLqC,QAASA,EACTrC,SAAUA,EACVJ,SAAUA,EACVC,KAAMA,EACNI,KAAMA,EACNlH,OAAQA,EACRqH,UAAW2C,EAEf,EAmBEE,iBAjBF,SAA0BC,EAAMrD,GAC9B,IAAIsD,EAAoB,MAAMC,KAAKF,GAC/BG,EAAqB,MAAMD,KAAKvD,GAQpC,OANIsD,GAAqBE,EACvBxD,EAAOA,EAAKzC,UAAU,GACZ+F,GAAsBE,IAChCxD,EAAO,IAAMA,GAGRqD,EAAOrD,CAChB,E,6BCpGA,IAAIyD,EAAU,EAAQ,KAElBnD,EAA6B,oBAAX7G,QAA2BA,OAAOiK,eACpDC,EAAQrD,GAAWA,EAAQsD,aAAe,UAC1CC,EAAiC,oBAAXpK,QAA2BA,OAAOkK,IAA0C,mBAAzBlK,OAAOkK,GAAOG,aAAoDjJ,IAA3BpB,OAAOkK,GAAOG,SAMlI,GAJuB,oBAAXrK,QAA4BA,OAAOsK,oBAC7CtK,OAAOsK,mBAAoB,IAAKC,MAAQC,YAGrCJ,GAAevD,EAAS,CAC3B,IAAI4D,EAAU,IAAIT,EAAQnD,GAC1B7G,OAAOkK,GAASO,CAClB,KAA6B,oBAAXzK,QAChBA,OAAOgK,QAAUA,EACjBhK,OAAO0K,iBAAkB,GACA,oBAAT9C,OAChBA,KAAKoC,QAAUA,EACfpC,KAAK8C,iBAAkB,GAGzBxL,EAAOD,QAAU+K,C,6BCrBjB,IAAIW,EAAS,EAAQ,KACjBxE,EAAI,EAAQ,KACZyE,EAAM,EAAQ,KACdC,EAAS,EAAQ,KACjBC,EAAU,EAAQ,KAElBC,EAAY,EAAQ,KACpBhE,EAAS,EAAQ,KAEjBiE,EAAa,EAAQ,KACrBC,EAAmB,EAAQ,KAC3BC,EAAa,EAAQ,KACrBC,EAAmB,EAAQ,IAC3BC,EAAc,EAAQ,KAE1B,SAASX,EAAQ5D,EAASwE,GACxBhM,KAAKwH,QAAUV,EAAEmF,cAAcjF,EAAgBQ,EAAS,KAAMgE,GAC9DxL,KAAKwH,QAAQ0E,mBAAqB1E,EAClC,IAAI2E,EAAYnM,KAAKoM,WAAWC,UAC5BC,EAAetM,KAAKoM,WAAWG,aAC/BC,EAAexM,KAAKoM,WAAWI,aACnCxM,KAAKyM,YAAczM,KAAKoM,WAAWK,YACnCzM,KAAK0M,MAAQ1M,KAAKoM,WAAWM,MAC7B,IAAI/E,EAAa3H,KAAKoM,WAAWzE,WAE7BF,EAAY,IAAIiE,EAAU/D,GAC1BgF,EAAM,IAAIpB,EAAIvL,KAAKwH,QAASC,EAAWC,EAAQC,GAC/CwE,IACFnM,KAAKqM,UAAY,IAAIF,EAAUnM,KAAKwH,UAEtCxH,KAAKgM,OACHA,GAAU,IAAIV,EAAOtL,KAAKwH,QAASmF,EAAKnB,EAAQxL,KAAKqM,UAAW,WAClE,IAAI1C,EAAUiD,IACVC,EAA+B,oBAAZC,UAA2BA,SAClD9M,KAAK+M,SAAWpD,EAAQqD,QAAUrD,EAAQqD,OAAOC,QACjDjN,KAAKkN,uBAAyB,EA2ehC,SAAiCC,EAAUxC,EAAShB,GAClDwD,EACGC,aAAazB,EAAW0B,oBACxBD,aAAazB,EAAW2B,qBACxBF,aAAazB,EAAW4B,6BACxBH,aAAazB,EAAW6B,aACxBJ,aAAazB,EAAW8B,eAAe9D,IACvCyD,aAAazB,EAAW+B,cAAc/D,IACtCyD,aAAazB,EAAWgC,cAAchE,IACtCyD,aAAazB,EAAWiC,SACxBR,aAAaxB,EAAiBiC,qBAC9BT,aAAaxB,EAAiBkC,kBAC9BV,aAAaxB,EAAiBmC,oBAC9BX,aAAazB,EAAWqC,YAAYrD,EAAQ+B,QAC5CU,aAAaxB,EAAiBqC,mBAC9Bb,aAAaxB,EAAiBsC,cAAc1C,IAC5C4B,aAAaxB,EAAiBuC,sBAC9Bf,aAAaxB,EAAiBwC,mBAC9BhB,aAAaxB,EAAiByC,cACnC,CA7fEC,CAAwBtO,KAAKgM,OAAOmB,SAAUnN,KAAM2J,GAC/B3J,KAAKgM,OAAOuC,MAggB9BC,aAAa1C,EAAiB2C,YAC9BD,aAAa3C,EAAW6C,aACxBF,aAAa1C,EAAiB6C,gBAAgBnD,IAC9CgD,aAAa1C,EAAiB8C,oBAAoBpD,IAClDgD,aAAa1C,EAAiB+C,gBAAgBrD,IAC9CgD,aAAa1C,EAAiBgD,iBAAiBtD,IApgBlDxL,KAAK+O,wBACDzC,IACFtM,KAAKuM,aAAe,IAAID,EACtBtM,KAAKwH,QACLxH,KAAKgM,OAAOK,UACZrM,KACA2J,EACAkD,GAEF7M,KAAKuM,aAAayC,cAEpBlI,EAAEmI,UAAUzC,GAGZxM,KAAK2K,QAAU3K,IACjB,CAEA,IAAIkP,EAAY,KAehB,SAASC,EAAoBC,GAC3B,IAAInM,EAAU,6BACduI,EAAOvK,MAAMgC,GACTmM,GACFA,EAAc,IAAI5N,MAAMyB,GAE5B,CA2eA,SAASoM,EAAkBtL,GACzB,IAAK,IAAIN,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,IAAOD,EAC5C,GAAIqD,EAAEwI,WAAWvL,EAAKN,IACpB,OAAOM,EAAKN,EAIlB,CAEA,SAASmJ,IACP,MACoB,oBAAVjM,QAAyBA,QACjB,oBAAR4H,MAAuBA,IAEnC,CA7gBA6C,EAAQmE,KAAO,SAAU/H,EAASwE,GAChC,OAAIkD,EACKA,EAAUjP,OAAOuH,GAASyB,UAAUzB,GAE7C0H,EAAY,IAAI9D,EAAQ5D,EAASwE,EAEnC,EAEAZ,EAAQhG,UAAUgH,WAAa,CAAC,EAEhChB,EAAQoE,cAAgB,SAAUpD,GAChChB,EAAQhG,UAAUgH,WAAaA,CACjC,EAUAhB,EAAQhG,UAAUnF,OAAS,SAAUuH,GAEnC,OADAxH,KAAKgM,OAAO/L,OAAOuH,GACZxH,IACT,EACAoL,EAAQnL,OAAS,SAAUuH,GACzB,GAAI0H,EACF,OAAOA,EAAUjP,OAAOuH,GAExB2H,GAEJ,EAEA/D,EAAQhG,UAAU6D,UAAY,SAAUzB,EAASiI,GAC/C,IAAIvG,EAAalJ,KAAKwH,QAClBa,EAAU,CAAC,EAaf,OAZIoH,IACFpH,EAAU,CAAEA,QAASoH,IAEvBzP,KAAKwH,QAAUV,EAAEmF,cAAc/C,EAAY1B,EAASa,EAASmD,GAC7DxL,KAAKwH,QAAQ0E,mBAAqBpF,EAAEmF,cAClC/C,EAAWgD,mBACX1E,EACAa,GAEFrI,KAAKgM,OAAO/C,UAAUjJ,KAAKwH,QAASiI,GACpCzP,KAAKuM,cAAgBvM,KAAKuM,aAAatD,UAAUjJ,KAAKwH,SACtDxH,KAAK+O,wBACE/O,IACT,EACAoL,EAAQnC,UAAY,SAAUzB,EAASiI,GACrC,GAAIP,EACF,OAAOA,EAAUjG,UAAUzB,EAASiI,GAEpCN,GAEJ,EAEA/D,EAAQhG,UAAUsK,UAAY,WAC5B,OAAO1P,KAAKgM,OAAO0D,SACrB,EACAtE,EAAQsE,UAAY,WAClB,GAAIR,EACF,OAAOA,EAAUQ,YAEjBP,GAEJ,EAEA/D,EAAQhG,UAAUuK,IAAM,WACtB,IAAIC,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAO2D,IAAIC,GACT,CAAEG,KAAMA,EACjB,EACA3E,EAAQuE,IAAM,WACZ,GAAIT,EACF,OAAOA,EAAUS,IAAIK,MAAMd,EAAWY,WAGtCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAU6K,MAAQ,WACxB,IAAIL,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAOiE,MAAML,GACX,CAAEG,KAAMA,EACjB,EACA3E,EAAQ6E,MAAQ,WACd,GAAIf,EACF,OAAOA,EAAUe,MAAMD,MAAMd,EAAWY,WAGxCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAU8K,KAAO,WACvB,IAAIN,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAOkE,KAAKN,GACV,CAAEG,KAAMA,EACjB,EACA3E,EAAQ8E,KAAO,WACb,GAAIhB,EACF,OAAOA,EAAUgB,KAAKF,MAAMd,EAAWY,WAGvCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAU+K,KAAO,WACvB,IAAIP,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAOmE,KAAKP,GACV,CAAEG,KAAMA,EACjB,EACA3E,EAAQ+E,KAAO,WACb,GAAIjB,EACF,OAAOA,EAAUiB,KAAKH,MAAMd,EAAWY,WAGvCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAUgL,QAAU,WAC1B,IAAIR,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAOoE,QAAQR,GACb,CAAEG,KAAMA,EACjB,EACA3E,EAAQgF,QAAU,WAChB,GAAIlB,EACF,OAAOA,EAAUkB,QAAQJ,MAAMd,EAAWY,WAG1CX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAUnE,MAAQ,WACxB,IAAI2O,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAO/K,MAAM2O,GACX,CAAEG,KAAMA,EACjB,EACA3E,EAAQnK,MAAQ,WACd,GAAIiO,EACF,OAAOA,EAAUjO,MAAM+O,MAAMd,EAAWY,WAGxCX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAUiL,SAAW,WAC3B,IAAIT,EAAO5P,KAAK6P,YAAYC,WACxBC,EAAOH,EAAKG,KAEhB,OADA/P,KAAKgM,OAAOqE,SAAST,GACd,CAAEG,KAAMA,EACjB,EACA3E,EAAQiF,SAAW,WACjB,GAAInB,EACF,OAAOA,EAAUmB,SAASL,MAAMd,EAAWY,WAG3CX,EADoBE,EAAkBS,WAG1C,EAEA1E,EAAQhG,UAAUsD,iBAAmB,SAAUkH,GAC7C,OAAO5P,KAAKgM,OAAOtD,iBAAiBkH,EACtC,EACAxE,EAAQ1C,iBAAmB,WACzB,GAAIwG,EACF,OAAOA,EAAUxG,iBAAiBsH,MAAMd,EAAWY,WAEnDX,GAEJ,EAEA/D,EAAQhG,UAAUkL,gBAAkB,SAAUtH,GAC5C,OAAOhJ,KAAKgM,OAAOsE,gBAAgBtH,EACrC,EACAoC,EAAQkF,gBAAkB,WACxB,GAAIpB,EACF,OAAOA,EAAUoB,gBAAgBN,MAAMd,EAAWY,WAElDX,GAEJ,EAEA/D,EAAQhG,UAAU2J,sBAAwB,WACxC,IAAIpF,EAAUiD,IAET5M,KAAKuQ,iCACJvQ,KAAKwH,QAAQgJ,iBAAmBxQ,KAAKwH,QAAQiJ,4BAC/ChF,EAAQiF,0BAA0B/G,EAAS3J,MACvCA,KAAKyM,aAAezM,KAAKwH,QAAQmJ,yBACnC3Q,KAAKyM,YAAY9C,EAAS3J,MAE5BA,KAAKuQ,gCAAiC,GAGrCvQ,KAAK4Q,iCAEN5Q,KAAKwH,QAAQqJ,4BACb7Q,KAAKwH,QAAQsJ,6BAEbrF,EAAQoF,2BAA2BlH,EAAS3J,MAC5CA,KAAK4Q,gCAAiC,EAG5C,EAEAxF,EAAQhG,UAAU2L,wBAA0B,SAC1C9N,EACA4E,EACAmJ,EACAC,EACAhQ,EACAoI,GAEA,GAAKrJ,KAAKwH,QAAQgJ,iBAAoBxQ,KAAKwH,QAAQiJ,yBAAnD,CAQA,GACEzQ,KAAKwH,QAAQ0J,wBACblR,KAAK+M,UACK,OAAV9L,GACQ,KAAR4G,EAEA,MAAO,YAGT,IAAI+H,EACAuB,EAAYrK,EAAEsK,uBAChBnO,EACA4E,EACAmJ,EACAC,EACAhQ,EACA,UACA,qBACA8K,GAEEjF,EAAEuK,QAAQpQ,IACZ2O,EAAO5P,KAAK6P,YAAY,CAAC5M,EAAShC,EAAOoI,KACpCiI,oBAAsBH,EAClBrK,EAAEuK,QAAQxJ,IACnB+H,EAAO5P,KAAK6P,YAAY,CAAC5M,EAAS4E,EAAKwB,KAClCiI,oBAAsBH,GAE3BvB,EAAO5P,KAAK6P,YAAY,CAAC5M,EAASoG,KAC7B8H,UAAYA,EAEnBvB,EAAK2B,MAAQvR,KAAKwH,QAAQgK,mBAC1B5B,EAAK6B,aAAc,EACnBzR,KAAKgM,OAAO2D,IAAIC,EAtChB,CAuCF,EAcAxE,EAAQhG,UAAUsM,sBAAwB,WACxC,GAAK1R,KAAKwH,QAAQ0J,wBAA2BlR,KAAK+M,SAAlD,CAIA,IAAI4E,EAAI3R,KAkCR,IACEwB,MAAMoQ,kBAlCR,SAA2B3Q,EAAO4Q,GAEhC,GAAIF,EAAEnK,QAAQ0J,wBACRS,EAAEzE,uBAAwB,CAQ5B,GAFAyE,EAAEzE,wBAA0B,GAEvBjM,EAIH,OAIFA,EAAM6Q,cAAe,EAKrBH,EAAEZ,wBAAwB9P,EAAMgC,QAAS,KAAM,KAAM,KAAMhC,EAC7D,CAIF,OAAOA,EAAMG,KACf,CAKA,CAAE,MAAO4B,GACPhD,KAAKwH,QAAQ0J,wBAAyB,EACtClR,KAAKiB,MAAM,iCAAkC+B,EAC/C,CAzCA,CA0CF,EAEAoI,EAAQhG,UAAU2M,yBAA2B,SAAUC,EAAQC,GAC7D,GACGjS,KAAKwH,QAAQqJ,4BACb7Q,KAAKwH,QAAQsJ,0BAFhB,CAOA,IAAI7N,EAAU,6CACd,GAAI+O,EACF,GAAIA,EAAO/O,QACTA,EAAU+O,EAAO/O,YACZ,CACL,IAAIiP,EAAepL,EAAE+B,UAAUmJ,GAC3BE,EAAapJ,QACf7F,EAAUiP,EAAapJ,MAE3B,CAEF,IAGI8G,EAHAvG,EACD2I,GAAUA,EAAOG,iBAAqBF,GAAWA,EAAQE,gBAGxDrL,EAAEuK,QAAQW,GACZpC,EAAO5P,KAAK6P,YAAY,CAAC5M,EAAS+O,EAAQ3I,KAE1CuG,EAAO5P,KAAK6P,YAAY,CAAC5M,EAAS+O,EAAQ3I,KACrC8H,UAAYrK,EAAEsK,uBACjBnO,EACA,GACA,EACA,EACA,KACA,qBACA,GACA8I,GAGJ6D,EAAK2B,MAAQvR,KAAKwH,QAAQgK,mBAC1B5B,EAAK6B,aAAc,EACnB7B,EAAKwC,cAAgBxC,EAAKwC,eAAiB,GAC3CxC,EAAKwC,cAAczO,KAAKsO,GACxBjS,KAAKgM,OAAO2D,IAAIC,EApChB,CAqCF,EAEAxE,EAAQhG,UAAUiN,KAAO,SAAUC,EAAGjJ,EAASkJ,GAC7C,IACE,IAAIC,EASJ,GAPEA,EADE1L,EAAEwI,WAAWjG,GACPA,EAEA,WACN,OAAOA,GAAW,CAAC,CACrB,GAGGvC,EAAEwI,WAAWgD,GAChB,OAAOA,EAGT,GAAIA,EAAEG,QACJ,OAAOH,EAGT,IAAKA,EAAEI,mBACLJ,EAAEI,iBAAmB,WACfH,GAAWzL,EAAEwI,WAAWiD,IAC1BA,EAAQvC,MAAMhQ,KAAM8P,WAEtB,IACE,OAAOwC,EAAEtC,MAAMhQ,KAAM8P,UACvB,CAAE,MAAO6C,GACP,IAAI3P,EAAI2P,EAUR,MATI3P,GAAKrC,OAAOiS,uBAAyB5P,IACnC8D,EAAEsC,OAAOpG,EAAG,YACdA,EAAI,IAAI6D,OAAO7D,IAEjBA,EAAEmP,gBAAkBK,KAAW,CAAC,EAChCxP,EAAEmP,gBAAgBU,eAAiBP,EAAE7M,WAErC9E,OAAOiS,qBAAuB5P,GAE1BA,CACR,CACF,EAEAsP,EAAEI,iBAAiBD,SAAU,EAEzBH,EAAEQ,gBACJ,IAAK,IAAI3S,KAAQmS,EACXA,EAAEQ,eAAe3S,IAAkB,qBAATA,IAC5BmS,EAAEI,iBAAiBvS,GAAQmS,EAAEnS,IAMrC,OAAOmS,EAAEI,gBACX,CAAE,MAAO1P,GAEP,OAAOsP,CACT,CACF,EACAlH,EAAQiH,KAAO,SAAUC,EAAGjJ,GAC1B,GAAI6F,EACF,OAAOA,EAAUmD,KAAKC,EAAGjJ,GAEzB8F,GAEJ,EAEA/D,EAAQhG,UAAU2N,aAAe,WAC/B,IAAIC,EAAQlM,EAAEmM,qBAAqBnD,WACnC,OAAO9P,KAAKgM,OAAO+G,aAAaC,EAAME,KAAMF,EAAMG,SAAUH,EAAMzB,MACpE,EACAnG,EAAQ2H,aAAe,WACrB,GAAI7D,EACF,OAAOA,EAAU6D,aAAa/C,MAAMd,EAAWY,WAE/CX,GAEJ,EAGA/D,EAAQhG,UAAUgO,wBAA0B,SAAUpQ,EAAGqQ,GAIvD,OAHKA,IACHA,EAAK,IAAInI,MAEJlL,KAAKgM,OAAOoH,wBAAwBC,EAC7C,EAEAjI,EAAQhG,UAAUkO,YAAc,SAAUtQ,EAAGqQ,GAI3C,OAHKA,IACHA,EAAK,IAAInI,MAEJlL,KAAKgM,OAAOsH,YAAYD,EACjC,EAmCAjI,EAAQhG,UAAUmO,SAAW,WAC3B/H,EAAO0E,KACL,sHAEJ,EAEA9E,EAAQhG,UAAUyK,YAAc,SAAU9L,GACxC,OAAO+C,EAAE0M,WAAWzP,EAAMyH,EAAQxL,KACpC,EAkBA,IAAIyJ,EAAW,EAAQ,KACnBgK,EAAc,EAAQ,KAEtBzM,EAAiB,CACnBI,QAASqC,EAASrC,QAClBqM,YAAaA,EAAYA,YACzBC,SAAUjK,EAASiK,SACnBC,YAAalK,EAASkK,YACtBnC,mBAAoB/H,EAAS+H,mBAC7BvH,SAAUR,EAASQ,SACnB2J,SAAS,EACTC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,yBAAyB,EACzBC,WAAW,EACX/C,wBAAwB,EACxBgD,uBAAuB,EACvBvD,yBAAyB,GAG3B9Q,EAAOD,QAAUwL,C,uBC5lBjBvL,EAAOD,QAAU,CACf6T,YAAa,CACX,KACA,OACA,SACA,WACA,SACA,mBACA,kBACA,wBACA,uBACA,eACA,cACA,yBACA,aACA,YACA,cACA,YACA,cACA,aACA,UACA,QACA,WACA,SACA,mBACA,qBACA,sBACA,kBACA,eACA,iBACA,QACA,SACA,SACA,MACA,OACA,OACA,OACA,gBACA,oBACA,sBACA,eACA,aACA,aACA,cACA,0BACA,SACA,YACA,WACA,UACA,SACA,eACA,kBACA,iBACA,UACA,SACA,UACA,U,uBChCJ,IAAIU,EAAY,CACdC,UAlBF,WACE,IAAIC,EACJ,GAAwB,oBAAbvH,SACT,OAAOuH,EAOT,IAJA,IAAI9O,EAAI,EACN+O,EAAMxH,SAASyH,cAAc,OAC7BC,EAAMF,EAAIG,qBAAqB,KAG7BH,EAAII,UAAY,uBAAqBnP,EAAI,2BAA0BiP,EAAI,KAG3E,OAAOjP,EAAI,EAAIA,EAAI8O,CACrB,GAMAxU,EAAOD,QAAUuU,C,uBC5BjB,SAASQ,EAAe3R,GACtB,OAAQA,EAAE4R,aAAa,SAAW,IAAIC,aACxC,CAiEA,SAASC,EAAoBC,GAC3B,IAAKA,IAASA,EAAKC,QACjB,MAAO,GAET,IAAIC,EAAM,CAACF,EAAKC,SACZD,EAAKG,IACPD,EAAItR,KAAK,IAAMoR,EAAKG,IAElBH,EAAKI,SACPF,EAAItR,KAAK,IAAMoR,EAAKI,QAAQ1S,KAAK,MAEnC,IAAK,IAAIgB,EAAI,EAAGA,EAAIsR,EAAKK,WAAWlS,OAAQO,IAC1CwR,EAAItR,KACF,IAAMoR,EAAKK,WAAW3R,GAAG4R,IAAM,KAAON,EAAKK,WAAW3R,GAAGqF,MAAQ,MAIrE,OAAOmM,EAAIxS,KAAK,GAClB,CAiBA,SAAS6S,EAAgBC,GACvB,IAAKA,IAASA,EAAKP,QACjB,OAAO,KAET,IACEQ,EACAH,EACAI,EACAhS,EAJEwR,EAAM,CAAC,EAKXA,EAAID,QAAUO,EAAKP,QAAQH,cACvBU,EAAKL,KACPD,EAAIC,GAAKK,EAAKL,KAEhBM,EAAYD,EAAKC,YACqB,iBAAdA,IACtBP,EAAIE,QAAUK,EAAU/U,MAAM,QAEhC,IAAI2U,EAAa,CAAC,OAAQ,OAAQ,QAAS,OAE3C,IADAH,EAAIG,WAAa,GACZ3R,EAAI,EAAGA,EAAI2R,EAAWlS,OAAQO,IACjC4R,EAAMD,EAAW3R,IACjBgS,EAAOF,EAAKX,aAAaS,KAEvBJ,EAAIG,WAAWzR,KAAK,CAAE0R,IAAKA,EAAKvM,MAAO2M,IAG3C,OAAOR,CACT,CAEApV,EAAOD,QAAU,CACf0V,gBAAiBA,EACjBR,oBAAqBA,EACrBY,qBAzFF,SAA8BC,GAS5B,IARA,IAKEC,EACAC,EAHEZ,EAAM,GACRvR,EAAM,EAICD,EAAIkS,EAAEzS,OAAS,EAAGO,GAAK,EAAGA,IAAK,CAGtC,GAFAmS,EAAUd,EAAoBa,EAAElS,IAChCoS,EAAcnS,EARIoS,EAQEb,EAAI/R,OAA2B0S,EAAQ1S,OACvDO,EAAIkS,EAAEzS,OAAS,GAAK2S,GAAeE,GAAgB,CACrDd,EAAIe,QAAQ,OACZ,KACF,CACAf,EAAIe,QAAQJ,GACZlS,GAAOkS,EAAQ1S,MACjB,CACA,OAAO+R,EAAIxS,KAjBK,MAkBlB,EAsEEwT,YAzGF,SAAqBV,GAInB,IAHA,IAEIW,EADAjB,EAAM,GAEDkB,EAAS,EAAGZ,GAAQY,EAHZ,GAKiB,UADhCD,EAAkBZ,EAAgBC,IACdP,QAF4BmB,IAKhDlB,EAAIe,QAAQE,GACZX,EAAOA,EAAKa,WAEd,OAAOnB,CACT,EA6FEoB,oBApHF,SAA6BC,EAAKC,GAChC,OAAID,EAAIE,OACCF,EAAIE,OAETD,GAAOA,EAAIE,iBACNF,EAAIE,iBAAiBH,EAAII,QAASJ,EAAIK,cAD/C,CAIF,EA6GEC,mBArIF,SAA4BC,EAAS3D,EAAM4D,GACzC,GAAID,EAAQ7B,QAAQH,gBAAkB3B,EAAK2B,cACzC,OAAO,EAET,IAAKiC,EACH,OAAO,EAETD,EAAUlC,EAAekC,GACzB,IAAK,IAAIpT,EAAI,EAAGA,EAAIqT,EAAS5T,OAAQO,IACnC,GAAIqT,EAASrT,KAAOoT,EAClB,OAAO,EAGX,OAAO,CACT,EAwHElC,eAAgBA,E,uBCvClB9U,EAAOD,QAAU,CACf8Q,0BApGF,SAAmC/P,EAAQoW,EAASC,GAClD,GAAKrW,EAAL,CAGA,IAAIsW,EAEJ,GAA0C,mBAA/BF,EAAQG,mBACjBD,EAAaF,EAAQG,wBAChB,GAAIvW,EAAOwW,QAAS,CAEzB,IADAF,EAAatW,EAAOwW,QACbF,EAAWC,oBAChBD,EAAaA,EAAWC,mBAE1BH,EAAQG,mBAAqBD,CAC/B,CAEAF,EAAQrF,wBAER,IAAI0F,EAAK,WACP,IAAIrT,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,UAAW,IASrD,SAA+BnP,EAAQgR,EAAG2F,EAAKvT,GACzCpD,EAAOiS,uBACJ7O,EAAK,KACRA,EAAK,GAAKpD,EAAOiS,sBAEd7O,EAAK,KACRA,EAAK,GAAKpD,EAAOiS,qBAAqBT,iBAExCxR,EAAOiS,qBAAuB,MAGhC,IAAI2E,EAAM5F,EAAEZ,wBAAwBf,MAAM2B,EAAG5N,GAEzCuT,GACFA,EAAItH,MAAMrP,EAAQoD,GAMR,cAARwT,IACF5F,EAAEzE,wBAA0B,EAEhC,CA/BIsK,CAAsB7W,EAAQoW,EAASE,EAAYlT,EACrD,EACIiT,IACFI,EAAGF,mBAAqBD,GAE1BtW,EAAOwW,QAAUC,CAtBjB,CAuBF,EA2EEvG,2BAhDF,SAAoClQ,EAAQoW,EAASC,GACnD,GAAKrW,EAAL,CAKgC,mBAAvBA,EAAO8W,aACd9W,EAAO8W,YAAYC,eAEnB/W,EAAOgX,oBAAoB,qBAAsBhX,EAAO8W,aAG1D,IAAIG,EAAmB,SAAUtB,GAC/B,IAAItE,EAAQC,EAAS4F,EACrB,IACE7F,EAASsE,EAAItE,MACf,CAAE,MAAOhP,GACPgP,OAASjQ,CACX,CACA,IACEkQ,EAAUqE,EAAIrE,OAChB,CAAE,MAAOjP,GACPiP,EAAU,yDACZ,CACA,IACE4F,EAASvB,EAAIuB,QACR7F,GAAU6F,IACb7F,EAAS6F,EAAO7F,OAChBC,EAAU4F,EAAO5F,QAErB,CAAE,MAAOjP,GAET,CACKgP,IACHA,EAAS,0DAGP+E,GAAWA,EAAQhF,0BACrBgF,EAAQhF,yBAAyBC,EAAQC,EAE7C,EACA2F,EAAiBF,cAAgBV,EACjCrW,EAAO8W,YAAcG,EACrBjX,EAAOmX,iBAAiB,qBAAsBF,EAxC9C,CAyCF,E,6BChGA,EAAQ,KACR,IAAIG,EAAY,EAAQ,KACpBjR,EAAI,EAAQ,KAkChBjH,EAAOD,QAAU,CACfqB,MAjCF,WACE,IAAI8C,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,UAAW,GACjD/L,EAAKiS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BlU,QAAQe,MAAM6F,EAAEkR,mBAAmBjU,IAEnC7D,QAAQe,MAAM+O,MAAM9P,QAAS6D,EAEjC,EA0BEmM,KAxBF,WACE,IAAInM,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,UAAW,GACjD/L,EAAKiS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BlU,QAAQgQ,KAAKpJ,EAAEkR,mBAAmBjU,IAElC7D,QAAQgQ,KAAKF,MAAM9P,QAAS6D,EAEhC,EAiBE4L,IAfF,WACE,IAAI5L,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,UAAW,GACjD/L,EAAKiS,QAAQ,YACT+B,EAAU3D,aAAe,EAC3BlU,QAAQyP,IAAI7I,EAAEkR,mBAAmBjU,IAEjC7D,QAAQyP,IAAIK,MAAM9P,QAAS6D,EAE/B,E,6BCjCA,IAAI+C,EAAI,EAAQ,KAShBjH,EAAOD,QAAU,CACf8O,YARF,SAAqBkB,EAAMqI,GACzB,OAAInR,EAAEoR,IAAID,EAAU,qCACVnR,EAAEoR,IAAItI,EAAM,4BAGxB,E,6BCPA,IAAIxE,EAAU,EAAQ,KAClBiB,EAAY,EAAQ,KACpBE,EAAe,EAAQ,KACvBC,EAAe,EAAQ,KACvBC,EAAc,EAAQ,KACtBC,EAAQ,EAAQ,KAChB/E,EAAa,EAAQ,KAEzByD,EAAQoE,cAAc,CACpBnD,UAAWA,EACXE,aAAcA,EACdC,aAAcA,EACdC,YAAaA,EACbC,MAAOA,EACP/E,WAAYA,IAGd9H,EAAOD,QAAUwL,C,6BCjBjB,IAAItE,EAAI,EAAQ,KACZqR,EAAU,EAAQ,KAClBrW,EAAU,EAAQ,KAClB4K,EAAQ,EAAQ,KAChB0L,EAAY,EAAQ,KACpBC,EAAU,EAAQ,KAElB5O,EAAW,CACb6O,SAAS,EACTC,wBAAwB,EACxBC,qBAAqB,EACrBC,uBAAuB,EACvBC,oBAAoB,EACpBC,uBAAuB,EACvBC,uBAAuB,EACvBC,qBAAqB,EACrBlJ,KAAK,EACLmJ,KAAK,EACLC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,8BAA8B,GAGhC,SAASC,EAAQC,EAAclG,GAE7B,IADA,IAAImG,EACGD,EAAalG,GAAMhQ,SACxBmW,EAAID,EAAalG,GAAMpP,SACrB,GAAGuV,EAAE,IAAMA,EAAE,EAEnB,CAkCA,SAAS/M,EAAa9E,EAAS6E,EAAW1B,EAAS2O,EAASC,GAC1DvZ,KAAKwH,QAAUA,EACf,IAAIgS,EAAiBhS,EAAQgS,gBACL,IAApBhS,EAAQqM,UAAwC,IAAnB2F,EAC/BxZ,KAAKwZ,eAAiB,CAAC,GAElB1S,EAAEsC,OAAOoQ,EAAgB,YAC5BA,EAAiB/P,GAEnBzJ,KAAKwZ,eAAiB1S,EAAEqC,MAAMM,EAAU+P,IAE1CxZ,KAAKyZ,uBAAyBjS,EAAQiS,qBACtCzZ,KAAK0Z,kBAAoBlS,EAAQkS,kBACjC1Z,KAAK2Z,qBAhCP,SAA8BlG,GAE5B,IADA,IAAImG,EAAW,GACNnW,EAAI,EAAGA,EAAIgQ,EAAYvQ,SAAUO,EACxCmW,EAASjW,KAAK,IAAIkW,OAAOpG,EAAYhQ,GAAI,MAE3C,OAAO,SAAUqW,GACf,IAAIC,EAnBR,SAA6BD,GAC3B,IAAKA,IAAgBA,EAAY1E,WAC/B,OAAO,KAGT,IADA,IAAI4E,EAAQF,EAAY1E,WACfO,EAAI,EAAGA,EAAIqE,EAAM9W,SAAUyS,EAClC,GAAqB,SAAjBqE,EAAMrE,GAAGN,IACX,OAAO2E,EAAMrE,GAAG7M,MAGpB,OAAO,IACT,CAQemR,CAAoBH,GAC/B,IAAKC,EACH,OAAO,EAET,IAAK,IAAItW,EAAI,EAAGA,EAAImW,EAAS1W,SAAUO,EACrC,GAAImW,EAASnW,GAAGgH,KAAKsP,GACnB,OAAO,EAGX,OAAO,CACT,CACF,CAe8BJ,CAAqBnS,EAAQiM,aACzDzT,KAAKqM,UAAYA,EACjBrM,KAAK2K,QAAUA,EACf3K,KAAKka,WAAavP,EAAQqB,OAAOmB,SAAS+M,WAC1Cla,KAAKsZ,QAAUA,GAAW,CAAC,EAC3BtZ,KAAKuZ,UAAYA,GAAa,CAAC,EAC/BvZ,KAAKoZ,aAAe,CAClBd,QAAS,GACT3I,IAAK,GACLoJ,WAAY,GACZC,aAAc,IAEhBhZ,KAAKma,cAAgB,CACnBrB,IAAK,GACLE,aAAc,GACdoB,sBAAuB,IAGzBpa,KAAKqa,UAAYra,KAAKsZ,QAAQlX,SAC9BpC,KAAKsa,UAAYta,KAAKqa,WAAara,KAAKqa,UAAUE,IACpD,CA03BA,SAASC,EAAaC,GACpB,MAAsB,oBAARC,KAAuBD,aAAiBC,GACxD,CA13BApO,EAAalH,UAAU6D,UAAY,SAAUzB,GAC3CxH,KAAKwH,QAAUV,EAAEqC,MAAMnJ,KAAKwH,QAASA,GACrC,IAAIgS,EAAiBhS,EAAQgS,eACzBmB,EAAc7T,EAAEqC,MAAMnJ,KAAKwZ,iBACP,IAApBhS,EAAQqM,UAAwC,IAAnB2F,EAC/BxZ,KAAKwZ,eAAiB,CAAC,GAElB1S,EAAEsC,OAAOoQ,EAAgB,YAC5BA,EAAiB/P,GAEnBzJ,KAAKwZ,eAAiB1S,EAAEqC,MAAMM,EAAU+P,IAE1CxZ,KAAKgP,WAAW2L,QACqB5Y,IAAjCyF,EAAQiS,uBACVzZ,KAAKyZ,uBAAyBjS,EAAQiS,2BAEN1X,IAA9ByF,EAAQkS,oBACV1Z,KAAK0Z,kBAAoBlS,EAAQkS,kBAErC,EAGApN,EAAalH,UAAU4J,WAAa,SAAU2L,IACxC3a,KAAKwZ,eAAelB,SAAaqC,GAAeA,EAAYrC,SAG7DtY,KAAKwZ,eAAelB,SACrBqC,GACAA,EAAYrC,SAEZtY,KAAK4a,sBANL5a,KAAK6a,qBASH7a,KAAKwZ,eAAe7J,KAASgL,GAAeA,EAAYhL,KAEhD3P,KAAKwZ,eAAe7J,KAAOgL,GAAeA,EAAYhL,KAChE3P,KAAK8a,sBAFL9a,KAAK+a,qBAKH/a,KAAKwZ,eAAeV,KAAS6B,GAAeA,EAAY7B,KAEhD9Y,KAAKwZ,eAAeV,KAAO6B,GAAeA,EAAY7B,KAChE9Y,KAAKgb,kBAFLhb,KAAKib,iBAMLjb,KAAKwZ,eAAeT,YAClB4B,GAAeA,EAAY5B,YAI5B/Y,KAAKwZ,eAAeT,YACrB4B,GACAA,EAAY5B,YAEZ/Y,KAAKkb,yBANLlb,KAAKmb,wBAULnb,KAAKwZ,eAAeR,cAClB2B,GAAeA,EAAY3B,cAI5BhZ,KAAKwZ,eAAeR,cACrB2B,GACAA,EAAY3B,cAEZhZ,KAAKob,2BANLpb,KAAKqb,0BAULrb,KAAKwZ,eAAeP,uBAClB0B,GAAeA,EAAY1B,uBAI5BjZ,KAAKwZ,eAAeP,uBACrB0B,GACAA,EAAY1B,uBAEZjZ,KAAKsb,oCANLtb,KAAKub,iCAQT,EAEAjP,EAAalH,UAAUwV,oBAAsB,WAC3CzB,EAAQnZ,KAAKoZ,aAAc,UAC7B,EAEA9M,EAAalH,UAAUyV,kBAAoB,WACzC,IAAItS,EAAOvI,KAEX,SAASwb,EAASrb,EAAMsb,GAClBtb,KAAQsb,GAAO3U,EAAEwI,WAAWmM,EAAItb,KAClC2B,EAAQ2Z,EAAKtb,GAAM,SAAUub,GAC3B,OAAOnT,EAAKoC,QAAQ0H,KAAKqJ,EAC3B,GAEJ,CAEA,GAAI,mBAAoB1b,KAAKsZ,QAAS,CACpC,IAAIqC,EAAO3b,KAAKsZ,QAAQxP,eAAe1E,UACvCtD,EACE6Z,EACA,QACA,SAAUD,GACR,OAAO,SAAUtb,EAAQyH,GACvB,IAAI+T,EAAcpB,EAAa3S,GAmB/B,OAlBIf,EAAEsC,OAAOvB,EAAK,WAAa+T,KAC7B/T,EAAM+T,EAAc/T,EAAIpC,WAAaoC,EACjC7H,KAAK6b,eACP7b,KAAK6b,cAAczb,OAASA,EAC5BJ,KAAK6b,cAAchU,IAAMA,EACzB7H,KAAK6b,cAAcC,YAAc,KACjC9b,KAAK6b,cAAcE,cAAgBjV,EAAEkV,MACrChc,KAAK6b,cAAcI,YAAc,MAEjCjc,KAAK6b,cAAgB,CACnBzb,OAAQA,EACRyH,IAAKA,EACLiU,YAAa,KACbC,cAAejV,EAAEkV,MACjBC,YAAa,OAIZP,EAAK1L,MAAMhQ,KAAM8P,UAC1B,CACF,GACA9P,KAAKoZ,aACL,WAGFtX,EACE6Z,EACA,oBACA,SAAUD,GACR,OAAO,SAAUQ,EAAQpT,GAiBvB,OAfK9I,KAAK6b,gBACR7b,KAAK6b,cAAgB,CAAC,GAEpB/U,EAAEsC,OAAO8S,EAAQ,WAAapV,EAAEsC,OAAON,EAAO,YAC5CP,EAAKiR,eAAef,wBACjBzY,KAAK6b,cAAcM,kBACtBnc,KAAK6b,cAAcM,gBAAkB,CAAC,GAExCnc,KAAK6b,cAAcM,gBAAgBD,GAAUpT,GAGlB,iBAAzBoT,EAAOrH,gBACT7U,KAAK6b,cAAcO,qBAAuBtT,IAGvC4S,EAAK1L,MAAMhQ,KAAM8P,UAC1B,CACF,GACA9P,KAAKoZ,aACL,WAGFtX,EACE6Z,EACA,QACA,SAAUD,GAER,OAAO,SAAUvT,GAEf,IAAIsT,EAAMzb,KAEV,SAASqc,IACP,GAAIZ,EAAII,gBACgC,OAAlCJ,EAAII,cAAcC,cACpBL,EAAII,cAAcC,YAAc,EAC5BvT,EAAKiR,eAAed,qBACtB+C,EAAII,cAAcS,QAAUnU,GAE9BsT,EAAIc,gBAAkBhU,EAAKiU,eACzBf,EAAII,cACJ,WACA9Z,IAGA0Z,EAAIgB,WAAa,IACnBhB,EAAII,cAAcE,cAAgBjV,EAAEkV,OAElCP,EAAIgB,WAAa,GAAG,CACtBhB,EAAII,cAAcI,YAAcnV,EAAEkV,MAElC,IAAI7D,EAAU,KAGd,GAFAsD,EAAII,cAAca,sBAChBjB,EAAIkB,kBAAkB,gBACpBpU,EAAKiR,eAAejB,uBAAwB,CAC9C,IAAIqE,EACFrU,EAAKiR,eAAejB,uBACtBJ,EAAU,CAAC,EACX,IACE,IAAI+D,EAAQzY,EACZ,IAAsB,IAAlBmZ,EAAwB,CAC1B,IAAIC,EAAapB,EAAIqB,wBACrB,GAAID,EAAY,CACd,IACIjb,EAAOkH,EADPiU,EAAMF,EAAWG,OAAOvc,MAAM,WAElC,IAAKgD,EAAI,EAAGA,EAAIsZ,EAAI7Z,OAAQO,IAE1ByY,GADAta,EAAQmb,EAAItZ,GAAGhD,MAAM,OACNqD,QACfgF,EAAQlH,EAAMa,KAAK,MACnB0V,EAAQ+D,GAAUpT,CAEtB,CACF,MACE,IAAKrF,EAAI,EAAGA,EAAImZ,EAAc1Z,OAAQO,IAEpC0U,EADA+D,EAASU,EAAcnZ,IACLgY,EAAIkB,kBAAkBT,EAG9C,CAAE,MAAOlZ,GAGT,CACF,CACA,IAAIia,EAAO,KACX,GAAI1U,EAAKiR,eAAehB,oBACtB,IACEyE,EAAOxB,EAAIyB,YACb,CAAE,MAAOla,GAET,CAEF,IAAIma,EAAW,MACXF,GAAQ9E,KACVgF,EAAW,CAAC,EACRF,IAEA1U,EAAK6U,kBACH3B,EAAII,cAAca,uBAGpBS,EAASF,KAAO1U,EAAK8U,UAAUJ,GAE/BE,EAASF,KAAOA,GAGhB9E,IACFgF,EAAShF,QAAUA,IAGnBgF,IACF1B,EAAII,cAAcsB,SAAWA,GAE/B,IACE,IAAIG,EAAO7B,EAAI8B,OACfD,EAAgB,OAATA,EAAgB,IAAMA,EAC7B7B,EAAII,cAAcC,YAAcwB,EAChC7B,EAAIc,gBAAgBhL,MAClBhJ,EAAK8D,UAAUmR,gBAAgBF,GACjC/U,EAAKkV,kBAAkBhC,EAAII,cAC7B,CAAE,MAAO7Y,GAET,CACF,CAEJ,CAuBA,OArBAwY,EAAS,SAAUC,GACnBD,EAAS,UAAWC,GACpBD,EAAS,aAAcC,GAGrB,uBAAwBA,GACxB3U,EAAEwI,WAAWmM,EAAIiC,oBAEjB5b,EAAQ2Z,EAAK,sBAAsB,SAAUC,GAC3C,OAAOnT,EAAKoC,QAAQ0H,KAClBqJ,OACA3Z,EACAsa,EAEJ,IAEAZ,EAAIiC,mBAAqBrB,EAEvBZ,EAAII,eAAiBtT,EAAKoV,oBAC5BlC,EAAII,cAAcza,OAAQ,IAAII,OAAQJ,OAEjCsa,EAAK1L,MAAMhQ,KAAM8P,UAC1B,CACF,GACA9P,KAAKoZ,aACL,UAEJ,CAEI,UAAWpZ,KAAKsZ,SAClBxX,EACE9B,KAAKsZ,QACL,SACA,SAAUoC,GAER,OAAO,SAAUtE,EAAIwG,GAGnB,IADA,IAAI7Z,EAAO,IAAIsT,MAAMvH,UAAU5M,QACtBO,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,EAAKD,IAC1CM,EAAKN,GAAKqM,UAAUrM,GAEtB,IAEIoE,EAFA4S,EAAQ1W,EAAK,GACb3D,EAAS,MAETwb,EAAcpB,EAAaC,GAC3B3T,EAAEsC,OAAOqR,EAAO,WAAamB,EAC/B/T,EAAM+T,EAAcnB,EAAMhV,WAAagV,EAC9BA,IACT5S,EAAM4S,EAAM5S,IACR4S,EAAMra,SACRA,EAASqa,EAAMra,SAGf2D,EAAK,IAAMA,EAAK,GAAG3D,SACrBA,EAAS2D,EAAK,GAAG3D,QAEnB,IAAI+S,EAAW,CACb/S,OAAQA,EACRyH,IAAKA,EACLiU,YAAa,KACbC,cAAejV,EAAEkV,MACjBC,YAAa,MAEf,GAAIlY,EAAK,IAAMA,EAAK,GAAGoU,QAAS,CAG9B,IAAI0F,EAAa1F,EAAQpU,EAAK,GAAGoU,SAEjChF,EAASiJ,qBAAuByB,EAAW3F,IAAI,gBAE3C3P,EAAKiR,eAAef,wBACtBtF,EAASgJ,gBAAkB5T,EAAKuV,aAC9BD,EACAtV,EAAKiR,eAAef,uBAG1B,CAoBA,OAlBIlQ,EAAKiR,eAAed,qBAClB3U,EAAK,IAAMA,EAAK,GAAGkZ,KACrB9J,EAASmJ,QAAUvY,EAAK,GAAGkZ,KAE3BlZ,EAAK,KACJ+C,EAAEsC,OAAOrF,EAAK,GAAI,WACnBA,EAAK,GAAGkZ,OAER9J,EAASmJ,QAAUvY,EAAK,GAAGkZ,OAG/B1U,EAAKiU,eAAerJ,EAAU,aAASpR,GACnCwG,EAAKoV,oBACPxK,EAAS/R,OAAQ,IAAII,OAAQJ,OAKxBsa,EAAK1L,MAAMhQ,KAAM+D,GAAMga,MAAK,SAAUC,GAC3C7K,EAAS8I,YAAcnV,EAAEkV,MACzB7I,EAAS2I,YAAckC,EAAKT,OAC5BpK,EAASuJ,sBAAwBsB,EAAK7F,QAAQD,IAAI,gBAClD,IAAIC,EAAU,KACV5P,EAAKiR,eAAejB,yBACtBJ,EAAU5P,EAAKuV,aACbE,EAAK7F,QACL5P,EAAKiR,eAAejB,yBAGxB,IAAI0E,EAAO,KAiCX,OAhCI1U,EAAKiR,eAAehB,qBACG,mBAAdwF,EAAKC,OAIdhB,EAAOe,EAAKE,QAAQD,SAGpB9F,GAAW8E,KACb9J,EAASgK,SAAW,CAAC,EACjBF,IAEuB,mBAAdA,EAAKc,KACdd,EAAKc,MAAK,SAAUE,GAEhBA,GACA1V,EAAK6U,kBAAkBjK,EAASuJ,uBAEhCvJ,EAASgK,SAASF,KAAO1U,EAAK8U,UAAUY,GAExC9K,EAASgK,SAASF,KAAOgB,CAE7B,IAEA9K,EAASgK,SAASF,KAAOA,GAGzB9E,IACFhF,EAASgK,SAAShF,QAAUA,IAGhC5P,EAAKkV,kBAAkBtK,GAChB6K,CACT,GACF,CACF,GACAhe,KAAKoZ,aACL,UAGN,EAEA9M,EAAalH,UAAUoX,eAAiB,SACtCrJ,EACAgL,EACAC,GAQA,OALEjL,EAASmJ,SACTtc,KAAKod,kBAAkBjK,EAASiJ,wBAEhCjJ,EAASmJ,QAAUtc,KAAKqd,UAAUlK,EAASmJ,UAEtCtc,KAAKqM,UAAUmQ,eAAerJ,EAAUgL,EAASC,EAC1D,EAEA9R,EAAalH,UAAUgY,kBAAoB,SAAUiB,GACnD,SAAOA,GACLvX,EAAEsC,OAAOiV,EAAa,WACtBA,EAAYxJ,cAAcyJ,SAAS,QAGvC,EAEAhS,EAAalH,UAAUiY,UAAY,SAAUkB,GAC3C,OAAOC,KAAK3V,UAAU6D,EAAM8R,KAAKxd,MAAMud,GAAOve,KAAKwH,QAAQiM,aAC7D,EAEAnH,EAAalH,UAAU0Y,aAAe,SAAUW,EAAW7B,GACzD,IAAI8B,EAAa,CAAC,EAClB,IACE,IAAIjb,EACJ,IAAsB,IAAlBmZ,GACF,GAAiC,mBAAtB6B,EAAUE,QAInB,IAFA,IAAI9B,EAAa4B,EAAUE,UACvBC,EAAgB/B,EAAWgC,QACvBD,EAAcE,MACpBJ,EAAWE,EAAc9V,MAAM,IAAM8V,EAAc9V,MAAM,GACzD8V,EAAgB/B,EAAWgC,YAI/B,IAAKpb,EAAI,EAAGA,EAAImZ,EAAc1Z,OAAQO,IAAK,CACzC,IAAIyY,EAASU,EAAcnZ,GAC3Bib,EAAWxC,GAAUuC,EAAUvG,IAAIgE,EACrC,CAEJ,CAAE,MAAOlZ,GAET,CACA,OAAO0b,CACT,EAEApS,EAAalH,UAAUuY,gBAAkB,WACvC,OACE3d,KAAKwZ,eAAeb,uBACpB3Y,KAAKwZ,eAAeZ,uBACpB5Y,KAAKwZ,eAAeX,mBAExB,EAEAvM,EAAalH,UAAUqY,kBAAoB,SAAUtK,GACnD,IAAIoK,EAASpK,EAAS2I,YAEtB,GACGyB,GAAU,KAAOvd,KAAKwZ,eAAeb,uBACrC4E,GAAU,KAAOvd,KAAKwZ,eAAeZ,uBAC1B,IAAX2E,GAAgBvd,KAAKwZ,eAAeX,oBACrC,CACA,IAAI5X,EAAQ,IAAIO,MAAM,mCAAqC+b,GAC3Dtc,EAAMG,MAAQ+R,EAAS/R,MACvBpB,KAAK2K,QAAQ1J,MAAMA,EAAO,CAAE8d,WAAY,GAC1C,CACF,EAEAzS,EAAalH,UAAU0V,oBAAsB,WAC3C,GAAM,YAAa9a,KAAKsZ,SAAWtZ,KAAKsZ,QAAQpZ,QAAQyP,IAIxD,IADA,IAAI0J,EACGrZ,KAAKoZ,aAAkB,IAAElW,QAC9BmW,EAAIrZ,KAAKoZ,aAAkB,IAAEtV,QAC7B9D,KAAKsZ,QAAQpZ,QAAQmZ,EAAE,IAAMA,EAAE,EAEnC,EAEA/M,EAAalH,UAAU2V,kBAAoB,WACzC,GAAM,YAAa/a,KAAKsZ,SAAWtZ,KAAKsZ,QAAQpZ,QAAQyP,IAAxD,CAIA,IAAIpH,EAAOvI,KACPgf,EAAIhf,KAAKsZ,QAAQpZ,QAkBjBM,EAAU,CAAC,QAAS,OAAQ,OAAQ,QAAS,OACjD,IACE,IAAK,IAAIiD,EAAI,EAAGC,EAAMlD,EAAQ0C,OAAQO,EAAIC,EAAKD,IAC7Cwb,EAAYze,EAAQiD,GAExB,CAAE,MAAOT,GACPhD,KAAKka,WAAWa,kBAAoB,CAAE9Z,MAAO+B,EAAEC,QACjD,CA5BA,CAKA,SAASgc,EAAY7e,GAGnB,IAAIsb,EAAOsD,EAAE5e,GACT8e,EAAcF,EACdzN,EAAmB,SAAXnR,EAAoB,UAAYA,EAC5C4e,EAAE5e,GAAU,WACV,IAAI2D,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,WAClC7M,EAAU6D,EAAEkR,mBAAmBjU,GACnCwE,EAAK8D,UAAU8S,WAAWlc,EAASsO,GAC/BmK,GACF0D,SAASha,UAAU4K,MAAMtK,KAAKgW,EAAMwD,EAAanb,EAErD,EACAwE,EAAK6Q,aAAkB,IAAEzV,KAAK,CAACvD,EAAQsb,GACzC,CASF,EAEApP,EAAalH,UAAU4V,gBAAkB,YACjC,qBAAsBhb,KAAKsZ,SAAW,gBAAiBtZ,KAAKsZ,UAGlEtZ,KAAKqf,gBAAgB,MACvB,EAEA/S,EAAalH,UAAU6V,cAAgB,WACrC,GAAM,qBAAsBjb,KAAKsZ,SAAW,gBAAiBtZ,KAAKsZ,QAAlE,CAGA,IAAIgG,EAAetf,KAAKuf,YAAYC,KAAKxf,MACrCyf,EAAczf,KAAK0f,WAAWF,KAAKxf,MACvCA,KAAK2f,YAAY,MAAO3f,KAAKsZ,QAAS,QAAS,UAAWgG,GAAc,GACxEtf,KAAK2f,YACH,MACA3f,KAAKsZ,QACL,OACA,aACAmG,GACA,EAVF,CAYF,EAEAnT,EAAalH,UAAUma,YAAc,SAAUjJ,GAC7C,IACE,IAAItT,EAAIqV,EAAQhC,oBAAoBC,EAAKtW,KAAKuZ,WAC1CqG,EAAS5c,GAAKA,EAAEgS,QAChB6K,EACFxH,EAAQzB,mBAAmB5T,EAAG,MAC9BqV,EAAQzB,mBAAmB5T,EAAG,UAE9B4c,IACCC,GACCxH,EAAQzB,mBAAmB5T,EAAG,QAAS,CAAC,SAAU,YAEpDhD,KAAK8f,gBAAgB,QAAS9c,GACrBqV,EAAQzB,mBAAmB5T,EAAG,QAAS,CAAC,WAAY,WAC7DhD,KAAK8f,gBAAgB,QAAS9c,EAAGA,EAAE8F,MAAO9F,EAAE+c,QAEhD,CAAE,MAAOpN,GAET,CACF,EAEArG,EAAalH,UAAUsa,WAAa,SAAUpJ,GAC5C,IACE,IAAItT,EAAIqV,EAAQhC,oBAAoBC,EAAKtW,KAAKuZ,WAC1CvW,GAAKA,EAAEgS,UACLqD,EAAQzB,mBAAmB5T,EAAG,YAChChD,KAAK8f,gBAAgB,QAAS9c,EAAGA,EAAE8F,OAEnCuP,EAAQzB,mBAAmB5T,EAAG,WAC9BA,EAAEwE,SACFxE,EAAEwE,QAAQtE,OAEVlD,KAAKggB,yBAAyBhd,GAE9BqV,EAAQzB,mBAAmB5T,EAAG,WAC7BqV,EAAQzB,mBAAmB5T,EAAG,QAAS,CACtC,SACA,SACA,SACA,WACA,WAGFhD,KAAK8f,gBAAgB,QAAS9c,EAAGA,EAAE8F,OAGzC,CAAE,MAAO6J,GAET,CACF,EAEArG,EAAalH,UAAU4a,yBAA2B,SAAUzK,GAC1D,GAAIA,EAAK0K,SACP,IAAK,IAAIxc,EAAI,EAAGA,EAAI8R,EAAK/N,QAAQtE,OAAQO,IACnC8R,EAAK/N,QAAQ/D,GAAGyc,UAClBlgB,KAAK8f,gBAAgB,QAASvK,EAAMA,EAAK/N,QAAQ/D,GAAGqF,YAG/CyM,EAAK4K,eAAiB,GAAK5K,EAAK/N,QAAQ+N,EAAK4K,gBACtDngB,KAAK8f,gBAAgB,QAASvK,EAAMA,EAAK/N,QAAQ+N,EAAK4K,eAAerX,MAEzE,EAEAwD,EAAalH,UAAU0a,gBAAkB,SACvC3B,EACAtH,EACA/N,EACAsX,GAEA,QAAcre,IAAV+G,EACF,GACE9I,KAAKyZ,sBAC+B,aAApCpB,EAAQ1D,eAAekC,GAEvB/N,EAAQ,iBACH,CACL,IAAIgR,EAAczB,EAAQ/C,gBAAgBuB,GACtC7W,KAAK0Z,kBACH1Z,KAAK0Z,kBAAkBI,KACzBhR,EAAQ,cAED9I,KAAK2Z,qBAAqBG,KACnChR,EAAQ,aAEZ,CAEF,IAAIuX,EAAgBhI,EAAQ3C,qBAC1B2C,EAAQpC,YAAYY,IAEtB7W,KAAKqM,UAAUiU,WAAWnC,EAASkC,EAAevX,EAAOsX,EAC3D,EAEA9T,EAAalH,UAAU8V,uBAAyB,WAC9C,IAAIlO,EAAShN,KAAKsZ,QAAQtM,SACFA,GAAUA,EAAOuT,KAAOvT,EAAOuT,IAAItT,UAIzDjN,KAAKsZ,QAAQkH,SACbxgB,KAAKsZ,QAAQkH,QAAQC,WAIvBtH,EAAQnZ,KAAKoZ,aAAc,aAC7B,EAEA9M,EAAalH,UAAU+V,qBAAuB,WAC5C,IAAInO,EAAShN,KAAKsZ,QAAQtM,OAO1B,KANwBA,GAAUA,EAAOuT,KAAOvT,EAAOuT,IAAItT,UAIzDjN,KAAKsZ,QAAQkH,SACbxgB,KAAKsZ,QAAQkH,QAAQC,UACvB,CAGA,IAAIlY,EAAOvI,KACX8B,EACE9B,KAAKsZ,QACL,cACA,SAAUoC,GACR,OAAO,WACL,IAAIgF,EAAUnY,EAAK8R,UAAUE,KAC7BhS,EAAKoY,gBAAgBpY,EAAK+R,UAAWoG,GACjChF,GACFA,EAAK1L,MAAMhQ,KAAM8P,UAErB,CACF,GACA9P,KAAKoZ,aACL,cAGFtX,EACE9B,KAAKsZ,QAAQkH,QACb,aACA,SAAU9E,GACR,OAAO,WACL,IAAI7T,EAAMiI,UAAU5M,OAAS,EAAI4M,UAAU,QAAK/N,EAIhD,OAHI8F,GACFU,EAAKoY,gBAAgBpY,EAAK+R,UAAWzS,EAAM,IAEtC6T,EAAK1L,MAAMhQ,KAAM8P,UAC1B,CACF,GACA9P,KAAKoZ,aACL,aA/BF,CAiCF,EAEA9M,EAAalH,UAAUub,gBAAkB,SAAUC,EAAMC,GACvD,IAAIC,EAAa1I,EAAUpX,MAAMhB,KAAKqa,UAAUE,MAC5CwG,EAAW3I,EAAUpX,MAAM6f,GAC3BG,EAAa5I,EAAUpX,MAAM4f,GACjC5gB,KAAKsa,UAAYuG,EAEfC,EAAWzZ,WAAa0Z,EAAS1Z,UACjCyZ,EAAWzW,OAAS0W,EAAS1W,OAE7BwW,EAAKE,EAAS7Z,MAAQ6Z,EAASE,MAAQ,KAGvCH,EAAWzZ,WAAa2Z,EAAW3Z,UACnCyZ,EAAWzW,OAAS2W,EAAW3W,OAE/BuW,EAAOI,EAAW9Z,MAAQ8Z,EAAWC,MAAQ,KAE/CjhB,KAAKqM,UAAU6U,kBAAkBN,EAAMC,EACzC,EAEAvU,EAAalH,UAAUgW,yBAA2B,YAC1C,qBAAsBpb,KAAKsZ,SAAW,SAAUtZ,KAAKuZ,aAGvDvZ,KAAKsZ,QAAQxB,iBACf9X,KAAKqf,gBAAgB,gBAErBlG,EAAQnZ,KAAKoZ,aAAc,gBAE/B,EAEA9M,EAAalH,UAAUiW,uBAAyB,WAC9C,GAAM,qBAAsBrb,KAAKsZ,SAAW,SAAUtZ,KAAKuZ,UAG3D,GAAIvZ,KAAKsZ,QAAQxB,iBACf9X,KAAK2f,YACH,eACA3f,KAAKsZ,QACL,cACAvX,EACA,WACE/B,KAAKqM,UAAU8U,0BAA0B,SAC3C,EAAE3B,KAAKxf,OACP,GAEFA,KAAK2f,YACH,eACA3f,KAAKsZ,QACL,eACAvX,EACA,WACE/B,KAAKqM,UAAU8U,0BAA0B,UAC3C,EAAE3B,KAAKxf,OACP,OAEG,CACL,IAAIuI,EAAOvI,KACX8B,EACE9B,KAAKuZ,UAAU0D,KACf,YACA,SAAUvB,GACR,OAAO,WACLnT,EAAK8D,UAAU8U,0BAA0B,UACrCzF,GACFA,EAAK1L,MAAMhQ,KAAM8P,UAErB,CACF,GACA9P,KAAKoZ,aACL,gBAEFtX,EACE9B,KAAKuZ,UAAU0D,KACf,aACA,SAAUvB,GACR,OAAO,WACLnT,EAAK8D,UAAU8U,0BAA0B,WACrCzF,GACFA,EAAK1L,MAAMhQ,KAAM8P,UAErB,CACF,GACA9P,KAAKoZ,aACL,eAEJ,CACF,EAEA9M,EAAalH,UAAUgc,eAAiB,SAAUC,GAChD,IAAIpe,EACF,0CAEAoe,EAASC,WAFT,wBAKAD,EAASE,kBALT,yBAQAF,EAASG,mBACT,KAEEH,EAASI,aACXxe,GACE,aACAoe,EAASI,WADT,WAIAJ,EAAS1e,WAJT,UAOA0e,EAASze,aACT,MAGJK,GAAW,mBAAqBoe,EAASK,eAEzC1hB,KAAKqM,UAAU8S,WAAWlc,EAAS,SACnCjD,KAAK2hB,eAAe1e,EACtB,EAEAqJ,EAAalH,UAAUuc,eAAiB,SAAU1e,GAC5CjD,KAAKwZ,eAAeN,8BACtBlZ,KAAK2K,QAAQ1J,MAAMgC,EAEvB,EAEAqJ,EAAalH,UAAUkW,kCAAoC,WACnD,qBAAsBtb,KAAKuZ,WAIjCvZ,KAAKqf,gBAAgB,wBACvB,EAEA/S,EAAalH,UAAUmW,gCAAkC,WACvD,GAAM,qBAAsBvb,KAAKuZ,UAAjC,CAIA,IAAIqI,EAAa5hB,KAAKohB,eAAe5B,KAAKxf,MAC1CA,KAAK2f,YACH,wBACA3f,KAAKuZ,UACL,0BACA,KACAqI,GACA,EATF,CAWF,EAEAtV,EAAalH,UAAUua,YAAc,SACnCkC,EACA1c,EACA+N,EACA4O,EACA/K,EACAgL,GAEI5c,EAAI2S,kBACN3S,EAAI2S,iBAAiB5E,EAAM6D,EAASgL,GACpC/hB,KAAKma,cAAc0H,GAASle,MAAK,WAC/BwB,EAAIwS,oBAAoBzE,EAAM6D,EAASgL,EACzC,KACSD,IACT3c,EAAI6c,YAAYF,EAAS/K,GACzB/W,KAAKma,cAAc0H,GAASle,MAAK,WAC/BwB,EAAI8c,YAAYH,EAAS/K,EAC3B,IAEJ,EAEAzK,EAAalH,UAAUia,gBAAkB,SAAUwC,GAEjD,KAAO7hB,KAAKma,cAAc0H,GAAS3e,QAC7BlD,KAAKma,cAAc0H,GAAS/d,OAChC6N,EAEJ,EAMA9R,EAAOD,QAAU0M,C,6BC/9BjB,IAAIxF,EAAI,EAAQ,KACZiF,EAAc,EAAQ,KACtBP,EAAS,EAAQ,KAkKrB,SAAS0W,EAAetS,EAAMpI,EAASY,GACrC,IAAInF,EAAU2M,EAAK3M,QACfkf,EAASvS,EAAKuS,OAEblf,IACHA,EAAU,6CAEZ,IAAIO,EAAS,CACXyZ,KAAMha,GAGJkf,IACF3e,EAAO4e,MAAQtb,EAAEqC,MAAMgZ,IAGzBrb,EAAEub,IAAIzS,EAAM,YAAa,CAAE3M,QAASO,IACpC4E,EAAS,KAAMwH,EACjB,CAEA,SAAS0S,EAAc1S,GAErB,IAAIxO,EAAQwO,EAAKuB,UAAU/P,MAS3B,OAPEA,GACiB,IAAjBA,EAAM8B,QACN0M,EAAK0B,qBACL1B,EAAK0B,oBAAoBlQ,QAEzBA,EAAQwO,EAAK0B,oBAAoBlQ,OAE5BA,CACT,CAkCA,SAASmhB,EAAW3S,EAAMuB,EAAW3J,GACnC,IAAIsS,EAAclK,GAAQA,EAAKzH,KAAK2R,YAChCqI,EAASvS,GAAQA,EAAKuS,OACtB/gB,EAAQkhB,EAAc1S,GAEtB4S,EAAQzW,EAAY0W,gBAAgBtR,EAAUlO,SAG9Cyf,EAAQ,CACVC,UAAW,CACTC,MAJYC,EAAW1R,EAAWqR,EAAM,GAAIhb,GAK5CvE,QAJUuf,EAAM,KAYpB,GAJI1I,IACF4I,EAAMC,UAAU7I,YAAcA,GAG5B1Y,EAAO,CAKT,IAAI0hB,EACAC,EACAzF,EACA0F,EACAva,EACAwa,EACAxf,EAAGyf,EAGP,IAbqB,IAAjB9hB,EAAM8B,SACRwf,EAAMC,UAAUvhB,MAAQ+P,EAAUgS,SAClCT,EAAMC,UAAUS,IAAMvc,OAAOsK,EAAUkS,eAUzCX,EAAMY,OAAS,GACV7f,EAAI,EAAGA,EAAIrC,EAAM8B,SAAUO,EAE9Bsf,EAAQ,CACNQ,UAFFT,EAAa1hB,EAAMqC,IAEIoE,IAAMf,EAAE0c,YAAYV,EAAWjb,KAAO,YAC3DmJ,OAAQ8R,EAAW7gB,MAAQ,KAC3B7B,OACG0iB,EAAWW,MAA4B,MAApBX,EAAWW,KAE3BX,EAAWW,KADX,cAENxS,MAAO6R,EAAWY,QAEhBlc,EAAQmc,eACVZ,EAAMlb,IAAMib,EAAWjb,KAGvBkb,EAAM3iB,QACN2iB,EAAM3iB,OAAOwjB,UACbb,EAAM3iB,OAAOwjB,SAAS,sBAKxBtG,EAAO0F,EAAMva,EAAO,MACpBwa,EAAgBH,EAAWzZ,QAAUyZ,EAAWzZ,QAAQnG,OAAS,KAE/DggB,EAAMW,KAAKC,MAAMb,EAAgB,GACjCD,EAAMF,EAAWzZ,QAAQ/G,MAAM,EAAG4gB,GAClC5F,EAAOwF,EAAWzZ,QAAQ6Z,GAC1Bza,EAAOqa,EAAWzZ,QAAQ/G,MAAM4gB,IAG9B5F,IACFyF,EAAMzF,KAAOA,IAGX0F,GAAOva,KACTsa,EAAM1Z,QAAU,CAAC,EACb2Z,GAAOA,EAAI9f,SACb6f,EAAM1Z,QAAQ2Z,IAAMA,GAElBva,GAAQA,EAAKvF,SACf6f,EAAM1Z,QAAQZ,KAAOA,IAIrBqa,EAAW/e,OACbgf,EAAMhf,KAAO+e,EAAW/e,MAG1B2e,EAAMY,OAAO3f,KAAKof,IAIpBL,EAAMY,OAAOS,UAET5B,IACFO,EAAMN,MAAQtb,EAAEqC,MAAMgZ,GAE1B,CAEA,OAAOO,CACT,CAEA,SAASG,EAAW1R,EAAWqR,EAAOhb,GACpC,OAAI2J,EAAU4I,KACL5I,EAAU4I,KACRvS,EAAQib,gBACVD,EAEA,WAEX,CAaA3iB,EAAOD,QAAU,CACfyN,mBAvVF,SAA4BuC,EAAMpI,EAASY,GACzC,GAAIwH,EAAKoU,KAA4C,iBAArCjY,EAAYkY,MAAMrU,EAAKoU,KAAKjK,KAAyB,CACnE,IAAImK,EAAgB,IAAI1iB,MACxB0iB,EAAcnK,KAAOnK,EAAKoU,IAAIjK,KAC9BmK,EAAcjhB,QAAU2M,EAAKoU,IAAI/gB,QACjCihB,EAAc9iB,MAAQwO,EAAKoU,IAAI5iB,MAC/B8iB,EAAcC,OAASvU,EAAKoU,IAC5BpU,EAAKoU,IAAME,CACb,CACA9b,EAAS,KAAMwH,EACjB,EA8UEtC,oBA5UF,SAA6BsC,EAAMpI,EAASY,GAE1C,GADAwH,EAAKzH,KAAOyH,EAAKzH,MAAQ,CAAC,EACtByH,EAAKoU,IACP,IACEpU,EAAKuB,UACHvB,EAAKoU,IAAII,kBACTrY,EAAY/K,MAAM4O,EAAKoU,IAAKpU,EAAKmP,YAE/BvX,EAAQ6c,iBAoBlB,SAAyBzU,GACvB,IAAI0U,EAAQ,GACRN,EAAMpU,EAAKoU,IAIf,IAFAM,EAAM3gB,KAAKqgB,GAEJA,EAAIG,QAAUH,EAAIO,OACvBP,EAAMA,EAAIG,QAAUH,EAAIO,MACxBD,EAAM3gB,KAAKqgB,GAGbld,EAAEud,gBAAgBzU,EAAM0U,EAC1B,CA/BQD,CAAgBzU,EAEpB,CAAE,MAAO5M,GACPwI,EAAOvK,MAAM,wCAAyC+B,GACtD,IACE4M,EAAK3M,QACH2M,EAAKoU,IAAI/gB,SACT2M,EAAKoU,IAAIlK,aACTlK,EAAK3M,SACL4D,OAAO+I,EAAKoU,IAChB,CAAE,MAAOQ,GACP5U,EAAK3M,QAAU4D,OAAO+I,EAAKoU,MAAQnd,OAAO2d,EAC5C,QACO5U,EAAKoU,GACd,CAEF5b,EAAS,KAAMwH,EACjB,EAmTErC,4BAnSF,SAAqCqC,EAAMpI,EAASY,GAC7CwH,EAAK3M,SAAY2M,EAAKuB,WAAcvB,EAAKuS,QAC5C/Z,EAAS,IAAI5G,MAAM,0CAA2C,MAEhE4G,EAAS,KAAMwH,EACjB,EA+REpC,YA7RF,SAAqBoC,EAAMpI,EAASY,GAClC,IAAIqc,EACDjd,EAAQa,SAAWb,EAAQa,QAAQoc,aAAgBjd,EAAQid,YAC9D7U,EAAKzH,KAAOrB,EAAEqC,MAAMyG,EAAKzH,KAAM,CAC7Bsc,YAAaA,EACblT,MAAO3B,EAAK2B,MACZtH,SAAUzC,EAAQyC,SAClBya,SAAU,UACVC,UAAW,aACXC,SAAU,aACVC,OAAQ,CAAC,EACT9U,KAAMH,EAAKG,KACX5C,SAAU,CACR4M,KAAM,qBACN3S,QAASI,EAAQJ,SAEnB+a,OAAQvS,EAAKuS,SAEf/Z,EAAS,KAAMwH,EACjB,EA2QEnC,eAzQF,SAAwB9M,GACtB,OAAO,SAAUiP,EAAMpI,EAASY,GAC9B,IAAI0c,EAAc,CAAC,EAEfnkB,GAAUA,EAAOyB,WACnB0iB,EAAYjd,IAAMlH,EAAOyB,SAASmY,KAClCuK,EAAYC,aAAepkB,EAAOyB,SAAS+E,QAG7C,IAAI6d,EAAe,aACdxd,EAAQyM,WAEoB,IAAtBzM,EAAQyM,YACjB+Q,GAAgB,cAFhBA,EAAe,KAIbA,IAAcF,EAAYG,QAAUD,GAEpCxf,OAAO0f,KAAKJ,GAAa5hB,OAAS,GACpC4D,EAAEub,IAAIzS,EAAM,eAAgBkV,GAG9B1c,EAAS,KAAMwH,EACjB,CACF,EAmPElC,cAjPF,SAAuB/M,GACrB,OAAO,SAAUiP,EAAMpI,EAASY,GAC9B,IAAKzH,EACH,OAAOyH,EAAS,KAAMwH,GAExB,IAAIuV,EAAMxkB,EAAOykB,WAAa,CAAC,EAC3BC,EAAM1kB,EAAO2kB,QAAU,CAAC,EAC5Bxe,EAAEub,IAAIzS,EAAM,cAAe,CACzB2V,WAAY3V,EAAK4V,UAAY7kB,EAAOsK,kBACpCua,UAAW3B,KAAK4B,MAAM7V,EAAK4V,UAAY,KACvCE,WAAY,CACVC,QAASR,EAAIS,UACbhB,SAAUO,EAAIP,SACdiB,eAAgBV,EAAIW,cACpBR,OAAQ,CACNS,MAAOV,EAAIU,MACX5P,OAAQkP,EAAIlP,WAIlB/N,EAAS,KAAMwH,EACjB,CACF,EA4NEjC,cA1NF,SAAuBhN,GACrB,OAAO,SAAUiP,EAAMpI,EAASY,GAC9B,IAAKzH,IAAWA,EAAOykB,UACrB,OAAOhd,EAAS,KAAMwH,GAKxB,IAHA,IAEIoW,EAFAC,EAAU,GACVC,EAAavlB,EAAOykB,UAAUa,SAAW,GAEpCxiB,EAAI,EAAG0iB,EAAID,EAAWhjB,OAAQO,EAAI0iB,IAAK1iB,EAC9CuiB,EAAME,EAAWziB,GACjBwiB,EAAQtiB,KAAK,CAAEoW,KAAMiM,EAAIjM,KAAMD,YAAakM,EAAIlM,cAElDhT,EAAEub,IAAIzS,EAAM,iCAAkCqW,GAC9C7d,EAAS,KAAMwH,EACjB,CACF,EA4MEhC,QA1MF,SAAiBgC,EAAMpI,EAASY,GAC1BwH,EAAKuB,UACHvB,EAAKuB,UAAUiV,WA2CvB,SAA2BxW,EAAMpI,EAASY,GAKxC,IAJA,IAAIge,EAAaxW,EAAKuB,UAAUiV,WAC5BC,EAAS,GAETC,EAAmBF,EAAWljB,OACzBO,EAAI,EAAGA,EAAI6iB,EAAkB7iB,IAAK,CACzC,IAAIif,EAAQH,EAAW3S,EAAMwW,EAAW3iB,GAAI+D,GAC5C6e,EAAO1iB,KAAK+e,EACd,CAEA5b,EAAEub,IAAIzS,EAAM,YAAa,CAAE2W,YAAaF,IACxCje,EAAS,KAAMwH,EACjB,CAtDM4W,CAAkB5W,EAAMpI,EAASY,GAwDvC,SAAsBwH,EAAMpI,EAASY,GACnC,IAAIhH,EAAQkhB,EAAc1S,GAE1B,GAAIxO,EAAO,CACT,IAAIshB,EAAQH,EAAW3S,EAAMA,EAAKuB,UAAW3J,GAC7CV,EAAEub,IAAIzS,EAAM,YAAa,CAAE8S,MAAOA,IAClCta,EAAS,KAAMwH,EACjB,KAAO,CACL,IAAIuB,EAAYvB,EAAKuB,UACjBqR,EAAQzW,EAAY0W,gBAAgBtR,EAAUlO,SAC9CuS,EAAYqN,EAAW1R,EAAWqR,EAAM,GAAIhb,GAC5CvE,EAAUuf,EAAM,GAEpB5S,EAAK3M,QAAUuS,EAAY,KAAOvS,EAClCif,EAAetS,EAAMpI,EAASY,EAChC,CACF,CAtEMqe,CAAa7W,EAAMpI,EAASY,GAG9B8Z,EAAetS,EAAMpI,EAASY,EAElC,EAiME4F,YApBF,SAAqB0Y,GACnB,OAAO,SAAU9W,EAAMpI,EAASY,GAC9B,GAAIse,EAAS,CACX,IAAIjT,EAAcjM,EAAQiM,aAAe,GACrCkT,EAAanf,EAAQmf,YAAc,GACvC/W,EAAKzH,KAAOue,EAAQ9W,EAAKzH,KAAMsL,EAAakT,EAC9C,CACAve,EAAS,KAAMwH,EACjB,CACF,E,6BCxVA,IAAI9I,EAAI,EAAQ,KACZ8f,EAAmB,EAAQ,KAC3BC,EAAiB,EAAQ,IAoB7B,SAASnb,EAAU/D,GACjB3H,KAAK2H,WAAaA,CACpB,CAEA+D,EAAUtG,UAAU8S,IAAM,SACxBpQ,EACAN,EACAsf,EACA1e,EACA2e,GAEK3e,GAAatB,EAAEwI,WAAWlH,KAC7BA,EAAW,WAAa,GAE1BtB,EAAEkgB,8BAA8Blf,EAAaN,EAASsf,GAEtD,IACIjf,EAAMf,EAAEmgB,UAAUzf,GACtBxH,KAAKknB,iBACHpf,EACAD,EAJW,MAMX,KACAO,EACA2e,EACAvf,EAAQkC,QACRlC,EAAQC,UAEZ,EAEAiE,EAAUtG,UAAUqD,KAAO,SACzBX,EACAN,EACAa,EACAD,EACA2e,GAMA,GAJK3e,GAAatB,EAAEwI,WAAWlH,KAC7BA,EAAW,WAAa,IAGrBC,EACH,OAAOD,EAAS,IAAI5G,MAAM,8BAG5B,IAAImH,EAMJ,IAJEA,EADE3I,KAAK2H,WACW3H,KAAK2H,WAAWiB,SAASP,GAEzBvB,EAAE+B,UAAUR,IAEZpH,MAClB,OAAOmH,EAASO,EAAgB1H,OAGlC,IAAIkmB,EAAYxe,EAAgBG,MAE5BjB,EAAMf,EAAEmgB,UAAUzf,GACtBxH,KAAKknB,iBACHpf,EACAD,EAJW,OAMXsf,EACA/e,EACA2e,EACAvf,EAAQkC,QACRlC,EAAQC,UAEZ,EAEAiE,EAAUtG,UAAU2D,gBAAkB,SACpCjB,EACAN,EACAwB,EACAZ,EACA2e,GAEK3e,GAAatB,EAAEwI,WAAWlH,KAC7BA,EAAW,WAAa,GAG1B,IACIP,EAAMf,EAAEmgB,UAAUzf,GACtBxH,KAAKknB,iBACHpf,EACAD,EAJW,OAMXmB,EACAZ,EACA2e,EACAvf,EAAQkC,QACRlC,EAAQC,UAEZ,EAMAiE,EAAUtG,UAAU8hB,iBAAmB,WACrC,IAAIvd,EACgB,oBAAVhJ,QAAyBA,aACjB,IAAR4H,GAAuBA,EAC7B6e,EAAczd,GAAWA,EAAQ0d,MAAQ1d,EAAQ0d,KAAK3G,QACtD3c,EAAOsT,MAAMjS,UAAU9C,MAAMoD,KAAKoK,WAEtC,GAAIsX,GAAqC,YAAtBA,EAAYE,MAAqB,CAClD,IAAIC,EAAWH,EAAYI,QACvBjf,EAAOvI,KACXunB,EAASE,KAAI,WACXlf,EAAKmf,aAAa1X,WAAMjO,EAAWgC,EACrC,GACF,MACE/D,KAAK0nB,aAAa1X,WAAMjO,EAAWgC,EAEvC,EAEA2H,EAAUtG,UAAUsiB,aAAe,SACjC5f,EACAD,EACAzH,EACA+H,EACAC,EACA2e,EACArd,EACAjC,GAEA,GAA4B,oBAAjBkgB,aACT,OAmBJ,SAAuBpJ,EAAMnW,IACR,IAAIuf,cACVrX,gBACXiO,GACA,SAAUqJ,GAEV,IACA,SAAU5D,GACR5b,EAAS,IAAI5G,MAAMwiB,GACrB,GAEJ,CA9BW6D,CAAc1f,EAAMC,GAGX,UAAdX,EACFmf,EAAiB9e,EAAaD,EAAKzH,EAAQ+H,EAAMC,EAAUsB,GAE3Dmd,EACE/e,EACAD,EACAzH,EACA+H,EACAC,EACA2e,EACArd,EAGN,EAgBA7J,EAAOD,QAAU8L,C,6BCtLjB,IAAIF,EAAS,EAAQ,KACjB1E,EAAI,EAAQ,KAmChBjH,EAAOD,QAjCP,SAA0BkI,EAAaD,EAAKzH,EAAQ+H,EAAMC,EAAUsB,GAClE,IAAIoe,EACAC,EAEAjhB,EAAEkhB,eAAete,KACnBoe,EAAa,IAAIG,gBACjBF,EAAYvf,YAAW,WACrBsf,EAAWI,OACb,GAAGxe,IAGLG,MAAMhC,EAAK,CACTzH,OAAQA,EACR+X,QAAS,CACP,eAAgB,mBAChB,yBAA0BrQ,EAC1BqgB,OAAQL,GAAcA,EAAWK,QAEnClL,KAAM9U,IAEL4V,MAAK,SAAUZ,GAEd,OADI4K,GAAWK,aAAaL,GACrB5K,EAASoB,MAClB,IACCR,MAAK,SAAU5V,GACdC,EAAS,KAAMD,EACjB,IACCkgB,OAAM,SAAUpnB,GACfuK,EAAOvK,MAAMA,EAAMgC,SACnBmF,EAASnH,EACX,GACJ,C,4BChCA,IAAI6F,EAAI,EAAQ,KACZ0E,EAAS,EAAQ,KAqKrB,SAAS8c,EAAmBrlB,EAASqa,GACnC,IAAI0G,EAAM,IAAIxiB,MAAMyB,GAEpB,OADA+gB,EAAI1G,KAAOA,GAAQ,YACZ0G,CACT,CAEAnkB,EAAOD,QAzKP,SACEkI,EACAD,EACAzH,EACA+H,EACAC,EACA2e,EACArd,GAEA,IAAI4S,EAMJ,KAJEA,EADEyK,EACQA,IA+Gd,WAGE,IAcIwB,EACA9kB,EAfA+kB,EAAY,CACd,WACE,OAAO,IAAI1e,cACb,EACA,WACE,OAAO,IAAI2e,cAAc,iBAC3B,EACA,WACE,OAAO,IAAIA,cAAc,iBAC3B,EACA,WACE,OAAO,IAAIA,cAAc,oBAC3B,GAIEC,EAAeF,EAAUtlB,OAC7B,IAAKO,EAAI,EAAGA,EAAIilB,EAAcjlB,IAE5B,IACE8kB,EAAUC,EAAU/kB,KACpB,KACF,CAAE,MAAOT,GAET,CAGF,OAAOulB,CACT,CA5IcI,IAIV,OAAOvgB,EAAS,IAAI5G,MAAM,6BAE5B,IACE,IACE,IAAIkc,EAAqB,WACvB,IACE,GAAIA,GAA6C,IAAvBpB,EAAQG,WAAkB,CAClDiB,OAAqB3b,EAErB,IAAI6mB,EAAgB9hB,EAAE+hB,UAAUvM,EAAQY,cACxC,IAgIQvL,EAhIO2K,IAiIb3K,EAAE4L,QAAuB,MAAb5L,EAAE4L,OA/Hd,YADAnV,EAASwgB,EAAc3nB,MAAO2nB,EAAc9f,OAEvC,GAiInB,SAA0B6I,GACxB,OAAOA,GAAK7K,EAAEsC,OAAOuI,EAAE4L,OAAQ,WAAa5L,EAAE4L,QAAU,KAAO5L,EAAE4L,OAAS,GAC5E,CAnIuBuL,CAAiBxM,GAAU,CACpC,GAAuB,MAAnBA,EAAQiB,OAAgB,CAE1B,IAAIta,EACF2lB,EAAc9f,OAAS8f,EAAc9f,MAAM7F,QAC7CuI,EAAOvK,MAAMgC,EACf,CAEAmF,EAAS,IAAI5G,MAAMqF,OAAOyV,EAAQiB,SACpC,MAMEnV,EAASkgB,EADP,+DAGN,CACF,CAAE,MAAOS,GAIP,IAAIpW,EAEFA,EADEoW,GAAMA,EAAG3nB,MACL2nB,EAEA,IAAIvnB,MAAMunB,GAElB3gB,EAASuK,EACX,CAgGR,IAAoBhB,CA/Fd,EAEA2K,EAAQ0M,KAAK5oB,EAAQyH,GAAK,GACtByU,EAAQ2M,mBACV3M,EAAQ2M,iBAAiB,eAAgB,oBACzC3M,EAAQ2M,iBAAiB,yBAA0BnhB,IAGjDhB,EAAEkhB,eAAete,KACnB4S,EAAQ5S,QAAUA,GAGpB4S,EAAQoB,mBAAqBA,EAC7BpB,EAAQ4M,KAAK/gB,EACf,CAAE,MAAOghB,GAEP,GAA8B,oBAAnBC,eAAgC,CAKzC,IAAKzoB,SAAWA,OAAOyB,SACrB,OAAOgG,EACL,IAAI5G,MACF,4DAOqC,UAAzCb,OAAOyB,SAASmY,KAAK9V,UAAU,EAAG,IACV,UAAxBoD,EAAIpD,UAAU,EAAG,KAEjBoD,EAAM,OAASA,EAAIpD,UAAU,IAG/B,IAAI4kB,EAAiB,IAAID,eACzBC,EAAeC,WAAa,WAAa,EACzCD,EAAeE,UAAY,WAGzBnhB,EAASkgB,EAFC,oBACC,aAEb,EACAe,EAAelS,QAAU,WACvB/O,EAAS,IAAI5G,MAAM,wBACrB,EACA6nB,EAAeG,OAAS,WACtB,IAAIZ,EAAgB9hB,EAAE+hB,UAAUQ,EAAenM,cAC/C9U,EAASwgB,EAAc3nB,MAAO2nB,EAAc9f,MAC9C,EACAugB,EAAeL,KAAK5oB,EAAQyH,GAAK,GACjCwhB,EAAeH,KAAK/gB,EACtB,MACEC,EAAS,IAAI5G,MAAM,+CAEvB,CACF,CAAE,MAAOgjB,GACPpc,EAASoc,EACX,CACF,C,uBCzCA3kB,EAAOD,QAAU,CACfoB,MApFF,SAAe6G,GACb,IAcIpE,EAAGgmB,EAdHjmB,EAAS,CACX6D,SAAU,KACVqiB,KAAM,KACNrf,KAAM,KACNnD,KAAM,KACN+Z,KAAM,KACN1G,KAAM1S,EACNZ,SAAU,KACVK,KAAM,KACN6C,SAAU,KACVhD,OAAQ,KACRwiB,MAAO,MAmBT,IAdW,KADXlmB,EAAIoE,EAAIlG,QAAQ,QAEd6B,EAAO6D,SAAWQ,EAAIpD,UAAU,EAAGhB,GACnCgmB,EAAOhmB,EAAI,GAEXgmB,EAAO,GAIE,KADXhmB,EAAIoE,EAAIlG,QAAQ,IAAK8nB,MAEnBjmB,EAAOkmB,KAAO7hB,EAAIpD,UAAUglB,EAAMhmB,GAClCgmB,EAAOhmB,EAAI,IAIF,KADXA,EAAIoE,EAAIlG,QAAQ,IAAK8nB,IACP,CAEZ,IAAW,KADXhmB,EAAIoE,EAAIlG,QAAQ,IAAK8nB,IAcnB,OAXW,KADXhmB,EAAIoE,EAAIlG,QAAQ,IAAK8nB,IAEnBjmB,EAAO6G,KAAOxC,EAAIpD,UAAUglB,IAE5BjmB,EAAO6G,KAAOxC,EAAIpD,UAAUglB,EAAMhmB,GAClCD,EAAOyd,KAAOpZ,EAAIpD,UAAUhB,IAE9BD,EAAOyD,SAAWzD,EAAO6G,KAAK5J,MAAM,KAAK,GACzC+C,EAAO8D,KAAO9D,EAAO6G,KAAK5J,MAAM,KAAK,GACjC+C,EAAO8D,OACT9D,EAAO8D,KAAOsiB,SAASpmB,EAAO8D,KAAM,KAE/B9D,EAEPA,EAAO6G,KAAOxC,EAAIpD,UAAUglB,EAAMhmB,GAClCD,EAAOyD,SAAWzD,EAAO6G,KAAK5J,MAAM,KAAK,GACzC+C,EAAO8D,KAAO9D,EAAO6G,KAAK5J,MAAM,KAAK,GACjC+C,EAAO8D,OACT9D,EAAO8D,KAAOsiB,SAASpmB,EAAO8D,KAAM,KAEtCmiB,EAAOhmB,CAEX,MACED,EAAO6G,KAAOxC,EAAIpD,UAAUglB,EAAMhmB,GAClCD,EAAOyD,SAAWzD,EAAO6G,KAAK5J,MAAM,KAAK,GACzC+C,EAAO8D,KAAO9D,EAAO6G,KAAK5J,MAAM,KAAK,GACjC+C,EAAO8D,OACT9D,EAAO8D,KAAOsiB,SAASpmB,EAAO8D,KAAM,KAEtCmiB,EAAOhmB,EAWT,IAPW,KADXA,EAAIoE,EAAIlG,QAAQ,IAAK8nB,IAEnBjmB,EAAO0D,KAAOW,EAAIpD,UAAUglB,IAE5BjmB,EAAO0D,KAAOW,EAAIpD,UAAUglB,EAAMhmB,GAClCD,EAAOyd,KAAOpZ,EAAIpD,UAAUhB,IAG1BD,EAAO0D,KAAM,CACf,IAAI2iB,EAAYrmB,EAAO0D,KAAKzG,MAAM,KAClC+C,EAAO2G,SAAW0f,EAAU,GAC5BrmB,EAAOmmB,MAAQE,EAAU,GACzBrmB,EAAO2D,OAAS3D,EAAOmmB,MAAQ,IAAMnmB,EAAOmmB,MAAQ,IACtD,CACA,OAAOnmB,CACT,E,uBC/DA,SAASsmB,EAAyB/S,EAAS3R,EAAW4R,GACpD,GACE5R,EAAU0N,gBACV1N,EAAU0N,eAAe,oBACzB,CAEA,IADA,IAAIiX,EAAsB3kB,EAAU0S,iBAElCiS,EAAoBC,gBACpBD,EAAoBrS,eAEpBqS,EAAsBA,EAAoBC,eAE5C,IAAIC,EAAQ,SAAUjX,EAAO5K,EAAU8hB,GACrCH,EAAoBrkB,KAAK1F,KAAMgT,EAAO+D,EAAQ1E,KAAKjK,GAAW8hB,EAChE,EACAD,EAAMD,eAAiBD,EACvBE,EAAMvS,cAAgBV,EACtB5R,EAAU0S,iBAAmBmS,EAG7B,IADA,IAAIE,EAAyB/kB,EAAUuS,oBAErCwS,EAAuBC,mBACvBD,EAAuBzS,eAEvByS,EAAyBA,EAAuBC,kBAElD,IAAIC,EAAW,SAAUrX,EAAO5K,EAAU8hB,GACxCC,EAAuBzkB,KACrB1F,KACAgT,EACC5K,GAAYA,EAASsK,kBAAqBtK,EAC3C8hB,EAEJ,EACAG,EAASD,kBAAoBD,EAC7BE,EAAS3S,cAAgBV,EACzB5R,EAAUuS,oBAAsB0S,CAClC,CACF,CAEAxqB,EAAOD,QA3DP,SAAqBe,EAAQoW,EAASC,GACpC,GAAKrW,EAAL,CAIA,IAII8C,EAAGxD,EAJHwL,EACF,4YAA4YhL,MAC1Y,KAGJ,IAAKgD,EAAI,EAAGA,EAAIgI,EAAQvI,SAAUO,EAG5B9C,EAFJV,EAASwL,EAAQhI,KAEK9C,EAAOV,GAAQmF,WACnC0kB,EAAyB/S,EAASpW,EAAOV,GAAQmF,UAAW4R,EAXhE,CAcF,C,uBCjBAnX,EAAOD,QAAU,CACfwH,QAAS,SACT6C,SAAU,8BACVyJ,SAAU,QACVC,YAAa,QACbnC,mBAAoB,QACpB8Y,SAAU,EACVC,YAAa,G,6BCPf,IAAIC,EAAmB,EAAQ,KAG3BC,EAAmB,IAAI5Q,OACzB,6DAOF,SAAS6Q,IACP,OAAO,IACT,CAEA,SAASC,EAAM7H,GACb,IAAI3a,EAAO,CAAC,EAYZ,OAVAA,EAAKyiB,YAAc9H,EAEnB3a,EAAKN,IAAMib,EAAWpgB,SACtByF,EAAKlG,KAAO6gB,EAAWngB,WACvBwF,EAAKsb,KAAOX,EAAWtgB,aACvB2F,EAAKub,OAASZ,EAAWlgB,aACzBuF,EAAKpE,KAAO+e,EAAW/e,KAEvBoE,EAAKkB,QAdE,KAgBAlB,CACT,CAEA,SAAS8b,EAAMtB,EAAWkI,GAqBxB,MAAO,CACLzpB,MArBF,WACE,IAAI0pB,EAAc,GAElBD,EAAOA,GAAQ,EAEf,IACEC,EAAcN,EAAiBxpB,MAAM2hB,EACvC,CAAE,MAAO3f,GACP8nB,EAAc,EAChB,CAIA,IAFA,IAAI1pB,EAAQ,GAEHqC,EAAIonB,EAAMpnB,EAAIqnB,EAAY5nB,OAAQO,IACzCrC,EAAMuC,KAAK,IAAIgnB,EAAMG,EAAYrnB,KAGnC,OAAOrC,CACT,CAGS2pB,GACP9nB,QAAS0f,EAAU1f,QACnB8W,MA+C4B9Y,EA/CC0hB,EAgD3B5I,EAAO9Y,EAAM8Y,MAAQ9Y,EAAM8Y,KAAK7W,QAAUjC,EAAM8Y,KAChDiR,EACF/pB,EAAMgqB,YAAYlR,MAClB9Y,EAAMgqB,YAAYlR,KAAK7W,QACvBjC,EAAMgqB,YAAYlR,KAEfA,GAASiR,EAID,UAATjR,EACKiR,EAEFjR,EANEA,GAAQiR,GAtDf7H,SAAUR,EAAUvhB,MACpBiiB,aAAcV,GA6ClB,IAAgC1hB,EAC1B8Y,EACAiR,CA7CN,CA4DAnrB,EAAOD,QAAU,CACfsrB,kBAjHF,WACE,MANqB,GAOvB,EAgHEzI,gBAxCF,SAAyB0I,GACvB,IAAKA,IAAWA,EAAO9pB,MACrB,MAAO,CAAC,wDAAyD,IAEnE,IAAI+pB,EAAgBD,EAAO9pB,MAAMopB,GAC7BY,EAAW,YAUf,OARID,IACFC,EAAWD,EAAcA,EAAcloB,OAAS,GAKhDioB,GAJAA,EAASA,EAAOrpB,SACbspB,EAAcA,EAAcloB,OAAS,IAAM,IAAMmoB,EAAW,IAC7D,KAEcvpB,QAAQ,mBAAoB,KAEvC,CAACupB,EAAUF,EACpB,EAyBET,cAAeA,EACf1pB,MA9DF,SAAegC,EAAG6nB,GAChB,IAAI7G,EAAMhhB,EAEV,GAAIghB,EAAIG,QAAUH,EAAIO,MAAO,CAE3B,IADA,IAAI6B,EAAa,GACVpC,GACLoC,EAAWziB,KAAK,IAAIsgB,EAAMD,EAAK6G,IAC/B7G,EAAMA,EAAIG,QAAUH,EAAIO,MAExBsG,EAAO,EAKT,OADAzE,EAAW,GAAGA,WAAaA,EACpBA,EAAW,EACpB,CACE,OAAO,IAAInC,EAAMD,EAAK6G,EAE1B,EA6CE5G,MAAOA,EACP0G,MAAOA,E,uBC3HT,IAAIW,EAAS9lB,OAAOJ,UAAU0N,eAC1ByY,EAAQ/lB,OAAOJ,UAAUK,SAEzB+lB,EAAgB,SAAuBrmB,GACzC,IAAKA,GAA2B,oBAApBomB,EAAM7lB,KAAKP,GACrB,OAAO,EAGT,IAYIkQ,EAZAoW,EAAoBH,EAAO5lB,KAAKP,EAAK,eACrCumB,EACFvmB,EAAI8lB,aACJ9lB,EAAI8lB,YAAY7lB,WAChBkmB,EAAO5lB,KAAKP,EAAI8lB,YAAY7lB,UAAW,iBAEzC,GAAID,EAAI8lB,cAAgBQ,IAAsBC,EAC5C,OAAO,EAMT,IAAKrW,KAAOlQ,GAIZ,YAAsB,IAARkQ,GAAuBiW,EAAO5lB,KAAKP,EAAKkQ,EACxD,EAkCAxV,EAAOD,QAhCP,SAASuJ,IACP,IAAI1F,EACFkoB,EACAC,EACA1N,EACAnE,EACAvW,EAAS,CAAC,EACVkd,EAAU,KACVxd,EAAS4M,UAAU5M,OAErB,IAAKO,EAAI,EAAGA,EAAIP,EAAQO,IAEtB,GAAe,OADfid,EAAU5Q,UAAUrM,IAKpB,IAAKsW,KAAQ2G,EACXiL,EAAMnoB,EAAOuW,GAETvW,KADJooB,EAAOlL,EAAQ3G,MAET6R,GAAQJ,EAAcI,IACxB1N,EAAQyN,GAAOH,EAAcG,GAAOA,EAAM,CAAC,EAC3CnoB,EAAOuW,GAAQ5Q,EAAM+U,EAAO0N,SACH,IAATA,IAChBpoB,EAAOuW,GAAQ6R,IAKvB,OAAOpoB,CACT,C,6BC5DA,IAAIsD,EAAI,EAAQ,KAWhB,SAAS+kB,EAAStd,EAAO/G,GACvBxH,KAAKuO,MAAQA,EACbvO,KAAKwH,QAAUA,EACfxH,KAAK2L,WAAa,GAClB3L,KAAKka,WAAa,CAAC,CACrB,CAQA2R,EAASzmB,UAAU6D,UAAY,SAAUzB,GACvCxH,KAAKuO,OAASvO,KAAKuO,MAAMtF,UAAUzB,GACnC,IAAI0B,EAAalJ,KAAKwH,QAEtB,OADAxH,KAAKwH,QAAUV,EAAEqC,MAAMD,EAAY1B,GAC5BxH,IACT,EAaA6rB,EAASzmB,UAAUgI,aAAe,SAAU0e,GAI1C,OAHIhlB,EAAEwI,WAAWwc,IACf9rB,KAAK2L,WAAWhI,KAAKmoB,GAEhB9rB,IACT,EAeA6rB,EAASzmB,UAAUuK,IAAM,SAAUC,EAAMxH,GAKvC,GAJKA,GAAatB,EAAEwI,WAAWlH,KAC7BA,EAAW,WAAa,IAGrBpI,KAAKwH,QAAQqM,QAChB,OAAOzL,EAAS,IAAI5G,MAAM,2BAG5BxB,KAAKuO,MAAMwd,eAAenc,GAC1B,IAAIsU,EAAgBtU,EAAKoU,IACzBhkB,KAAKgsB,iBACHpc,EACA,SAAUoU,EAAKvgB,GACb,GAAIugB,EAEF,OADAhkB,KAAKuO,MAAM0d,kBAAkBrc,GACtBxH,EAAS4b,EAAK,MAEvBhkB,KAAKuO,MAAM2d,QAAQzoB,EAAG2E,EAAU8b,EAAetU,EACjD,EAAE4P,KAAKxf,MAEX,EAaA6rB,EAASzmB,UAAU4mB,iBAAmB,SAAUpc,EAAMxH,GACpD,IAAI+jB,GAAkB,EAClBC,EAAmBpsB,KAAK2L,WAAWzI,OACnCyI,EAAa3L,KAAK2L,WAClBnE,EAAUxH,KAAKwH,QAEf6kB,EAAK,SAAUrI,EAAKvgB,GAClBugB,EACF5b,EAAS4b,EAAK,QAIhBmI,IAEuBC,EAKvBzgB,EAAWwgB,GAAgB1oB,EAAG+D,EAAS6kB,GAJrCjkB,EAAS,KAAM3E,EAKnB,EAEA4oB,EAAG,KAAMzc,EACX,EAEA/P,EAAOD,QAAUisB,C,4BCzHjB,IAAI/kB,EAAI,EAAQ,KAuDhB,SAASwlB,EAAY5J,EAAO6J,EAAMC,GAChC,IAAK9J,EACH,OAAQ8J,EAGV,IAMWjJ,EAAU1b,EANjByb,EAASZ,EAAMY,OAEnB,IAAKA,GAA4B,IAAlBA,EAAOpgB,OACpB,OAAQspB,EAMV,IAFA,IAAIC,EAAaF,EAAKrpB,OAClBwpB,EAAcpJ,EAAOpgB,OAChBO,EAAI,EAAGA,EAAIipB,EAAajpB,IAAK,CAIpC,GAFA8f,EADQD,EAAO7f,GACE8f,UAEZzc,EAAEsC,OAAOma,EAAU,UACtB,OAAQiJ,EAGV,IAAK,IAAI9lB,EAAI,EAAGA,EAAI+lB,EAAY/lB,IAI9B,GAHAmB,EAAM0kB,EAAK7lB,GACA,IAAImT,OAAOhS,GAET4C,KAAK8Y,GAChB,OAAO,CAGb,CACA,OAAO,CACT,CAEA,SAASoJ,EAAa/c,EAAMqI,EAAU2U,EAAaphB,GAEjD,IAKI+gB,EAAMlG,EALNmG,GAAQ,EACQ,cAAhBI,IACFJ,GAAQ,GAIV,IAME,GALAD,EAAOC,EAAQvU,EAAS4U,cAAgB5U,EAAS6U,aACjDzG,EAASvf,EAAEoR,IAAItI,EAAM,qBAAuB,CAAC9I,EAAEoR,IAAItI,EAAM,gBAIpD2c,GAAwB,IAAhBA,EAAKrpB,OAChB,OAAQspB,EAEV,GAAsB,IAAlBnG,EAAOnjB,SAAiBmjB,EAAO,GACjC,OAAQmG,EAIV,IADA,IAAIO,EAAe1G,EAAOnjB,OACjBO,EAAI,EAAGA,EAAIspB,EAActpB,IAChC,GAAI6oB,EAAYjG,EAAO5iB,GAAI8oB,EAAMC,GAC/B,OAAO,CAGb,CAAE,MACAxpB,GAGIwpB,EACFvU,EAAS4U,cAAgB,KAEzB5U,EAAS6U,aAAe,KAE1B,IAAIE,EAAWR,EAAQ,gBAAkB,eASzC,OARAhhB,EAAOvK,MACL,4CACE+rB,EACA,4BACAA,EACA,IACFhqB,IAEMwpB,CACV,CACA,OAAO,CACT,CAqEA3sB,EAAOD,QAAU,CACf6O,WA7MF,SAAoBmB,EAAMqI,GACxB,IAAI1G,EAAQ3B,EAAK2B,MACb0b,EAAWnmB,EAAEomB,OAAO3b,IAAU,EAC9BoC,EAAcsE,EAAStE,YAG3B,QAAIsZ,GAFiBnmB,EAAEomB,OAAOvZ,IAAgB,GAMhD,EAoMEhF,gBAlMF,SAAyBnD,GACvB,OAAO,SAAUoE,EAAMqI,GACrB,IAAIkV,IAAevd,EAAK6B,mBACjB7B,EAAK6B,YACZ,IAAI1N,EAAO6L,EAAKwC,qBACTxC,EAAKwC,cACZ,IACMtL,EAAEwI,WAAW2I,EAASmV,iBACxBnV,EAASmV,eAAeD,EAAYppB,EAAM6L,EAE9C,CAAE,MAAO5M,GACPiV,EAASmV,eAAiB,KAC1B5hB,EAAOvK,MAAM,+CAAgD+B,EAC/D,CACA,IACE,GACE8D,EAAEwI,WAAW2I,EAASvJ,cACtBuJ,EAASvJ,YAAYye,EAAYppB,EAAM6L,GAEvC,OAAO,CAEX,CAAE,MAAO5M,GACPiV,EAASvJ,YAAc,KACvBlD,EAAOvK,MAAM,qDAAsD+B,EACrE,CACA,OAAO,CACT,CACF,EAwKE4L,oBAtKF,SAA6BpD,GAC3B,OAAO,SAAUoE,EAAMqI,GACrB,OAAQ0U,EAAa/c,EAAMqI,EAAU,YAAazM,EACpD,CACF,EAmKEqD,gBAjKF,SAAyBrD,GACvB,OAAO,SAAUoE,EAAMqI,GACrB,OAAO0U,EAAa/c,EAAMqI,EAAU,WAAYzM,EAClD,CACF,EA8JEsD,iBAxEF,SAA0BtD,GACxB,OAAO,SAAUoE,EAAMqI,GACrB,IAAIxU,EAAGiD,EAAG2mB,EAAiB3pB,EAAuB4pB,EAAiBC,EAEnE,IAIE,KAFAF,EAAkBpV,EAASoV,kBAEwB,IAA3BA,EAAgBnqB,OACtC,OAAO,EAKT,GAFAqqB,EAgCN,SAA0B3d,GACxB,IAAIqN,EAAOrN,EAAKqN,KACZsQ,EAAW,GAKf,GAAItQ,EAAKsJ,YAEP,IADA,IAAIH,EAAanJ,EAAKsJ,YACb9iB,EAAI,EAAGA,EAAI2iB,EAAWljB,OAAQO,IAAK,CAC1C,IAAIif,EAAQ0D,EAAW3iB,GACvB8pB,EAAS5pB,KAAKmD,EAAEoR,IAAIwK,EAAO,qBAC7B,CAQF,OANIzF,EAAKyF,OACP6K,EAAS5pB,KAAKmD,EAAEoR,IAAI+E,EAAM,4BAExBA,EAAKha,SACPsqB,EAAS5pB,KAAKmD,EAAEoR,IAAI+E,EAAM,iBAErBsQ,CACT,CArDiBC,CAAiB5d,GAEJ,IAApB2d,EAASrqB,OACX,OAAO,EAIT,IADAQ,EAAM2pB,EAAgBnqB,OACjBO,EAAI,EAAGA,EAAIC,EAAKD,IAGnB,IAFA6pB,EAAkB,IAAIzT,OAAOwT,EAAgB5pB,GAAI,MAE5CiD,EAAI,EAAGA,EAAI6mB,EAASrqB,OAAQwD,IAG/B,GAFmB4mB,EAAgB7iB,KAAK8iB,EAAS7mB,IAG/C,OAAO,CAIf,CAAE,MACA1D,GAGAiV,EAASoV,gBAAkB,KAC3B7hB,EAAOvK,MACL,oGAEJ,CAEA,OAAO,CACT,CACF,E,6BCrLA,IAAI6F,EAAI,EAAQ,KAehB,SAAS2mB,EAAMC,EAAa/gB,EAAKnB,EAAQhE,GACvCxH,KAAK0tB,YAAcA,EACnB1tB,KAAK2M,IAAMA,EACX3M,KAAKwL,OAASA,EACdxL,KAAKwH,QAAUA,EACfxH,KAAK6L,WAAa,GAClB7L,KAAK2tB,aAAe,GACpB3tB,KAAK4tB,gBAAkB,GACvB5tB,KAAK6tB,WAAa,GAClB7tB,KAAK8tB,YAAc,KACnB9tB,KAAK+tB,aAAe,KACpB/tB,KAAKguB,eAAiB,IACxB,CAOAP,EAAMroB,UAAU6D,UAAY,SAAUzB,GACpCxH,KAAK2M,KAAO3M,KAAK2M,IAAI1D,UAAUzB,GAC/B,IAAI0B,EAAalJ,KAAKwH,QAEtB,OADAxH,KAAKwH,QAAUV,EAAEqC,MAAMD,EAAY1B,GAC5BxH,IACT,EAWAytB,EAAMroB,UAAUoJ,aAAe,SAAUyf,GAIvC,OAHInnB,EAAEwI,WAAW2e,IACfjuB,KAAK6L,WAAWlI,KAAKsqB,GAEhBjuB,IACT,EAEAytB,EAAMroB,UAAU2mB,eAAiB,SAAUnc,GACzC5P,KAAK2tB,aAAahqB,KAAKiM,EACzB,EAEA6d,EAAMroB,UAAU6mB,kBAAoB,SAAUrc,GAC5C,IAAIse,EAAMluB,KAAK2tB,aAAahsB,QAAQiO,IACvB,IAATse,GACFluB,KAAK2tB,aAAaQ,OAAOD,EAAK,EAElC,EAYAT,EAAMroB,UAAU8mB,QAAU,SACxBtc,EACAxH,EACA8b,EACAkK,GAEKhmB,GAAatB,EAAEwI,WAAWlH,KAC7BA,EAAW,WAEX,GAEF,IAAIimB,EAAkBruB,KAAKsuB,iBAAiB1e,GAC5C,GAAIye,EAAgBE,KAGlB,OAFAvuB,KAAKisB,kBAAkBmC,QACvBhmB,EAASimB,EAAgBrK,KAK3B,GAFAhkB,KAAKwuB,UAAU5e,EAAMsU,GACrBlkB,KAAKisB,kBAAkBmC,GAClBpuB,KAAKwH,QAAQsM,SAAlB,CAIA9T,KAAK4tB,gBAAgBjqB,KAAKiM,GAC1B,IACE5P,KAAKyuB,gBACH7e,EACA,SAAUoU,EAAKhG,GACbhe,KAAK0uB,uBAAuB9e,GAC5BxH,EAAS4b,EAAKhG,EAChB,EAAEwB,KAAKxf,MAEX,CAAE,MAAOgD,GACPhD,KAAK0uB,uBAAuB9e,GAC5BxH,EAASpF,EACX,CAbA,MAFEoF,EAAS,IAAI5G,MAAM,qBAgBvB,EAQAisB,EAAMroB,UAAUupB,KAAO,SAAUvmB,GAC1BtB,EAAEwI,WAAWlH,KAGlBpI,KAAK+tB,aAAe3lB,EAChBpI,KAAK4uB,mBAGL5uB,KAAKguB,iBACPhuB,KAAKguB,eAAiBa,cAAc7uB,KAAKguB,iBAE3ChuB,KAAKguB,eAAiBc,YACpB,WACE9uB,KAAK4uB,gBACP,EAAEpP,KAAKxf,MACP,MAEJ,EASAytB,EAAMroB,UAAUkpB,iBAAmB,SAAU1e,GAE3C,IADA,IAAIjL,EAAI,KACClB,EAAI,EAAGC,EAAM1D,KAAK6L,WAAW3I,OAAQO,EAAIC,EAAKD,IAErD,KADAkB,EAAI3E,KAAK6L,WAAWpI,GAAGmM,EAAM5P,KAAKwH,gBACdzF,IAAV4C,EAAEqf,IACV,MAAO,CAAEuK,MAAM,EAAMvK,IAAKrf,EAAEqf,KAGhC,MAAO,CAAEuK,MAAM,EAAOvK,IAAK,KAC7B,EASAyJ,EAAMroB,UAAUqpB,gBAAkB,SAAU7e,EAAMxH,GAChD,IAAI2mB,EAAoB/uB,KAAK0tB,YAAYsB,WAAWpf,GAChDmf,EAAkBC,WACpBhvB,KAAK2M,IAAIzE,SACP0H,EACA,SAAUoU,EAAKhG,GACTgG,EACFhkB,KAAKivB,YAAYjL,EAAKpU,EAAMxH,GAE5BA,EAAS4b,EAAKhG,EAElB,EAAEwB,KAAKxf,OAEA+uB,EAAkB9tB,MAC3BmH,EAAS2mB,EAAkB9tB,OAE3BjB,KAAK2M,IAAIzE,SAAS6mB,EAAkB1mB,QAASD,EAEjD,EAGA,IAAI8mB,EAAmB,CACrB,aACA,YACA,kBACA,YACA,eACA,eACA,QACA,aAWFzB,EAAMroB,UAAU6pB,YAAc,SAAUjL,EAAKpU,EAAMxH,GACjD,IAAI+mB,GAAc,EAClB,GAAInvB,KAAKwH,QAAQ4nB,cAAe,CAC9B,IAAK,IAAI3rB,EAAI,EAAGC,EAAMwrB,EAAiBhsB,OAAQO,EAAIC,EAAKD,IACtD,GAAIugB,EAAI1G,OAAS4R,EAAiBzrB,GAAI,CACpC0rB,GAAc,EACd,KACF,CAEEA,GAAeroB,EAAEkhB,eAAehoB,KAAKwH,QAAQ6nB,cAC/Czf,EAAK0f,QAAU1f,EAAK0f,QAAU1f,EAAK0f,QAAU,EAAI,EAC7C1f,EAAK0f,QAAUtvB,KAAKwH,QAAQ6nB,aAC9BF,GAAc,GAGpB,CACIA,EACFnvB,KAAKuvB,iBAAiB3f,EAAMxH,GAE5BA,EAAS4b,EAEb,EASAyJ,EAAMroB,UAAUmqB,iBAAmB,SAAU3f,EAAMxH,GACjDpI,KAAK6tB,WAAWlqB,KAAK,CAAEiM,KAAMA,EAAMxH,SAAUA,IAExCpI,KAAK8tB,cACR9tB,KAAK8tB,YAAcgB,YACjB,WACE,KAAO9uB,KAAK6tB,WAAW3qB,QAAQ,CAC7B,IAAIssB,EAAcxvB,KAAK6tB,WAAW/pB,QAClC9D,KAAKyuB,gBAAgBe,EAAY5f,KAAM4f,EAAYpnB,SACrD,CACF,EAAEoX,KAAKxf,MACPA,KAAKwH,QAAQ4nB,eAGnB,EAUA3B,EAAMroB,UAAUspB,uBAAyB,SAAU9e,GACjD,IAAIse,EAAMluB,KAAK4tB,gBAAgBjsB,QAAQiO,IAC1B,IAATse,IACFluB,KAAK4tB,gBAAgBO,OAAOD,EAAK,GACjCluB,KAAK4uB,iBAET,EAEAnB,EAAMroB,UAAUopB,UAAY,SAAUrmB,EAAM+b,GAC1C,GAAIlkB,KAAKwL,QAAUxL,KAAKwH,QAAQoM,QAAS,CACvC,IAAI3Q,EAAUihB,EAGd,GADAjhB,GADAA,EAAUA,GAAW6D,EAAEoR,IAAI/P,EAAM,kCACZrB,EAAEoR,IAAI/P,EAAM,wCAG/B,YADAnI,KAAKwL,OAAOvK,MAAMgC,IAGpBA,EAAU6D,EAAEoR,IAAI/P,EAAM,uBAEpBnI,KAAKwL,OAAOmE,IAAI1M,EAEpB,CACF,EAEAwqB,EAAMroB,UAAUwpB,eAAiB,WAC/B,SACE9nB,EAAEwI,WAAWtP,KAAK+tB,eACW,IAA7B/tB,KAAK2tB,aAAazqB,QACc,IAAhClD,KAAK4tB,gBAAgB1qB,SAEjBlD,KAAKguB,iBACPhuB,KAAKguB,eAAiBa,cAAc7uB,KAAKguB,iBAE3ChuB,KAAK+tB,eACE,GAGX,EAEAluB,EAAOD,QAAU6tB,C,6BC3SjB,IAAI3mB,EAAI,EAAQ,KAOhB,SAAS2oB,EAAYjoB,GACnBxH,KAAK0vB,UAAY5oB,EAAEkV,MACnBhc,KAAK2vB,QAAU,EACf3vB,KAAK4vB,cAAgB,EACrB5vB,KAAK0kB,SAAW,KAChB1kB,KAAK6vB,gBAAkB,CAAC,EACxB7vB,KAAK8vB,gBAAgBtoB,EACvB,CA8FA,SAASuoB,EAAUngB,EAAMogB,EAAOL,GAC9B,OAAQ/f,EAAKqgB,iBAAmBD,GAAS,GAAKL,EAAUK,CAC1D,CAEA,SAASE,EACPxL,EACAld,EACAvG,EACA+tB,EACAmB,EACAC,EACAC,GAEA,IAAIhoB,EAAU,KAad,OAZIpH,IACFA,EAAQ,IAAIO,MAAMP,IAEfA,GAAU+tB,IACb3mB,EAWJ,SACEqc,EACAld,EACA2oB,EACAC,EACAC,GAEA,IAAI5L,EACFjd,EAAQid,aAAgBjd,EAAQa,SAAWb,EAAQa,QAAQoc,YAOzD7U,EAAO,CACTqN,KAAM,CACJha,QAAS,CACPga,KARFoT,EACI,+DAEA,sDAMFjO,MAAO,CACLkI,SAAU6F,EACVG,eAAgBF,KAItBxL,SAAU,aACVH,YAAaA,EACbtX,SAAU,CACR/F,QACGI,EAAQ2F,UAAY3F,EAAQ2F,SAAS/F,SAAYI,EAAQJ,UAchE,MAXiB,YAAbsd,GACF9U,EAAK8U,SAAW,UAChB9U,EAAK+U,UAAY,aACjB/U,EAAKzC,SAAS4M,KAAO,sBACC,WAAb2K,GACT9U,EAAK+U,UAAYnd,EAAQmd,WAAa,UACtC/U,EAAKzC,SAAS4M,KAAOvS,EAAQ2F,SAAS4M,MAChB,iBAAb2K,IACT9U,EAAK+U,UAAYnd,EAAQmd,WAAa,eACtC/U,EAAKzC,SAAS4M,KAAOvS,EAAQ2F,SAAS4M,MAEjCnK,CACT,CAvDc2gB,CACR7L,EACAld,EACA2oB,EACAC,EACAC,IAGG,CAAEpvB,MAAOA,EAAO+tB,WAAYA,EAAY3mB,QAASA,EAC1D,CAvHAonB,EAAYe,eAAiB,CAC3Bd,UAAW5oB,EAAEkV,MACbsO,cAAUvoB,EACVuuB,oBAAgBvuB,GAWlB0tB,EAAYrqB,UAAU0qB,gBAAkB,SAAUtoB,QACtBzF,IAAtByF,EAAQkoB,YACVD,EAAYe,eAAed,UAAYloB,EAAQkoB,gBAExB3tB,IAArByF,EAAQ8iB,WACVmF,EAAYe,eAAelG,SAAW9iB,EAAQ8iB,eAEjBvoB,IAA3ByF,EAAQ8oB,iBACVb,EAAYe,eAAeF,eAAiB9oB,EAAQ8oB,eAExD,EAiBAb,EAAYrqB,UAAU4pB,WAAa,SAAUpf,EAAMoM,GAEjD,IAAIyU,GADJzU,EAAMA,GAAOlV,EAAEkV,OACShc,KAAK0vB,WACzBe,EAAc,GAAKA,GAAe,OACpCzwB,KAAK0vB,UAAY1T,EACjBhc,KAAK4vB,cAAgB,GAGvB,IAAIO,EAAkBV,EAAYe,eAAelG,SAC7CoG,EAAwBjB,EAAYe,eAAeF,eAEvD,GAAIP,EAAUngB,EAAMugB,EAAiBnwB,KAAK2vB,SACxC,OAAOO,EACLlwB,KAAK0kB,SACL1kB,KAAK6vB,gBACLM,EAAkB,sBAClB,GAEG,GAAIJ,EAAUngB,EAAM8gB,EAAuB1wB,KAAK4vB,eACrD,OAAOM,EACLlwB,KAAK0kB,SACL1kB,KAAK6vB,gBACLa,EAAwB,6BACxB,GAGJ1wB,KAAK2vB,UACL3vB,KAAK4vB,gBAEL,IAAIZ,GAAce,EAAUngB,EAAMugB,EAAiBnwB,KAAK2vB,SACpDU,EAAYrB,EAGhB,OAFAA,EACEA,IAAee,EAAUngB,EAAM8gB,EAAuB1wB,KAAK4vB,eACtDM,EACLlwB,KAAK0kB,SACL1kB,KAAK6vB,gBACL,KACAb,EACAmB,EACAO,EACAL,EAEJ,EAEAZ,EAAYrqB,UAAUurB,mBAAqB,SAAUjM,EAAUld,GAC7DxH,KAAK0kB,SAAWA,EAChB1kB,KAAK6vB,gBAAkBroB,CACzB,EA+EA3H,EAAOD,QAAU6vB,C,6BCvLjB,IAAIA,EAAc,EAAQ,KACtBhC,EAAQ,EAAQ,KAChB5B,EAAW,EAAQ,KACnB/kB,EAAI,EAAQ,KAShB,SAASsE,EAAQ5D,EAASmF,EAAKnB,EAAQa,EAAWqY,GAChD1kB,KAAKwH,QAAUV,EAAEqC,MAAM3B,GACvBxH,KAAKwL,OAASA,EACdJ,EAAQsiB,YAAYoC,gBAAgB9vB,KAAKwH,SACzC4D,EAAQsiB,YAAYiD,mBAAmBjM,EAAU1kB,KAAKwH,SACtDxH,KAAK2M,IAAMA,EACX3M,KAAKuO,MAAQ,IAAIkf,EAAMriB,EAAQsiB,YAAa/gB,EAAKnB,EAAQxL,KAAKwH,SAG9D,IAAIopB,EAAS5wB,KAAKwH,QAAQopB,QAAU,KAChCC,EAAeD,IACjB5wB,KAAK4wB,OAASA,EAEd5wB,KAAKwH,QAAQopB,OAAS,6BACtB5wB,KAAKwH,QAAQ0E,mBAAmB0kB,OAAS,8BAEzC5wB,KAAK4wB,OAAS,KAGhB5wB,KAAKmN,SAAW,IAAI0e,EAAS7rB,KAAKuO,MAAOvO,KAAKwH,SAC9CxH,KAAKqM,UAAYA,EACjBykB,EAAmBtpB,GACnBxH,KAAK0P,UAAY,KACjB1P,KAAK+wB,cAAgB,MACvB,CAiMA,SAASD,EAAmBtpB,GACtBA,EAAQwpB,kBACVxvB,MAAMwvB,gBAAkBxpB,EAAQwpB,gBAEpC,CAOA,SAASH,EAAeD,GACtB,IAAKA,EACH,OAAO,EAGT,IAAKA,EAAOK,OAAiC,mBAAjBL,EAAOK,MACjC,OAAO,EAGT,IAAIA,EAAQL,EAAOK,QAEnB,SAAKA,IAAUA,EAAMC,QAAkC,mBAAjBD,EAAMC,OAK9C,CArNA9lB,EAAQsiB,YAAc,IAAI+B,EALL,CACnBnF,SAAU,EACVgG,eAAgB,KAKlBllB,EAAQhG,UAAUnF,OAAS,SAAUuH,GAEnC,OADA4D,EAAQsiB,YAAYoC,gBAAgBtoB,GAC7BxH,IACT,EAEAoL,EAAQhG,UAAU6D,UAAY,SAAUzB,EAASiI,GAC/C,IAAIvG,EAAalJ,KAAKwH,QAClBa,EAAU,CAAC,EACXoH,IACFpH,EAAU,CAAEA,QAASoH,IAGvBzP,KAAKwH,QAAUV,EAAEqC,MAAMD,EAAY1B,EAASa,GAG5C,IAAIuoB,EAAS5wB,KAAKwH,QAAQopB,QAAU,KAmBpC,OAlBIC,EAAeD,IACjB5wB,KAAK4wB,OAASA,EAEd5wB,KAAKwH,QAAQopB,OAAS,6BACtB5wB,KAAKwH,QAAQ0E,mBAAmB0kB,OAAS,8BAEzC5wB,KAAK4wB,OAAS,KAGhB5wB,KAAKmN,UAAYnN,KAAKmN,SAASlE,UAAUjJ,KAAKwH,SAC9CxH,KAAKqM,WAAarM,KAAKqM,UAAUpD,UAAUjJ,KAAKwH,SAChDspB,EAAmBtpB,GACnBxH,KAAKC,OAAOD,KAAKwH,SAEbqpB,EAAerpB,EAAQopB,UACzB5wB,KAAK4wB,OAASppB,EAAQopB,QAGjB5wB,IACT,EAEAoL,EAAQhG,UAAUuK,IAAM,SAAUC,GAChC,IAAI2B,EAAQvR,KAAKmxB,mBACjB,OAAOnxB,KAAKoxB,KAAK7f,EAAO3B,EAC1B,EAEAxE,EAAQhG,UAAU6K,MAAQ,SAAUL,GAClC5P,KAAKoxB,KAAK,QAASxhB,EACrB,EAEAxE,EAAQhG,UAAU8K,KAAO,SAAUN,GACjC5P,KAAKoxB,KAAK,OAAQxhB,EACpB,EAEAxE,EAAQhG,UAAU+K,KAAO,SAAUP,GACjC5P,KAAKoxB,KAAK,UAAWxhB,EACvB,EAEAxE,EAAQhG,UAAUgL,QAAU,SAAUR,GACpC5P,KAAKoxB,KAAK,UAAWxhB,EACvB,EAEAxE,EAAQhG,UAAUnE,MAAQ,SAAU2O,GAClC5P,KAAKoxB,KAAK,QAASxhB,EACrB,EAEAxE,EAAQhG,UAAUiL,SAAW,SAAUT,GACrC5P,KAAKoxB,KAAK,WAAYxhB,EACxB,EAEAxE,EAAQhG,UAAUupB,KAAO,SAAUvmB,GACjCpI,KAAKuO,MAAMogB,KAAKvmB,EAClB,EAEAgD,EAAQhG,UAAU2N,aAAe,SAAUG,EAAMC,EAAU5B,GACzD,OAAOvR,KAAKqM,WAAarM,KAAKqM,UAAU0G,aAAaG,EAAMC,EAAU5B,EACvE,EAEAnG,EAAQhG,UAAUgO,wBAA0B,SAAUC,GACpD,OAAOrT,KAAKqM,WAAarM,KAAKqM,UAAU+G,wBAAwBC,EAClE,EAEAjI,EAAQhG,UAAUkO,YAAc,SAAUD,GACxC,OAAOrT,KAAKqM,WAAarM,KAAKqM,UAAUiH,YAAYD,EACtD,EAEAjI,EAAQhG,UAAUsD,iBAAmB,SAAUkH,GAC7C,OAAO5P,KAAK2M,IAAIjE,iBAAiBkH,EACnC,EAEAxE,EAAQhG,UAAUkL,gBAAkB,SAAUtH,GAC5ChJ,KAAK2M,IAAI5D,gBAAgBC,EAC3B,EAIAoC,EAAQhG,UAAUgsB,KAAO,SAAUC,EAAczhB,GAC/C,IAAIxH,EAKJ,GAJIwH,EAAKxH,WACPA,EAAWwH,EAAKxH,gBACTwH,EAAKxH,UAEVpI,KAAKwH,QAAQ0M,uBAAyBlU,KAAKsxB,iBAAiB1hB,IAC9D,GAAIxH,EAAU,CACZ,IAAInH,EAAQ,IAAIO,MAAM,0BACtBP,EAAM2O,KAAOA,EACbxH,EAASnH,EACX,OAGF,IACEjB,KAAKuxB,gBAAgB3hB,GACrBA,EAAK2B,MAAQ3B,EAAK2B,OAAS8f,EAC3BrxB,KAAKqM,WAAarM,KAAKqM,UAAUmlB,oBAAoB5hB,GACrDA,EAAK6hB,gBACFzxB,KAAKqM,WAAarM,KAAKqM,UAAUqlB,cAAiB,GACrD1xB,KAAKmN,SAASwC,IAAIC,EAAMxH,EAC1B,CAAE,MAAOpF,GACHoF,GACFA,EAASpF,GAEXhD,KAAKwL,OAAOvK,MAAM+B,EACpB,CACF,EAEAoI,EAAQhG,UAAU+rB,iBAAmB,WACnC,OAAOnxB,KAAKwH,QAAQkM,UAAY,OAClC,EAEAtI,EAAQhG,UAAUksB,iBAAmB,SAAU1hB,GAC7C,IAAKA,EAAK6B,YACR,OAAO,EAET,IAAIkgB,EA8CN,SAA0B/hB,GACxB,IAAI3M,EAAU2M,EAAK3M,SAAW,GAC1B7B,GAASwO,EAAKoU,KAAO,CAAC,GAAG5iB,OAASyF,OAAO+I,EAAKoU,KAClD,OAAO/gB,EAAU,KAAO7B,CAC1B,CAlDiBwwB,CAAiBhiB,GAChC,OAAI5P,KAAK+wB,gBAAkBY,IAG3B3xB,KAAK0P,UAAYE,EAAKoU,IACtBhkB,KAAK+wB,cAAgBY,GACd,EACT,EAEAvmB,EAAQhG,UAAUmsB,gBAAkB,SAAU3hB,GAG5C,GAAI5P,KAAK4wB,OAAQ,CAEf,IAAIiB,EAAO7xB,KAAK4wB,OAAOK,QAAQC,SAE/B,GAwEJ,SAAsBW,GACpB,IAAKA,IAASA,EAAKxoB,SAAmC,mBAAjBwoB,EAAKxoB,QACxC,OAAO,EAGT,IAAIyoB,EAAcD,EAAKxoB,UAEvB,SACGyoB,GACAA,EAAYC,UACZD,EAAYE,WACmB,mBAAzBF,EAAYC,UACc,mBAA1BD,EAAYE,UAMvB,CA1FQC,CAAaJ,GAAO,CACtBA,EAAKK,OAAO,qBAAsBtiB,EAAKG,MACvC8hB,EAAKK,OAAO,qBAAqB,GACjCL,EAAKK,OAAO,SAAS,GACrBL,EAAKK,OACH,mBACA,uCAAuCtiB,EAAKG,QAE9C8hB,EAAKK,OACH,yBACA,6CAA6CtiB,EAAKG,QAIpD,IAAIoiB,EAAoBN,EAAKxoB,UAAU0oB,WACnCK,EAAqBP,EAAKxoB,UAAU2oB,YAEpCpiB,EAAKuS,QACPvS,EAAKuS,OAAOkQ,oBAAsBF,EAClCviB,EAAKuS,OAAOmQ,qBAAuBF,GAEnCxiB,EAAKuS,OAAS,CACZkQ,oBAAqBF,EACrBG,qBAAsBF,EAG5B,CACF,CACF,EAgEAvyB,EAAOD,QAAUwL,C,6BC1RjB,IAAItE,EAAI,EAAQ,KACZyrB,EAAW,EAAQ,KAsDvB,SAASC,EAAUrtB,EAAK+B,GACtB,IAAIge,EAAOhe,EAAKzG,MAAM,KAClBgpB,EAAOvE,EAAKhiB,OAAS,EACzB,IACE,IAAK,IAAIO,EAAI,EAAGA,GAAKgmB,IAAQhmB,EACvBA,EAAIgmB,EACNtkB,EAAMA,EAAI+f,EAAKzhB,IAEf0B,EAAI+f,EAAKzhB,IAAMqD,EAAE2rB,QAGvB,CAAE,MAAOzvB,GAET,CACF,CAsBAnD,EAAOD,QAxFP,SAAeuI,EAAMsL,EAAakT,GAGhC,GAFAlT,EAAcA,GAAe,GAEzBkT,EACF,IAAK,IAAIljB,EAAI,EAAGA,EAAIkjB,EAAWzjB,SAAUO,EACvC+uB,EAAUrqB,EAAMwe,EAAWljB,IAI/B,IAAIivB,EA2DN,SAA8Bjf,GAG5B,IAFA,IACIkf,EADApb,EAAM,GAED9T,EAAI,EAAGA,EAAIgQ,EAAYvQ,SAAUO,EACxCkvB,EAAM,iBAAmBlf,EAAYhQ,GAAK,8BAC1C8T,EAAI5T,KAAK,IAAIkW,OAAO8Y,EAAK,MAE3B,OAAOpb,CACT,CAnEiBqb,CAAqBnf,GAChCof,EAoEN,SAAmCpf,GAGjC,IAFA,IACIkf,EADApb,EAAM,GAED9T,EAAI,EAAGA,EAAIgQ,EAAYvQ,SAAUO,EACxCkvB,EAAM,gBAAkBlf,EAAYhQ,GAAK,6BACzC8T,EAAI5T,KAAK,IAAIkW,OAAO,IAAM8Y,EAAM,eAAgB,QAElD,OAAOpb,CACT,CA5EiBub,CAA0Brf,GAEzC,SAASsf,EAAiBC,EAAQC,GAChC,OAAOA,EAAYnsB,EAAE2rB,QACvB,CAmCA,OAAOF,EAASpqB,GAZhB,SAAS+qB,EAAStsB,EAAGrB,EAAG4tB,GACtB,IAAIC,EAZN,SAAqBxsB,EAAGrB,GACtB,IAAI9B,EACJ,IAAKA,EAAI,EAAGA,EAAIivB,EAASxvB,SAAUO,EACjC,GAAIivB,EAASjvB,GAAGgH,KAAK7D,GAAI,CACvBrB,EAAIuB,EAAE2rB,SACN,KACF,CAEF,OAAOltB,CACT,CAGa8tB,CAAYzsB,EAAGrB,GAC1B,OAAI6tB,IAAS7tB,EACPuB,EAAEsC,OAAO7D,EAAG,WAAauB,EAAEsC,OAAO7D,EAAG,SAChCgtB,EAAShtB,EAAG2tB,EAAUC,GAzBnC,SAAuB5tB,GACrB,IAAI9B,EACJ,GAAIqD,EAAEsC,OAAO7D,EAAG,UACd,IAAK9B,EAAI,EAAGA,EAAIovB,EAAS3vB,SAAUO,EACjC8B,EAAIA,EAAEzD,QAAQ+wB,EAASpvB,GAAIsvB,GAG/B,OAAOxtB,CACT,CAmBW+tB,CAAcF,GAEdA,CAEX,GAGF,C,6BCrDA,IAAItsB,EAAI,EAAQ,KAEZysB,EAAa,IAEjB,SAASpnB,EAAU3E,GACjBxH,KAAKuO,MAAQ,GACbvO,KAAKwH,QAAUV,EAAEqC,MAAM3B,GACvB,IAAIgsB,EAAqBxzB,KAAKwH,QAAQgsB,oBAAsBD,EAC5DvzB,KAAKyzB,aAAe5P,KAAK6P,IAAI,EAAG7P,KAAK8P,IAAIH,EAAoBD,GAC/D,CA4NA,SAASK,EAAS1gB,EAAM3B,GACtB,OAAIA,IAGe,CACjBtQ,MAAO,QACP4yB,OAAQ,QAEU3gB,IAAS,OAC/B,CAnOA/G,EAAU/G,UAAU6D,UAAY,SAAUzB,GACxC,IAAI0B,EAAalJ,KAAKwH,QACtBxH,KAAKwH,QAAUV,EAAEqC,MAAMD,EAAY1B,GACnC,IAAIgsB,EAAqBxzB,KAAKwH,QAAQgsB,oBAAsBD,EACxDO,EAAejQ,KAAK6P,IAAI,EAAG7P,KAAK8P,IAAIH,EAAoBD,IACxDQ,EAAc,EACd/zB,KAAKuO,MAAMrL,OAAS4wB,IACtBC,EAAc/zB,KAAKuO,MAAMrL,OAAS4wB,GAEpC9zB,KAAKyzB,aAAeK,EACpB9zB,KAAKuO,MAAM4f,OAAO,EAAG4F,EACvB,EAEA5nB,EAAU/G,UAAUssB,WAAa,WAC/B,IAAIsC,EAAS3c,MAAMjS,UAAU9C,MAAMoD,KAAK1F,KAAKuO,MAAO,GACpD,GAAIzH,EAAEwI,WAAWtP,KAAKwH,QAAQysB,iBAC5B,IAEE,IADA,IAAIxwB,EAAIuwB,EAAO9wB,OACRO,KACDzD,KAAKwH,QAAQysB,gBAAgBD,EAAOvwB,KACtCuwB,EAAO7F,OAAO1qB,EAAG,EAGvB,CAAE,MAAOT,GACPhD,KAAKwH,QAAQysB,gBAAkB,IACjC,CAEF,OAAOD,CACT,EAEA7nB,EAAU/G,UAAU2c,QAAU,SAC5B7O,EACAC,EACA5B,EACA6M,EACAoH,GAEA,IAAIxiB,EAAI,CACNuO,MAAOqiB,EAAS1gB,EAAM3B,GACtB2B,KAAMA,EACNghB,aAAc1O,GAAa1e,EAAEkV,MAC7BiB,KAAM9J,EACNtQ,OAAQ,UAENub,IACFpb,EAAE+M,KAAOqO,GAGX,IACE,GACEtX,EAAEwI,WAAWtP,KAAKwH,QAAQysB,kBAC1Bj0B,KAAKwH,QAAQysB,gBAAgBjxB,GAE7B,OAAO,CAEX,CAAE,MAAO2P,GACP3S,KAAKwH,QAAQysB,gBAAkB,IACjC,CAGA,OADAj0B,KAAK2D,KAAKX,GACHA,CACT,EAEAmJ,EAAU/G,UAAU2N,aAAe,SACjCG,EACAC,EACA5B,EACA6M,GAEA,OAAOpe,KAAK+hB,QAAQ7O,EAAMC,EAAU5B,EAAO6M,EAC7C,EAEAjS,EAAU/G,UAAU+uB,aAAe,SACjCnQ,EACAzS,EACA6M,EACAoH,GAEA,IAAIrS,EAAW,CACblQ,QAAS+gB,EAAI/gB,SAAW4D,OAAOmd,IAKjC,OAHIA,EAAI5iB,QACN+R,EAAS/R,MAAQ4iB,EAAI5iB,OAEhBpB,KAAK+hB,QAAQ,QAAS5O,EAAU5B,EAAO6M,EAAaoH,EAC7D,EAEArZ,EAAU/G,UAAU+Z,WAAa,SAC/Blc,EACAsO,EACA6M,EACAoH,GAEA,OAAOxlB,KAAK+hB,QACV,MACA,CACE9e,QAASA,GAEXsO,EACA6M,EACAoH,EAEJ,EAEArZ,EAAU/G,UAAUoX,eAAiB,SACnCrJ,EACAgL,EACAC,EACAgW,GAEAjW,EAAUA,GAAW,MACrBhL,EAASgL,QAAUhL,EAASgL,SAAWA,EACnCiW,IACFjhB,EAASmJ,QAAU8X,GAErB,IAAI7iB,EAAQvR,KAAKwd,gBAAgBrK,EAAS2I,aAC1C,OAAO9b,KAAK+hB,QAAQ,UAAW5O,EAAU5B,EAAO6M,EAClD,EAEAjS,EAAU/G,UAAUoY,gBAAkB,SAAU6W,GAC9C,OAAIA,GAAc,KAAOA,EAAa,IAC7B,OAEU,IAAfA,GAAoBA,GAAc,IAC7B,QAEF,MACT,EAEAloB,EAAU/G,UAAUkb,WAAa,SAC/BnC,EACAtH,EACA/N,EACAiX,EACA3B,GAEA,IAAIjL,EAAW,CACbgL,QAASA,EACTtH,QAASA,GAQX,YANc9U,IAAV+G,IACFqK,EAASrK,MAAQA,QAEH/G,IAAZge,IACF5M,EAAS4M,QAAUA,GAEd/f,KAAK+hB,QAAQ,MAAO5O,EAAU,OAAQiL,EAC/C,EAEAjS,EAAU/G,UAAU8b,kBAAoB,SAAUN,EAAMC,EAAIzC,GAC1D,OAAOpe,KAAK+hB,QACV,aACA,CAAEnB,KAAMA,EAAMC,GAAIA,GAClB,OACAzC,EAEJ,EAEAjS,EAAU/G,UAAUgO,wBAA0B,SAAUC,GACtD,OAAOrT,KAAK+hB,QACV,aACA,CAAE5D,QAAS,oBACX,YACApc,EACAsR,GAAMA,EAAGlI,UAMb,EACAgB,EAAU/G,UAAUkO,YAAc,SAAUD,GAC1C,OAAOrT,KAAK+hB,QACV,aACA,CAAE5D,QAAS,QACX,YACApc,EACAsR,GAAMA,EAAGlI,UAMb,EAEAgB,EAAU/G,UAAU+b,0BAA4B,SAAUjO,EAAMkL,GAC9D,OAAOpe,KAAKwc,eAAe,CAAE8X,OAAQphB,GAAQ,eAAgBkL,EAC/D,EAGAjS,EAAU/G,UAAUosB,oBAAsB,SAAU5hB,GAClD,GAAK5P,KAAKwH,QAAQwM,wBAGlB,OAAIpE,EAAKoU,IACAhkB,KAAKm0B,aAAavkB,EAAKoU,IAAKpU,EAAK2B,MAAO3B,EAAKG,KAAMH,EAAK4V,WAE7D5V,EAAK3M,QACAjD,KAAKmf,WAAWvP,EAAK3M,QAAS2M,EAAK2B,MAAO3B,EAAKG,KAAMH,EAAK4V,WAE/D5V,EAAKuS,OACAniB,KAAK+hB,QACV,MACAnS,EAAKuS,OACLvS,EAAK2B,MACL3B,EAAKG,KACLH,EAAK4V,gBANT,CASF,EAEArZ,EAAU/G,UAAUzB,KAAO,SAAUX,GACnChD,KAAKuO,MAAM5K,KAAKX,GACZhD,KAAKuO,MAAMrL,OAASlD,KAAKyzB,cAC3BzzB,KAAKuO,MAAMzK,OAEf,EAaAjE,EAAOD,QAAUuM,C,6BChPjB,IAAIrF,EAAI,EAAQ,KAqGhB,SAASytB,EAAkB/sB,EAASuS,GAC9BjT,EAAEwI,WAAW9H,EAAQuS,MACvBvS,EAAQuS,GAAQvS,EAAQuS,GAAMtU,WAElC,CAoDA5F,EAAOD,QAAU,CACfyO,cA5JF,SAAuBuB,EAAMpI,EAASY,GACpC,IAAID,EAAOyH,EAAKzH,KAEZyH,EAAK6B,cACPtJ,EAAKsJ,aAAc,GAEjB7B,EAAKwC,gBACPjK,EAAKiK,cAAgBxC,EAAKwC,eAE5BhK,EAAS,KAAMD,EACjB,EAmJE8F,kBAjJF,SAA2B2B,EAAMpI,EAASY,GACxC,IAAIosB,EAAiBhtB,EAAQa,SAAW,CAAC,EACrCmsB,EAAevX,aACVuX,EAAevX,KAGxBrN,EAAKzH,KAAOrB,EAAEqC,MAAMyG,EAAKzH,KAAMqsB,GAC/BpsB,EAAS,KAAMwH,EACjB,EA0IE9B,iBAxIF,SAA0B8B,EAAMpI,EAASY,GACnCwH,EAAK6hB,iBACP3qB,EAAEub,IAAIzS,EAAM,sBAAuBA,EAAK6hB,iBAE1CrpB,EAAS,KAAMwH,EACjB,EAoIE/B,oBAlIF,SAA6B+B,EAAMpI,EAASY,GAC1C,GAAKwH,EAAK3M,QAAV,CAIA,IAAIwxB,EAAY,0BACZ/R,EAAQ5b,EAAEoR,IAAItI,EAAM6kB,GAKxB,GAJK/R,IACH+R,EAAY,kBACZ/R,EAAQ5b,EAAEoR,IAAItI,EAAM6kB,IAElB/R,EAAO,CACT,IAAMA,EAAMC,YAAaD,EAAMC,UAAU7I,YAGvC,OAFAhT,EAAEub,IAAIzS,EAAM6kB,EAAY,yBAA0B7kB,EAAK3M,cACvDmF,EAAS,KAAMwH,GAGjB,IAAIwS,EAAQtb,EAAEoR,IAAItI,EAAM6kB,EAAY,WAAa,CAAC,EAC9CC,EAAW5tB,EAAEqC,MAAMiZ,EAAO,CAAEnf,QAAS2M,EAAK3M,UAC9C6D,EAAEub,IAAIzS,EAAM6kB,EAAY,SAAUC,EACpC,CACAtsB,EAAS,KAAMwH,EAjBf,MAFExH,EAAS,KAAMwH,EAoBnB,EA6GE1B,cA3GF,SAAuB1C,GACrB,OAAO,SAAUoE,EAAMpI,EAASY,GAC9B,IAAIusB,EAAU7tB,EAAEqC,MAAMyG,GAClBuN,EAAW,KACf,IACMrW,EAAEwI,WAAW9H,EAAQskB,aACvB3O,EAAW3V,EAAQskB,UAAU6I,EAAQxsB,KAAMyH,GAE/C,CAAE,MAAO5M,GAOP,OANAwE,EAAQskB,UAAY,KACpBtgB,EAAOvK,MACL,gFACA+B,QAEFoF,EAAS,KAAMwH,EAEjB,CACI9I,EAAE8tB,UAAUzX,GACdA,EAASY,MACP,SAAU8W,GACJA,IACFF,EAAQxsB,KAAO0sB,GAEjBzsB,EAAS,KAAMusB,EACjB,IACA,SAAU1zB,GACRmH,EAASnH,EAAO2O,EAClB,IAGFxH,EAAS,KAAMusB,EAEnB,CACF,EA2EE5mB,mBAzEF,SAA4B6B,EAAMpI,EAASY,GACzC,IAAKZ,EAAQuM,WACX,OAAO3L,EAAS,KAAMwH,GAExB,IACIuS,EAASrb,EAAEoR,IAAItI,EAAM,gBAAkB,CAAC,EAC5CuS,EAAgB,eAAI3a,EACpBoI,EAAKzH,KAAKga,OAASA,EACnB/Z,EAAS,KAAMwH,EACjB,EAiEEzB,qBAzDF,SAA8ByB,EAAMpI,EAASY,GAC3C,IAAI0sB,EAAoBttB,EAAQ0E,mBAGhCqoB,EAAkBO,EAAmB,aACrCP,EAAkBO,EAAmB,eACrCP,EAAkBO,EAAmB,yBAE9BA,EAAkBhtB,YACzB8H,EAAKzH,KAAKgF,SAAS4nB,mBAAqBD,EACxC1sB,EAAS,KAAMwH,EACjB,EA+CExB,kBA7CF,SAA2BwB,EAAMpI,EAASY,GACxC,IAAI8R,EAAapT,EAAEqC,MACjByG,EAAKzC,SAASnB,OAAOmB,SAAS+M,WAC9BtK,EAAKsK,YAWP,GARIpT,EAAEoR,IAAItI,EAAM,sBACdsK,EAAW8a,cAAe,GAGxBplB,EAAK6B,cACPyI,EAAW+a,YAAcrlB,EAAK6B,aAG5B7B,EAAKoU,IACP,IACE9J,EAAWgb,UAAY,CACrBjyB,QAAS2M,EAAKoU,IAAI/gB,QAClB8W,KAAMnK,EAAKoU,IAAIjK,KACfob,iBAAkBvlB,EAAKoU,IAAIiH,aAAerb,EAAKoU,IAAIiH,YAAYlR,KAC/DwJ,SAAU3T,EAAKoU,IAAIthB,SACnBT,KAAM2N,EAAKoU,IAAIrhB,WACf+gB,OAAQ9T,EAAKoU,IAAIphB,aACjBxB,MAAOwO,EAAKoU,IAAI5iB,MAEpB,CAAE,MAAO4B,GACPkX,EAAWgb,UAAY,CAAEE,OAAQvuB,OAAO7D,GAC1C,CAGF4M,EAAKzH,KAAKgF,SAAS+M,WAAapT,EAAEqC,MAChCyG,EAAKzH,KAAKgF,SAAS+M,WACnBA,GAEF9R,EAAS,KAAMwH,EACjB,E,6BC3JA,IAAI9I,EAAI,EAAQ,KACZyrB,EAAW,EAAQ,KAEvB,SAASnP,EAAI/a,EAAST,GACpB,MAAO,CAACS,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASytB,EAAa/R,EAAQgS,GAC5B,IAAI5xB,EAAM4f,EAAOpgB,OACjB,OAAIQ,EAAc,EAAR4xB,EACDhS,EAAOhhB,MAAM,EAAGgzB,GAAOpwB,OAAOoe,EAAOhhB,MAAMoB,EAAM4xB,IAEnDhS,CACT,CAEA,SAASiS,EAAeltB,EAAST,EAAY0tB,GAC3CA,OAAyB,IAAVA,EAAwB,GAAKA,EAC5C,IACIhS,EADArG,EAAO5U,EAAQF,KAAK8U,KAExB,GAAIA,EAAKsJ,YAEP,IADA,IAAIjC,EAAQrH,EAAKsJ,YACR9iB,EAAI,EAAGA,EAAI6gB,EAAMphB,OAAQO,IAEhC6f,EAAS+R,EADT/R,EAASgB,EAAM7gB,GAAG6f,OACYgS,GAC9BhR,EAAM7gB,GAAG6f,OAASA,OAEXrG,EAAKyF,QAEdY,EAAS+R,EADT/R,EAASrG,EAAKyF,MAAMY,OACUgS,GAC9BrY,EAAKyF,MAAMY,OAASA,GAEtB,MAAO,CAACjb,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAAS4tB,EAAmB9xB,EAAK+xB,GAC/B,OAAKA,GAGDA,EAAIvyB,OAASQ,EACR+xB,EAAInzB,MAAM,EAAGoB,EAAM,GAAGwB,OAAO,OAH7BuwB,CAMX,CAEA,SAASC,EAAgBhyB,EAAK2E,EAAST,GAarC,OADAS,EAAUkqB,EAASlqB,GAXnB,SAASstB,EAAU/uB,EAAGrB,EAAG4tB,GACvB,OAAQrsB,EAAE8uB,SAASrwB,IACjB,IAAK,SACH,OAAOiwB,EAAmB9xB,EAAK6B,GACjC,IAAK,SACL,IAAK,QACH,OAAOgtB,EAAShtB,EAAGowB,EAAWxC,GAChC,QACE,OAAO5tB,EAEb,IAEO,CAAC8C,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASiuB,EAAkBC,GASzB,OARIA,EAAUnT,mBACLmT,EAAUnT,UAAU7I,YAC3Bgc,EAAUnT,UAAU1f,QAAUuyB,EAC5B,IACAM,EAAUnT,UAAU1f,UAGxB6yB,EAAUxS,OAAS+R,EAAaS,EAAUxS,OAAQ,GAC3CwS,CACT,CAEA,SAASC,EAAQ1tB,EAAST,GACxB,IAAIqV,EAAO5U,EAAQF,KAAK8U,KACxB,GAAIA,EAAKsJ,YAEP,IADA,IAAIjC,EAAQrH,EAAKsJ,YACR9iB,EAAI,EAAGA,EAAI6gB,EAAMphB,OAAQO,IAChC6gB,EAAM7gB,GAAKoyB,EAAkBvR,EAAM7gB,SAE5BwZ,EAAKyF,QACdzF,EAAKyF,MAAQmT,EAAkB5Y,EAAKyF,QAEtC,MAAO,CAACra,EAASvB,EAAE+B,UAAUR,EAAST,GACxC,CAEA,SAASouB,EAAgB3tB,EAAS4tB,GAChC,OAAOnvB,EAAEovB,YAAY7tB,GAAW4tB,CAClC,CAyBAp2B,EAAOD,QAAU,CACfgJ,SAxBF,SAAkBP,EAAST,EAAYquB,GACrCA,OAA6B,IAAZA,EAA0B,OAAaA,EAWxD,IAVA,IAQIE,EAAUC,EAAS5yB,EARnB6yB,EAAa,CACfjT,EACAmS,EACAG,EAAgBlW,KAAK,KAAM,MAC3BkW,EAAgBlW,KAAK,KAAM,KAC3BkW,EAAgBlW,KAAK,KAAM,KAC3BuW,GAIMI,EAAWE,EAAWvyB,SAI5B,GAFAuE,GADA+tB,EAAUD,EAAS9tB,EAAST,IACV,IAClBpE,EAAS4yB,EAAQ,IACNn1B,QAAU+0B,EAAgBxyB,EAAOsF,MAAOmtB,GACjD,OAAOzyB,EAGX,OAAOA,CACT,EAME4f,IAAKA,EACLmS,eAAgBA,EAChBG,gBAAiBA,EACjBF,mBAAoBA,E,6BCvHtB,IAAIrsB,EAAQ,EAAQ,KAEhBmtB,EAAc,CAAC,EAgDnB,SAASltB,EAAOmtB,EAAG3Y,GACjB,OAAOA,IAAMgY,EAASW,EACxB,CAKA,SAASX,EAASW,GAChB,IAAIxc,SAAcwc,EAClB,MAAa,WAATxc,EACKA,EAEJwc,EAGDA,aAAa/0B,MACR,QAEF,CAAC,EAAEiE,SACPC,KAAK6wB,GACLl1B,MAAM,iBAAiB,GACvBwT,cARM,MASX,CAOA,SAASvF,EAAWgD,GAClB,OAAOlJ,EAAOkJ,EAAG,WACnB,CAOA,SAASkkB,EAAiBlkB,GACxB,IACImkB,EAAkBrX,SAASha,UAAUK,SACtCC,KAAKF,OAAOJ,UAAU0N,gBACtBhR,QAHgB,sBAGM,QACtBA,QAAQ,yDAA0D,SACjE40B,EAAa7c,OAAO,IAAM4c,EAAkB,KAChD,OAAOE,EAASrkB,IAAMokB,EAAWjsB,KAAK6H,EACxC,CAOA,SAASqkB,EAAS7tB,GAChB,IAAIoK,SAAcpK,EAClB,OAAgB,MAATA,IAA0B,UAARoK,GAA4B,YAARA,EAC/C,CAoEA,SAAS0jB,IACP,IAAIC,EAAI7a,IASR,MARW,uCAAuCla,QAChD,SACA,SAAUkd,GACR,IAAIrN,GAAKklB,EAAoB,GAAhBhT,KAAKiT,UAAiB,GAAK,EAExC,OADAD,EAAIhT,KAAKC,MAAM+S,EAAI,KACL,MAAN7X,EAAYrN,EAAS,EAAJA,EAAW,GAAKlM,SAAS,GACpD,GAGJ,CAyBA,IAAIsxB,EAAkB,CACpBC,YAAY,EACZ3hB,IAAK,CACH,SACA,WACA,YACA,WACA,OACA,WACA,OACA,OACA,WACA,OACA,YACA,OACA,QACA,UAEF4hB,EAAG,CACDld,KAAM,WACNmd,OAAQ,6BAEVA,OAAQ,CACNC,OACE,0IACFC,MACE,qMAiFN,SAASvuB,EAAU1D,EAAKkyB,GACtB,IAAIvuB,EAAO7H,EACX,IACE6H,EAAQwtB,EAAYztB,UAAU1D,EAChC,CAAE,MAAOmyB,GACP,GAAID,GAAU/nB,EAAW+nB,GACvB,IACEvuB,EAAQuuB,EAAOlyB,EACjB,CAAE,MAAOoyB,GACPt2B,EAAQs2B,CACV,MAEAt2B,EAAQq2B,CAEZ,CACA,MAAO,CAAEr2B,MAAOA,EAAO6H,MAAOA,EAChC,CA8EA,SAAS0uB,EAAahsB,EAAQ8G,GAC5B,OAAO,SAAU0R,EAAKhG,GACpB,IACE1L,EAAE0R,EAAKhG,EACT,CAAE,MAAOhb,GACPwI,EAAOvK,MAAM+B,EACf,CACF,CACF,CAEA,SAASy0B,EAAiBtyB,GA+BxB,OA5BA,SAAS+Y,EAAM/Y,EAAKguB,GAClB,IAAIrqB,EACFiR,EACA2d,EACAl0B,EAAS,CAAC,EAEZ,IACE,IAAKuW,KAAQ5U,GACX2D,EAAQ3D,EAAI4U,MAEE3Q,EAAON,EAAO,WAAaM,EAAON,EAAO,UACjDqqB,EAAK7U,SAASxV,GAChBtF,EAAOuW,GAAQ,+BAAiC6b,EAAS9sB,KAEzD4uB,EAAUvE,EAAK7wB,SACPqB,KAAKmF,GACbtF,EAAOuW,GAAQmE,EAAMpV,EAAO4uB,IAKhCl0B,EAAOuW,GAAQjR,CAEnB,CAAE,MAAO9F,GACPQ,EAAS,+BAAiCR,EAAEC,OAC9C,CACA,OAAOO,CACT,CACO0a,CAAM/Y,EA9BF,CAACA,GA+Bd,CAiIA,IAAIwyB,EAAkB,CACpB,MACA,UACA,MACA,aACA,QACA,UAEEC,EAAmB,CAAC,WAAY,QAAS,UAAW,OAAQ,SAEhE,SAASC,EAAc9a,EAAK0Y,GAC1B,IAAK,IAAI7uB,EAAI,EAAGA,EAAImW,EAAI7Z,SAAU0D,EAChC,GAAImW,EAAInW,KAAO6uB,EACb,OAAO,EAIX,OAAO,CACT,CAiHA,SAASzZ,IACP,OAAI9Q,KAAK8Q,KACC9Q,KAAK8Q,OAEP,IAAI9Q,IACd,CAgEArL,EAAOD,QAAU,CACfonB,8BAxgBF,SAAuClf,EAAaN,EAASsf,IAC3DA,EAASA,GAAU,CAAC,GACbtd,aAAe1B,EACtB,IACIlB,EADAkxB,EAAc,GAElB,IAAKlxB,KAAKkgB,EACJthB,OAAOJ,UAAU0N,eAAepN,KAAKohB,EAAQlgB,IAC/CkxB,EAAYn0B,KAAK,CAACiD,EAAGkgB,EAAOlgB,IAAInE,KAAK,MAGzC,IAAIknB,EAAQ,IAAMmO,EAAYC,OAAOt1B,KAAK,MAE1C+E,EAAUA,GAAW,CAAC,GACdN,KAAOM,EAAQN,MAAQ,GAC/B,IAEIvC,EAFAqzB,EAAKxwB,EAAQN,KAAKvF,QAAQ,KAC1Bs2B,EAAIzwB,EAAQN,KAAKvF,QAAQ,MAEjB,IAARq2B,KAAqB,IAAPC,GAAYA,EAAID,IAChCrzB,EAAI6C,EAAQN,KACZM,EAAQN,KAAOvC,EAAEF,UAAU,EAAGuzB,GAAMrO,EAAQ,IAAMhlB,EAAEF,UAAUuzB,EAAK,KAExD,IAAPC,GACFtzB,EAAI6C,EAAQN,KACZM,EAAQN,KAAOvC,EAAEF,UAAU,EAAGwzB,GAAKtO,EAAQhlB,EAAEF,UAAUwzB,IAEvDzwB,EAAQN,KAAOM,EAAQN,KAAOyiB,CAGpC,EA6eEnW,WAzUF,SAAoBzP,EAAMyH,EAAQ2B,EAAU+qB,EAAaC,GAOvD,IANA,IAAIl1B,EAAS+gB,EAAK7B,EAAQ/Z,EAAUkU,EAChC8b,EACAC,EAAY,GAEZC,EAAW,GAEN70B,EAAI,EAAG0iB,EAAIpiB,EAAKb,OAAQO,EAAI0iB,IAAK1iB,EAAG,CAG3C,IAAI80B,EAAM3C,EAFVwC,EAAMr0B,EAAKN,IAIX,OADA60B,EAAS30B,KAAK40B,GACNA,GACN,IAAK,YACH,MACF,IAAK,SACHt1B,EAAUo1B,EAAU10B,KAAKy0B,GAAQn1B,EAAUm1B,EAC3C,MACF,IAAK,WACHhwB,EAAWovB,EAAahsB,EAAQ4sB,GAChC,MACF,IAAK,OACHC,EAAU10B,KAAKy0B,GACf,MACF,IAAK,QACL,IAAK,eACL,IAAK,YACHpU,EAAMqU,EAAU10B,KAAKy0B,GAAQpU,EAAMoU,EACnC,MACF,IAAK,SACL,IAAK,QACH,GACEA,aAAe52B,OACU,oBAAjBg3B,cAAgCJ,aAAeI,aACvD,CACAxU,EAAMqU,EAAU10B,KAAKy0B,GAAQpU,EAAMoU,EACnC,KACF,CACA,GAAIF,GAAuB,WAARK,IAAqBjc,EAAS,CAC/C,IAAK,IAAI5V,EAAI,EAAGhD,EAAMw0B,EAAYh1B,OAAQwD,EAAIhD,IAAOgD,EACnD,QAA4B3E,IAAxBq2B,EAAIF,EAAYxxB,IAAmB,CACrC4V,EAAU8b,EACV,KACF,CAEF,GAAI9b,EACF,KAEJ,CACA6F,EAASkW,EAAU10B,KAAKy0B,GAAQjW,EAASiW,EACzC,MACF,QACE,GACEA,aAAe52B,OACU,oBAAjBg3B,cAAgCJ,aAAeI,aACvD,CACAxU,EAAMqU,EAAU10B,KAAKy0B,GAAQpU,EAAMoU,EACnC,KACF,CACAC,EAAU10B,KAAKy0B,GAErB,CAGIjW,IAAQA,EAASsV,EAAiBtV,IAElCkW,EAAUn1B,OAAS,IAChBif,IAAQA,EAASsV,EAAiB,CAAC,IACxCtV,EAAOkW,UAAYZ,EAAiBY,IAGtC,IAAIzoB,EAAO,CACT3M,QAASA,EACT+gB,IAAKA,EACL7B,OAAQA,EACRqD,UAAWxJ,IACX5T,SAAUA,EACV+E,SAAUA,EACV+M,WA1Ee,CAAC,EA2EhBnK,KAAM6mB,KAaR,OAGF,SAA2BhnB,EAAMuS,GAC3BA,QAA2BpgB,IAAjBogB,EAAO5Q,QACnB3B,EAAK2B,MAAQ4Q,EAAO5Q,aACb4Q,EAAO5Q,OAEZ4Q,QAAgCpgB,IAAtBogB,EAAOpD,aACnBnP,EAAKmP,WAAaoD,EAAOpD,kBAClBoD,EAAOpD,WAElB,CAtBE0Z,CAAkB7oB,EAAMuS,GAEpB+V,GAAe5b,IACjB1M,EAAK0M,QAAUA,GAEb6b,IACFvoB,EAAKuoB,cAAgBA,GAEvBvoB,EAAKwC,cAAgBrO,EACrB6L,EAAKsK,WAAWwe,mBAAqBJ,EAC9B1oB,CACT,EA6OEyU,gBAhOF,SAAyBzU,EAAM+oB,GAC7B,IAAIxW,EAASvS,EAAKzH,KAAKga,QAAU,CAAC,EAC9ByW,GAAe,EAEnB,IACE,IAAK,IAAIn1B,EAAI,EAAGA,EAAIk1B,EAAOz1B,SAAUO,EAC/Bk1B,EAAOl1B,GAAGqP,eAAe,oBAC3BqP,EAAShZ,EAAMgZ,EAAQsV,EAAiBkB,EAAOl1B,GAAGo1B,iBAClDD,GAAe,GAKfA,IACFhpB,EAAKzH,KAAKga,OAASA,EAEvB,CAAE,MAAOnf,GACP4M,EAAKsK,WAAW4e,cAAgB,WAAa91B,EAAEC,OACjD,CACF,EA8MEgQ,qBAxLF,SAA8BlP,GAI5B,IAHA,IAAImP,EAAMC,EAAU5B,EAChB6mB,EAEK30B,EAAI,EAAG0iB,EAAIpiB,EAAKb,OAAQO,EAAI0iB,IAAK1iB,EAIxC,OADUmyB,EAFVwC,EAAMr0B,EAAKN,KAIT,IAAK,UACEyP,GAAQ2kB,EAAcF,EAAiBS,GAC1CllB,EAAOklB,GACG7mB,GAASsmB,EAAcD,EAAkBQ,KACnD7mB,EAAQ6mB,GAEV,MACF,IAAK,SACHjlB,EAAWilB,EAYjB,MANY,CACVllB,KAAMA,GAAQ,SACdC,SAAUA,GAAY,CAAC,EACvB5B,MAAOA,EAIX,EA2JEwnB,SAnEF,SAAkB3E,EAAangB,GAC7B,GAAKmgB,GAAgBA,EAAqB,UAAmB,IAAdngB,EAA/C,CAGA,IAAI+kB,EAAQ5E,EAAqB,QACjC,GAAKngB,EAGH,IACE,IAAIrS,EACJ,IAA4B,IAAxBo3B,EAAMr3B,QAAQ,MAChBC,EAAQo3B,EAAMv4B,MAAM,MACdC,MACNkB,EAAM+B,KAAK,KACXq1B,EAAQp3B,EAAMa,KAAK,UACd,IAA4B,IAAxBu2B,EAAMr3B,QAAQ,MAEvB,IADAC,EAAQo3B,EAAMv4B,MAAM,MACVyC,OAAS,EAAG,CACpB,IAAI+1B,EAAYr3B,EAAMU,MAAM,EAAG,GAC3B42B,EAAWD,EAAU,GAAGt3B,QAAQ,MAClB,IAAdu3B,IACFD,EAAU,GAAKA,EAAU,GAAGx0B,UAAU,EAAGy0B,IAG3CF,EAAQC,EAAU/zB,OADH,4BACoBzC,KAAK,IAC1C,OAEAu2B,EAAQ,IAEZ,CAAE,MAAOh2B,GACPg2B,EAAQ,IACV,MAzBAA,EAAQ,KA2BV5E,EAAqB,QAAI4E,CA9BzB,CA+BF,EAkCEhhB,mBAvGF,SAA4BjU,GAC1B,IAAIN,EAAGC,EAAK00B,EACR50B,EAAS,GACb,IAAKC,EAAI,EAAGC,EAAMK,EAAKb,OAAQO,EAAIC,IAAOD,EAAG,CAE3C,OAAQmyB,EADRwC,EAAMr0B,EAAKN,KAET,IAAK,UAEH20B,GADAA,EAAMvvB,EAAUuvB,IACNn3B,OAASm3B,EAAItvB,OACf5F,OAAS,MACfk1B,EAAMA,EAAI7uB,OAAO,EAAG,KAAO,OAE7B,MACF,IAAK,OACH6uB,EAAM,OACN,MACF,IAAK,YACHA,EAAM,YACN,MACF,IAAK,SACHA,EAAMA,EAAI3yB,WAGdjC,EAAOG,KAAKy0B,EACd,CACA,OAAO50B,EAAOf,KAAK,IACrB,EA8EEwkB,UAhfF,SAAmBkS,EAAG9xB,GAWpB,KAVAA,EAAWA,GAAY8xB,EAAE9xB,WACR8xB,EAAE7xB,OACF,KAAX6xB,EAAE7xB,KACJD,EAAW,QACS,MAAX8xB,EAAE7xB,OACXD,EAAW,WAGfA,EAAWA,GAAY,UAElB8xB,EAAElyB,SACL,OAAO,KAET,IAAIzD,EAAS6D,EAAW,KAAO8xB,EAAElyB,SAOjC,OANIkyB,EAAE7xB,OACJ9D,EAASA,EAAS,IAAM21B,EAAE7xB,MAExB6xB,EAAEjyB,OACJ1D,GAAkB21B,EAAEjyB,MAEf1D,CACT,EA2dE0U,IApJF,SAAa/S,EAAK+B,GAChB,GAAK/B,EAAL,CAGA,IAAI+f,EAAOhe,EAAKzG,MAAM,KAClB+C,EAAS2B,EACb,IACE,IAAK,IAAI1B,EAAI,EAAGC,EAAMwhB,EAAKhiB,OAAQO,EAAIC,IAAOD,EAC5CD,EAASA,EAAO0hB,EAAKzhB,GAEzB,CAAE,MAAOT,GACPQ,OAASzB,CACX,CACA,OAAOyB,CAVP,CAWF,EAuIEyI,cAnCF,SAAuByU,EAASjG,EAAOpS,EAASmD,GAC9C,IAAIhI,EAAS2F,EAAMuX,EAASjG,EAAOpS,GAEnC,OADA7E,EAUF,SAAiCgE,EAASgE,GAWxC,OAVIhE,EAAQ4xB,gBAAkB5xB,EAAQslB,eACpCtlB,EAAQslB,aAAetlB,EAAQ4xB,cAC/B5xB,EAAQ4xB,mBAAgBr3B,EACxByJ,GAAUA,EAAOmE,IAAI,mDAEnBnI,EAAQ6xB,gBAAkB7xB,EAAQqlB,gBACpCrlB,EAAQqlB,cAAgBrlB,EAAQ6xB,cAChC7xB,EAAQ6xB,mBAAgBt3B,EACxByJ,GAAUA,EAAOmE,IAAI,oDAEhBnI,CACT,CAtBW8xB,CAAwB91B,EAAQgI,IACpCiP,GAASA,EAAM8e,sBAGhB9e,EAAMhH,cACRjQ,EAAOiQ,aAAeiN,EAAQjN,aAAe,IAAIvO,OAAOuV,EAAMhH,cAHvDjQ,CAMX,EA0BE6N,QA7nBF,SAAiBrO,GAEf,OAAOoG,EAAOpG,EAAG,UAAYoG,EAAOpG,EAAG,YACzC,EA2nBEglB,eA9pBF,SAAwB/jB,GACtB,OAAO0C,OAAOvC,SAASH,EACzB,EA6pBEqL,WAAYA,EACZkqB,WA3oBF,SAAoB/1B,GAClB,IAAIyP,EAAO0iB,EAASnyB,GACpB,MAAgB,WAATyP,GAA8B,UAATA,CAC9B,EAyoBEsjB,iBAAkBA,EAClBG,SAAUA,EACV8C,SA7qBF,SAAkB3wB,GAChB,MAAwB,iBAAVA,GAAsBA,aAAiBjC,MACvD,EA4qBEuC,OAAQA,EACRwrB,UA3nBF,SAAmBjwB,GACjB,OAAOgyB,EAAShyB,IAAMyE,EAAOzE,EAAEoZ,KAAM,WACvC,EA0nBE8K,UApbF,SAAmB6Q,GACjB,IAAI5wB,EAAO7H,EACX,IACE6H,EAAQwtB,EAAYt1B,MAAM04B,EAC5B,CAAE,MAAO12B,GACP/B,EAAQ+B,CACV,CACA,MAAO,CAAE/B,MAAOA,EAAO6H,MAAOA,EAChC,EA6aEokB,OAvmBW,CACXjd,MAAO,EACPC,KAAM,EACNE,QAAS,EACTnP,MAAO,EACPoP,SAAU,GAmmBVe,uBA5aF,SACEnO,EACA4E,EACAmJ,EACAC,EACAhQ,EACA04B,EACAC,EACA7tB,GAEA,IAAI3J,EAAW,CACbyF,IAAKA,GAAO,GACZ5F,KAAM+O,EACN0S,OAAQzS,GAEV7O,EAASqhB,KAAO1X,EAAYmf,kBAAkB9oB,EAASyF,IAAKzF,EAASH,MACrEG,EAASiH,QAAU0C,EAAY2e,cAActoB,EAASyF,IAAKzF,EAASH,MACpE,IAAIsY,EACkB,oBAAbzN,UACPA,UACAA,SAAS1K,UACT0K,SAAS1K,SAASmY,KAChBsf,EACgB,oBAAXl5B,QACPA,QACAA,OAAOykB,WACPzkB,OAAOykB,UAAUQ,UACnB,MAAO,CACL+T,KAAMA,EACN12B,QAAShC,EAAQ4F,OAAO5F,GAASgC,GAAW22B,EAC5C/xB,IAAK0S,EACLnZ,MAAO,CAACgB,GACRy3B,UAAWA,EAEf,EA2YE1wB,MAAOA,EACP6S,IAAKA,EACLyW,OA7nBF,WACE,MAAO,UACT,EA4nBE6D,YAAaA,EACb9S,YArmBF,SAAqB3b,GACnB,IAAIiyB,EA4CN,SAAkBx1B,GAChB,GAAK8E,EAAO9E,EAAK,UAAjB,CAQA,IAJA,IAAIy1B,EAAIhD,EACJiD,EAAID,EAAE7C,OAAO6C,EAAE/C,WAAa,SAAW,SAASn1B,KAAKyC,GACrD21B,EAAM,CAAC,EAEFx2B,EAAI,EAAG0iB,EAAI4T,EAAE1kB,IAAInS,OAAQO,EAAI0iB,IAAK1iB,EACzCw2B,EAAIF,EAAE1kB,IAAI5R,IAAMu2B,EAAEv2B,IAAM,GAU1B,OAPAw2B,EAAIF,EAAE9C,EAAEld,MAAQ,CAAC,EACjBkgB,EAAIF,EAAE1kB,IAAI,KAAKvT,QAAQi4B,EAAE9C,EAAEC,QAAQ,SAAUgD,EAAIC,EAAIC,GAC/CD,IACFF,EAAIF,EAAE9C,EAAEld,MAAMogB,GAAMC,EAExB,IAEOH,CAjBP,CAkBF,CAjEqBI,CAASxyB,GAC5B,OAAKiyB,GAKuB,KAAxBA,EAAaQ,SACfR,EAAaj3B,OAASi3B,EAAaj3B,OAAOf,QAAQ,IAAK,KAGzD+F,EAAMiyB,EAAaj3B,OAAOf,QAAQ,IAAMg4B,EAAanQ,MAAO,KARnD,WAUX,EAylBEtH,IAvJF,SAAald,EAAK+B,EAAM4B,GACtB,GAAK3D,EAAL,CAGA,IAAI+f,EAAOhe,EAAKzG,MAAM,KAClBiD,EAAMwhB,EAAKhiB,OACf,KAAIQ,EAAM,GAGV,GAAY,IAARA,EAIJ,IAGE,IAFA,IAAI62B,EAAOp1B,EAAI+f,EAAK,KAAO,CAAC,EACxBsV,EAAcD,EACT92B,EAAI,EAAGA,EAAIC,EAAM,IAAKD,EAC7B82B,EAAKrV,EAAKzhB,IAAM82B,EAAKrV,EAAKzhB,KAAO,CAAC,EAClC82B,EAAOA,EAAKrV,EAAKzhB,IAEnB82B,EAAKrV,EAAKxhB,EAAM,IAAMoF,EACtB3D,EAAI+f,EAAK,IAAMsV,CACjB,CAAE,MAAOx3B,GACP,MACF,MAdEmC,EAAI+f,EAAK,IAAMpc,CAPjB,CAsBF,EA+HEmG,UAvyBF,SAAmBzC,GACb8C,EAAWgnB,EAAYztB,YAAcyG,EAAWgnB,EAAYt1B,SAkIxDoI,EA9HMoV,KA8HI,eA5HZhS,GACEgqB,EAAiBhY,KAAK3V,aACxBytB,EAAYztB,UAAY2V,KAAK3V,WAE3B2tB,EAAiBhY,KAAKxd,SACxBs1B,EAAYt1B,MAAQwd,KAAKxd,SAIvBsO,EAAWkP,KAAK3V,aAClBytB,EAAYztB,UAAY2V,KAAK3V,WAE3ByG,EAAWkP,KAAKxd,SAClBs1B,EAAYt1B,MAAQwd,KAAKxd,SAI1BsO,EAAWgnB,EAAYztB,YAAeyG,EAAWgnB,EAAYt1B,QAChEwL,GAAgBA,EAAa8pB,GAEjC,EA6wBEztB,UAAWA,EACXqtB,YA7dF,SAAqBuE,GAanB,IAHA,IAAIC,EAAQ,EACRx3B,EAASu3B,EAAOv3B,OAEXO,EAAI,EAAGA,EAAIP,EAAQO,IAAK,CAC/B,IAAI6Z,EAAOmd,EAAOE,WAAWl3B,GACzB6Z,EAAO,IAETod,GAAgB,EACPpd,EAAO,KAEhBod,GAAgB,EACPpd,EAAO,QAEhBod,GAAgB,EAEpB,CAEA,OAAOA,CACT,EAkcE9E,SAAUA,EACVgB,MAAOA,E,uBC5xBT,SAASgE,EAAc7gB,GAIrB,MAHoB,iBAATA,IACTA,EAAOlT,OAAOkT,IAETA,EAAKlF,aACd,CAoBA,SAASgmB,EAAa1iB,GACpBnY,KAAKkC,IAAM,CAAC,EAERiW,aAAmB0iB,EACrB1iB,EAAQ2iB,SAAQ,SAAUhyB,EAAOiR,GAC/B/Z,KAAK+6B,OAAOhhB,EAAMjR,EACpB,GAAG9I,MACMqX,MAAM2jB,QAAQ7iB,GACvBA,EAAQ2iB,SAAQ,SAAU5e,GACxBlc,KAAK+6B,OAAO7e,EAAO,GAAIA,EAAO,GAChC,GAAGlc,MACMmY,GACT3S,OAAOy1B,oBAAoB9iB,GAAS2iB,SAAQ,SAAU/gB,GACpD/Z,KAAK+6B,OAAOhhB,EAAM5B,EAAQ4B,GAC5B,GAAG/Z,KAEP,CAEA66B,EAAaz1B,UAAU21B,OAAS,SAAUhhB,EAAMjR,GAC9CiR,EAAO6gB,EAAc7gB,GACrBjR,EAtCF,SAAwBA,GAItB,MAHqB,iBAAVA,IACTA,EAAQjC,OAAOiC,IAEVA,CACT,CAiCUoyB,CAAepyB,GACvB,IAAIqyB,EAAWn7B,KAAKkC,IAAI6X,GACxB/Z,KAAKkC,IAAI6X,GAAQohB,EAAWA,EAAW,KAAOryB,EAAQA,CACxD,EAEA+xB,EAAaz1B,UAAU8S,IAAM,SAAU6B,GAErC,OADAA,EAAO6gB,EAAc7gB,GACd/Z,KAAKo7B,IAAIrhB,GAAQ/Z,KAAKkC,IAAI6X,GAAQ,IAC3C,EAEA8gB,EAAaz1B,UAAUg2B,IAAM,SAAUrhB,GACrC,OAAO/Z,KAAKkC,IAAI4Q,eAAe8nB,EAAc7gB,GAC/C,EAEA8gB,EAAaz1B,UAAU01B,QAAU,SAAU1yB,EAAUizB,GACnD,IAAK,IAAIthB,KAAQ/Z,KAAKkC,IAChBlC,KAAKkC,IAAI4Q,eAAeiH,IAC1B3R,EAAS1C,KAAK21B,EAASr7B,KAAKkC,IAAI6X,GAAOA,EAAM/Z,KAGnD,EAEA66B,EAAaz1B,UAAUuZ,QAAU,WAC/B,IAAI2c,EAAQ,GAIZ,OAHAt7B,KAAK86B,SAAQ,SAAUhyB,EAAOiR,GAC5BuhB,EAAM33B,KAAK,CAACoW,EAAMjR,GACpB,IAzDF,SAAqBwyB,GAQnB,MAPe,CACbzc,KAAM,WACJ,IAAI/V,EAAQwyB,EAAMx3B,QAClB,MAAO,CAAEgb,UAAgB/c,IAAV+G,EAAqBA,MAAOA,EAC7C,EAIJ,CAiDSyyB,CAAYD,EACrB,EAEAz7B,EAAOD,QAnFP,SAAiBuY,GACf,MAAuB,oBAAZqjB,QACF,IAAIX,EAAa1iB,GAGnB,IAAIqjB,QAAQrjB,EACrB,C,6BChBA,IAAI3L,EAAe,EAAQ,KAE3B3M,EAAOD,QAAU4M,C,uBCMjB3M,EAAOD,QARP,SAAiBuF,EAAK4U,EAAMygB,EAAaphB,EAAclG,GACrD,IAAIwI,EAAOvW,EAAI4U,GACf5U,EAAI4U,GAAQygB,EAAY9e,GACpBtC,GACFA,EAAalG,GAAMvP,KAAK,CAACwB,EAAK4U,EAAM2B,GAExC,C,6BCNA,IAAI5U,EAAI,EAAQ,KAoDhBjH,EAAOD,QAlDP,SAAkBuF,EAAKse,EAAM0P,GAC3B,IAAIvsB,EAAGrB,EAAG9B,EAINg4B,EAHAC,EAAQ50B,EAAEsC,OAAOjE,EAAK,UACtB61B,EAAUl0B,EAAEsC,OAAOjE,EAAK,SACxB+f,EAAO,GAMX,GAFAiO,EAAOA,GAAQ,CAAEhuB,IAAK,GAAIw2B,OAAQ,IAE9BD,EAAO,CAGT,GAFAD,EAAYtI,EAAKhuB,IAAIxD,QAAQwD,GAEzBu2B,IAAwB,IAAfD,EAEX,OAAOtI,EAAKwI,OAAOF,IAActI,EAAKhuB,IAAIs2B,GAG5CtI,EAAKhuB,IAAIxB,KAAKwB,GACds2B,EAAYtI,EAAKhuB,IAAIjC,OAAS,CAChC,CAEA,GAAIw4B,EACF,IAAK90B,KAAKzB,EACJK,OAAOJ,UAAU0N,eAAepN,KAAKP,EAAKyB,IAC5Cse,EAAKvhB,KAAKiD,QAGT,GAAIo0B,EACT,IAAKv3B,EAAI,EAAGA,EAAI0B,EAAIjC,SAAUO,EAC5ByhB,EAAKvhB,KAAKF,GAId,IAAID,EAASk4B,EAAQ,CAAC,EAAI,GACtBE,GAAO,EACX,IAAKn4B,EAAI,EAAGA,EAAIyhB,EAAKhiB,SAAUO,EAE7B8B,EAAIJ,EADJyB,EAAIse,EAAKzhB,IAETD,EAAOoD,GAAK6c,EAAK7c,EAAGrB,EAAG4tB,GACvByI,EAAOA,GAAQp4B,EAAOoD,KAAOzB,EAAIyB,GAOnC,OAJI80B,IAAUE,IACZzI,EAAKwI,OAAOF,GAAaj4B,GAGnBo4B,EAAgBz2B,EAAT3B,CACjB,C,UCssBA3D,EAAOD,QAnmBe,SAAS4e,GAE7B,IAqCIqd,EACAC,EACAC,EACAC,EAgOIC,EAaA76B,EACA86B,EACA7mB,EACAvM,EACAqzB,EAUA1B,EAsBA2B,EAcAC,EAnUJC,EAAe,kIAGnB,SAAShqB,EAAErO,GAET,OAAOA,EAAI,GACP,IAAMA,EACNA,CACN,CAEA,SAASs4B,IACP,OAAOv8B,KAAKw8B,SACd,CA2BA,SAASC,EAAMhC,GAQb,OADA6B,EAAaI,UAAY,EAClBJ,EAAa7xB,KAAKgwB,GACrB,IAAOA,EAAO34B,QAAQw6B,GAAc,SAAU3mB,GAC9C,IAAIqJ,EAAI+c,EAAKpmB,GACb,MAAoB,iBAANqJ,EACVA,EACA,OAAS,OAASrJ,EAAEglB,WAAW,GAAGl1B,SAAS,KAAKnD,OAAO,EAC7D,IAAK,IACL,IAAOm4B,EAAS,GACpB,CAGA,SAASn2B,EAAI+Q,EAAKsnB,GAIhB,IAAIl5B,EACAmD,EACArB,EACArC,EAEA05B,EADAC,EAAOhB,EAEP/yB,EAAQ6zB,EAAOtnB,GAkBnB,OAdIvM,GAA0B,iBAAVA,GACQ,mBAAjBA,EAAMg0B,SACfh0B,EAAQA,EAAMg0B,OAAOznB,IAMJ,mBAAR2mB,IACTlzB,EAAQkzB,EAAIt2B,KAAKi3B,EAAQtnB,EAAKvM,WAKjBA,GACb,IAAK,SACH,OAAO2zB,EAAM3zB,GAEf,IAAK,SAIH,OAAO1E,SAAS0E,GACZjC,OAAOiC,GACP,OAEN,IAAK,UACL,IAAK,OAMH,OAAOjC,OAAOiC,GAKhB,IAAK,SAKH,IAAKA,EACH,MAAO,OAUT,GALA+yB,GAAOC,EACPc,EAAU,GAIqC,mBAA3Cp3B,OAAOJ,UAAUK,SAASuK,MAAMlH,GAA6B,CAM/D,IADA5F,EAAS4F,EAAM5F,OACVO,EAAI,EAAGA,EAAIP,EAAQO,GAAK,EAC3Bm5B,EAAQn5B,GAAKa,EAAIb,EAAGqF,IAAU,OAYhC,OANAvD,EAAuB,IAAnBq3B,EAAQ15B,OACR,KACA24B,EACA,MAAQA,EAAMe,EAAQn6B,KAAK,MAAQo5B,GAAO,KAAOgB,EAAO,IACxD,IAAMD,EAAQn6B,KAAK,KAAO,IAC9Bo5B,EAAMgB,EACCt3B,CACT,CAIA,GAAIy2B,GAAsB,iBAARA,EAEhB,IADA94B,EAAS84B,EAAI94B,OACRO,EAAI,EAAGA,EAAIP,EAAQO,GAAK,EACL,iBAAXu4B,EAAIv4B,KAEb8B,EAAIjB,EADJsC,EAAIo1B,EAAIv4B,GACGqF,KAET8zB,EAAQj5B,KAAK84B,EAAM71B,IACbi1B,EACE,KACA,KACEt2B,QAQhB,IAAKqB,KAAKkC,EACJtD,OAAOJ,UAAU0N,eAAepN,KAAKoD,EAAOlC,KAC9CrB,EAAIjB,EAAIsC,EAAGkC,KAET8zB,EAAQj5B,KAAK84B,EAAM71B,IACbi1B,EACE,KACA,KACEt2B,GAelB,OANAA,EAAuB,IAAnBq3B,EAAQ15B,OACR,KACA24B,EACA,MAAQA,EAAMe,EAAQn6B,KAAK,MAAQo5B,GAAO,KAAOgB,EAAO,IACxD,IAAMD,EAAQn6B,KAAK,KAAO,IAC9Bo5B,EAAMgB,EACCt3B,EAEb,CApLqC,mBAA1B2F,KAAK9F,UAAU03B,SAExB5xB,KAAK9F,UAAU03B,OAAS,WAEtB,OAAO14B,SAASpE,KAAKw8B,WACjBx8B,KAAK+8B,iBAAmB,IAC1BzqB,EAAEtS,KAAKg9B,cAAgB,GAAK,IAC5B1qB,EAAEtS,KAAKi9B,cAAgB,IACvB3qB,EAAEtS,KAAKk9B,eAAiB,IACxB5qB,EAAEtS,KAAKm9B,iBAAmB,IAC1B7qB,EAAEtS,KAAKo9B,iBAAmB,IACxB,IACN,EAEA32B,QAAQrB,UAAU03B,OAASP,EAC3B51B,OAAOvB,UAAU03B,OAASP,EAC1B11B,OAAOzB,UAAU03B,OAASP,GAwKE,mBAAnB/d,EAAK3V,YACdkzB,EAAO,CACL,KAAM,MACN,KAAM,MACN,KAAM,MACN,KAAM,MACN,KAAM,MACN,IAAM,MACN,KAAM,QAERvd,EAAK3V,UAAY,SAAUC,EAAOu0B,EAAUC,GAQ1C,IAAI75B,EAOJ,GANAo4B,EAAM,GACNC,EAAS,GAKY,iBAAVwB,EACT,IAAK75B,EAAI,EAAGA,EAAI65B,EAAO75B,GAAK,EAC1Bq4B,GAAU,QAKc,iBAAVwB,IAChBxB,EAASwB,GAOX,GADAtB,EAAMqB,EACFA,GAAgC,mBAAbA,IACE,iBAAbA,GACoB,iBAApBA,EAASn6B,QACnB,MAAM,IAAI1B,MAAM,kBAMlB,OAAO8C,EAAI,GAAI,CAAC,GAAIwE,GACtB,GAMwB,mBAAf0V,EAAKxd,QACdwd,EAAKxd,OAsBCm7B,EAAU,CACZ,KAAM,KACN,IAAM,IACN,IAAK,IACL,EAAK,KACL,EAAK,KACL,EAAK,KACL,EAAK,KACL,EAAK,MAEH1B,EAAS,CACX8C,GAAI,WACFtB,EAAQ,IACV,EACAuB,UAAW,WACTnoB,EAAMvM,EACNmzB,EAAQ,OACV,EACAwB,KAAM,WACJpoB,EAAMvM,EACNmzB,EAAQ,OACV,EACAyB,OAAQ,WACNzB,EAAQ,QACV,EACA0B,YAAa,WACX1B,EAAQ,QACV,EACA2B,OAAQ,WACN3B,EAAQ,QACV,GAEEG,EAAS,CACXmB,GAAI,WACFtB,EAAQ,IACV,EACAyB,OAAQ,WACNzB,EAAQ,QACV,EACA0B,YAAa,WACX1B,EAAQ,QACV,EACA2B,OAAQ,WACN3B,EAAQ,QACV,GAEEI,EAAS,CAOX,IAAK,CACHkB,GAAI,WACFn8B,EAAMuC,KAAK,CAACs4B,MAAO,OACnBC,EAAY,CAAC,EACbD,EAAQ,WACV,EACAyB,OAAQ,WACNt8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,SAAU5mB,IAAKA,IACxD6mB,EAAY,CAAC,EACbD,EAAQ,WACV,EACA0B,YAAa,WACXv8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,CAAC,EACbD,EAAQ,WACV,EACA2B,OAAQ,WACNx8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,CAAC,EACbD,EAAQ,WACV,GAEF,IAAK,CACHuB,UAAW,WACT,IAAI98B,EAAMU,EAAMV,MAChBoI,EAAQozB,EACRA,EAAYx7B,EAAIw7B,UAChB7mB,EAAM3U,EAAI2U,IACV4mB,EAAQv7B,EAAIu7B,KACd,EACA4B,OAAQ,WACN,IAAIn9B,EAAMU,EAAMV,MAChBw7B,EAAU7mB,GAAOvM,EACjBA,EAAQozB,EACRA,EAAYx7B,EAAIw7B,UAChB7mB,EAAM3U,EAAI2U,IACV4mB,EAAQv7B,EAAIu7B,KACd,GAEF,IAAK,CACHsB,GAAI,WACFn8B,EAAMuC,KAAK,CAACs4B,MAAO,OACnBC,EAAY,GACZD,EAAQ,aACV,EACAyB,OAAQ,WACNt8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,SAAU5mB,IAAKA,IACxD6mB,EAAY,GACZD,EAAQ,aACV,EACA0B,YAAa,WACXv8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,GACZD,EAAQ,aACV,EACA2B,OAAQ,WACNx8B,EAAMuC,KAAK,CAACu4B,UAAWA,EAAWD,MAAO,WACzCC,EAAY,GACZD,EAAQ,aACV,GAEF,IAAK,CACH0B,YAAa,WACX,IAAIj9B,EAAMU,EAAMV,MAChBoI,EAAQozB,EACRA,EAAYx7B,EAAIw7B,UAChB7mB,EAAM3U,EAAI2U,IACV4mB,EAAQv7B,EAAIu7B,KACd,EACA6B,OAAQ,WACN,IAAIp9B,EAAMU,EAAMV,MAChBw7B,EAAUv4B,KAAKmF,GACfA,EAAQozB,EACRA,EAAYx7B,EAAIw7B,UAChB7mB,EAAM3U,EAAI2U,IACV4mB,EAAQv7B,EAAIu7B,KACd,GAEF,IAAK,CACH8B,MAAO,WACL,GAAIv4B,OAAOsN,eAAepN,KAAKw2B,EAAW7mB,GACxC,MAAM,IAAI2oB,YAAY,kBAAoB3oB,EAAM,KAElD4mB,EAAQ,QACV,GAEF,IAAK,CACH4B,OAAQ,WACN3B,EAAU7mB,GAAOvM,EACjBmzB,EAAQ,MACV,EACA6B,OAAQ,WACN5B,EAAUv4B,KAAKmF,GACfmzB,EAAQ,QACV,GAEF,KAAQ,CACNsB,GAAI,WACFz0B,GAAQ,EACRmzB,EAAQ,IACV,EACAyB,OAAQ,WACN50B,GAAQ,EACRmzB,EAAQ,QACV,EACA0B,YAAa,WACX70B,GAAQ,EACRmzB,EAAQ,QACV,EACA2B,OAAQ,WACN90B,GAAQ,EACRmzB,EAAQ,QACV,GAEF,MAAS,CACPsB,GAAI,WACFz0B,GAAQ,EACRmzB,EAAQ,IACV,EACAyB,OAAQ,WACN50B,GAAQ,EACRmzB,EAAQ,QACV,EACA0B,YAAa,WACX70B,GAAQ,EACRmzB,EAAQ,QACV,EACA2B,OAAQ,WACN90B,GAAQ,EACRmzB,EAAQ,QACV,GAEF,KAAQ,CACNsB,GAAI,WACFz0B,EAAQ,KACRmzB,EAAQ,IACV,EACAyB,OAAQ,WACN50B,EAAQ,KACRmzB,EAAQ,QACV,EACA0B,YAAa,WACX70B,EAAQ,KACRmzB,EAAQ,QACV,EACA2B,OAAQ,WACN90B,EAAQ,KACRmzB,EAAQ,QACV,IAeG,SAAUp5B,EAAQo7B,GAKvB,IAAIz6B,EAhBkBya,EAiBlBigB,EAAK,iJAITjC,EAAQ,KAKR76B,EAAQ,GAIR,IAIE,KACEoC,EAAS06B,EAAGr8B,KAAKgB,IAWbW,EAAO,GAIT64B,EAAO74B,EAAO,IAAIy4B,KAETz4B,EAAO,IAKhBsF,GAAStF,EAAO,GAChB44B,EAAOH,OA1DShe,EAgEOza,EAAO,GAA9BsF,EA5DCmV,EAAKnc,QAAQ,yBAAyB,SAAUq8B,EAAQ9kB,EAAG2F,GAChE,OAAO3F,EACHxS,OAAOu3B,aAAaxU,SAASvQ,EAAG,KAChC8iB,EAAQnd,EACd,IAyDMyb,EAAOwB,MAOTp5B,EAASA,EAAOP,MAAMkB,EAAO,GAAGN,OAMpC,CAAE,MAAOF,GACPi5B,EAAQj5B,CACV,CAMA,GAAc,OAAVi5B,GAAmB,kBAAkBxxB,KAAK5H,GAC5C,MAAOo5B,aAAiB+B,YACpB/B,EACA,IAAI+B,YAAY,QAStB,MAA2B,mBAAZC,EACV,SAASI,EAAK1B,EAAQtnB,GACvB,IAAIzO,EACArB,EACAkwB,EAAMkH,EAAOtnB,GACjB,GAAIogB,GAAsB,iBAARA,EAChB,IAAK7uB,KAAKkC,EACJtD,OAAOJ,UAAU0N,eAAepN,KAAK+vB,EAAK7uB,UAElC7E,KADVwD,EAAI84B,EAAK5I,EAAK7uB,IAEZ6uB,EAAI7uB,GAAKrB,SAEFkwB,EAAI7uB,IAKnB,OAAOq3B,EAAQv4B,KAAKi3B,EAAQtnB,EAAKogB,EACnC,CAjBE,CAiBA,CAAC,GAAI3sB,GAAQ,IACfA,CACJ,GAGN,C,GCvvBIw1B,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBz8B,IAAjB08B,EACH,OAAOA,EAAa7+B,QAGrB,IAAIC,EAASy+B,EAAyBE,GAAY,CAGjD5+B,QAAS,CAAC,GAOX,OAHA8+B,EAAoBF,GAAU94B,KAAK7F,EAAOD,QAASC,EAAQA,EAAOD,QAAS2+B,GAGpE1+B,EAAOD,OACf,CCnB0B2+B,CAAoB,K,MDF1CD,C", "sources": ["webpack://rollbar/webpack/universalModuleDefinition", "webpack://rollbar/./node_modules/console-polyfill/index.js", "webpack://rollbar/./node_modules/error-stack-parser/error-stack-parser.js", "webpack://rollbar/./node_modules/error-stack-parser/node_modules/stackframe/stackframe.js", "webpack://rollbar/./src/api.js", "webpack://rollbar/./src/apiUtility.js", "webpack://rollbar/./src/browser/bundles/rollbar.js", "webpack://rollbar/./src/browser/core.js", "webpack://rollbar/./src/browser/defaults/scrubFields.js", "webpack://rollbar/./src/browser/detection.js", "webpack://rollbar/./src/browser/domUtility.js", "webpack://rollbar/./src/browser/globalSetup.js", "webpack://rollbar/./src/browser/logger.js", "webpack://rollbar/./src/browser/predicates.js", "webpack://rollbar/./src/browser/rollbar.js", "webpack://rollbar/./src/browser/telemetry.js", "webpack://rollbar/./src/browser/transforms.js", "webpack://rollbar/./src/browser/transport.js", "webpack://rollbar/./src/browser/transport/fetch.js", "webpack://rollbar/./src/browser/transport/xhr.js", "webpack://rollbar/./src/browser/url.js", "webpack://rollbar/./src/browser/wrapGlobals.js", "webpack://rollbar/./src/defaults.js", "webpack://rollbar/./src/errorParser.js", "webpack://rollbar/./src/merge.js", "webpack://rollbar/./src/notifier.js", "webpack://rollbar/./src/predicates.js", "webpack://rollbar/./src/queue.js", "webpack://rollbar/./src/rateLimiter.js", "webpack://rollbar/./src/rollbar.js", "webpack://rollbar/./src/scrub.js", "webpack://rollbar/./src/telemetry.js", "webpack://rollbar/./src/transforms.js", "webpack://rollbar/./src/truncation.js", "webpack://rollbar/./src/utility.js", "webpack://rollbar/./src/utility/headers.js", "webpack://rollbar/./src/utility/polyfillJSON.js", "webpack://rollbar/./src/utility/replace.js", "webpack://rollbar/./src/utility/traverse.js", "webpack://rollbar/./vendor/JSON-js/json3.js", "webpack://rollbar/webpack/bootstrap", "webpack://rollbar/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"rollbar\"] = factory();\n\telse\n\t\troot[\"rollbar\"] = factory();\n})(this, () => {\nreturn ", "// Console-polyfill. MIT license.\n// https://github.com/paulmillr/console-polyfill\n// Make it safe to do console.log() always.\n(function(global) {\n  'use strict';\n  if (!global.console) {\n    global.console = {};\n  }\n  var con = global.console;\n  var prop, method;\n  var dummy = function() {};\n  var properties = ['memory'];\n  var methods = ('assert,clear,count,debug,dir,dirxml,error,exception,group,' +\n     'groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,' +\n     'show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn').split(',');\n  while (prop = properties.pop()) if (!con[prop]) con[prop] = {};\n  while (method = methods.pop()) if (!con[method]) con[method] = dummy;\n  // Using `this` for web workers & supports Browserify / Webpack.\n})(typeof window === 'undefined' ? this : window);\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n    var objectProps = ['evalOrigin'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps, objectProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "'use strict';\n\nvar _ = require('./utility');\nvar helpers = require('./apiUtility');\n\nvar defaultOptions = {\n  hostname: 'api.rollbar.com',\n  path: '/api/1/item/',\n  search: null,\n  version: '1',\n  protocol: 'https:',\n  port: 443,\n};\n\n/**\n * Api is an object that encapsulates methods of communicating with\n * the Rollbar API.  It is a standard interface with some parts implemented\n * differently for server or browser contexts.  It is an object that should\n * be instantiated when used so it can contain non-global options that may\n * be different for another instance of RollbarApi.\n *\n * @param options {\n *    accessToken: the accessToken to use for posting items to rollbar\n *    endpoint: an alternative endpoint to send errors to\n *        must be a valid, fully qualified URL.\n *        The default is: https://api.rollbar.com/api/1/item\n *    proxy: if you wish to proxy requests provide an object\n *        with the following keys:\n *          host or hostname (required): foo.example.com\n *          port (optional): 123\n *          protocol (optional): https\n * }\n */\nfunction Api(options, transport, urllib, truncation, jsonBackup) {\n  this.options = options;\n  this.transport = transport;\n  this.url = urllib;\n  this.truncation = truncation;\n  this.jsonBackup = jsonBackup;\n  this.accessToken = options.accessToken;\n  this.transportOptions = _getTransport(options, urllib);\n}\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.postItem = function (data, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n  var self = this;\n\n  // ensure the network request is scheduled after the current tick.\n  setTimeout(function () {\n    self.transport.post(self.accessToken, transportOptions, payload, callback);\n  }, 0);\n};\n\n/**\n *\n * @param data\n * @param callback\n */\nApi.prototype.buildJsonPayload = function (data, callback) {\n  var payload = helpers.buildPayload(this.accessToken, data, this.jsonBackup);\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n\n  if (stringifyResult.error) {\n    if (callback) {\n      callback(stringifyResult.error);\n    }\n    return null;\n  }\n\n  return stringifyResult.value;\n};\n\n/**\n *\n * @param jsonPayload\n * @param callback\n */\nApi.prototype.postJsonPayload = function (jsonPayload, callback) {\n  var transportOptions = helpers.transportOptions(\n    this.transportOptions,\n    'POST',\n  );\n  this.transport.postJsonPayload(\n    this.accessToken,\n    transportOptions,\n    jsonPayload,\n    callback,\n  );\n};\n\nApi.prototype.configure = function (options) {\n  var oldOptions = this.oldOptions;\n  this.options = _.merge(oldOptions, options);\n  this.transportOptions = _getTransport(this.options, this.url);\n  if (this.options.accessToken !== undefined) {\n    this.accessToken = this.options.accessToken;\n  }\n  return this;\n};\n\nfunction _getTransport(options, url) {\n  return helpers.getTransportFromOptions(options, defaultOptions, url);\n}\n\nmodule.exports = Api;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction buildPayload(accessToken, data, jsonBackup) {\n  if (!_.isType(data.context, 'string')) {\n    var contextResult = _.stringify(data.context, jsonBackup);\n    if (contextResult.error) {\n      data.context = \"Error: could not serialize 'context'\";\n    } else {\n      data.context = contextResult.value || '';\n    }\n    if (data.context.length > 255) {\n      data.context = data.context.substr(0, 255);\n    }\n  }\n  return {\n    access_token: accessToken,\n    data: data,\n  };\n}\n\nfunction getTransportFromOptions(options, defaults, url) {\n  var hostname = defaults.hostname;\n  var protocol = defaults.protocol;\n  var port = defaults.port;\n  var path = defaults.path;\n  var search = defaults.search;\n  var timeout = options.timeout;\n  var transport = detectTransport(options);\n\n  var proxy = options.proxy;\n  if (options.endpoint) {\n    var opts = url.parse(options.endpoint);\n    hostname = opts.hostname;\n    protocol = opts.protocol;\n    port = opts.port;\n    path = opts.pathname;\n    search = opts.search;\n  }\n  return {\n    timeout: timeout,\n    hostname: hostname,\n    protocol: protocol,\n    port: port,\n    path: path,\n    search: search,\n    proxy: proxy,\n    transport: transport,\n  };\n}\n\nfunction detectTransport(options) {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var transport = options.defaultTransport || 'xhr';\n  if (typeof gWindow.fetch === 'undefined') transport = 'xhr';\n  if (typeof gWindow.XMLHttpRequest === 'undefined') transport = 'fetch';\n  return transport;\n}\n\nfunction transportOptions(transport, method) {\n  var protocol = transport.protocol || 'https:';\n  var port =\n    transport.port ||\n    (protocol === 'http:' ? 80 : protocol === 'https:' ? 443 : undefined);\n  var hostname = transport.hostname;\n  var path = transport.path;\n  var timeout = transport.timeout;\n  var transportAPI = transport.transport;\n  if (transport.search) {\n    path = path + transport.search;\n  }\n  if (transport.proxy) {\n    path = protocol + '//' + hostname + path;\n    hostname = transport.proxy.host || transport.proxy.hostname;\n    port = transport.proxy.port;\n    protocol = transport.proxy.protocol || protocol;\n  }\n  return {\n    timeout: timeout,\n    protocol: protocol,\n    hostname: hostname,\n    path: path,\n    port: port,\n    method: method,\n    transport: transportAPI,\n  };\n}\n\nfunction appendPathToPath(base, path) {\n  var baseTrailingSlash = /\\/$/.test(base);\n  var pathBeginningSlash = /^\\//.test(path);\n\n  if (baseTrailingSlash && pathBeginningSlash) {\n    path = path.substring(1);\n  } else if (!baseTrailingSlash && !pathBeginningSlash) {\n    path = '/' + path;\n  }\n\n  return base + path;\n}\n\nmodule.exports = {\n  buildPayload: buildPayload,\n  getTransportFromOptions: getTransportFromOptions,\n  transportOptions: transportOptions,\n  appendPathToPath: appendPathToPath,\n};\n", "'use strict';\n\nvar rollbar = require('../rollbar');\n\nvar options = (typeof window !== 'undefined') && window._rollbarConfig;\nvar alias = options && options.globalAlias || 'Rollbar';\nvar shimRunning = (typeof window !== 'undefined') && window[alias] && typeof window[alias].shimId === 'function' && window[alias].shimId() !== undefined;\n\nif ((typeof window !== 'undefined') && !window._rollbarStartTime) {\n  window._rollbarStartTime = (new Date()).getTime();\n}\n\nif (!shimRunning && options) {\n  var Rollbar = new rollbar(options);\n  window[alias] = Rollbar;\n} else if (typeof window !== 'undefined') {\n  window.rollbar = rollbar;\n  window._rollbarDidLoad = true;\n} else if (typeof self !== 'undefined') {\n  self.rollbar = rollbar;\n  self._rollbarDidLoad = true;\n}\n\nmodule.exports = rollbar;\n", "'use strict';\n\nvar Client = require('../rollbar');\nvar _ = require('../utility');\nvar API = require('../api');\nvar logger = require('./logger');\nvar globals = require('./globalSetup');\n\nvar Transport = require('./transport');\nvar urllib = require('./url');\n\nvar transforms = require('./transforms');\nvar sharedTransforms = require('../transforms');\nvar predicates = require('./predicates');\nvar sharedPredicates = require('../predicates');\nvar errorParser = require('../errorParser');\n\nfunction Rollbar(options, client) {\n  this.options = _.handleOptions(defaultOptions, options, null, logger);\n  this.options._configuredOptions = options;\n  var Telemeter = this.components.telemeter;\n  var Instrumenter = this.components.instrumenter;\n  var polyfillJSON = this.components.polyfillJSON;\n  this.wrapGlobals = this.components.wrapGlobals;\n  this.scrub = this.components.scrub;\n  var truncation = this.components.truncation;\n\n  var transport = new Transport(truncation);\n  var api = new API(this.options, transport, urllib, truncation);\n  if (Telemeter) {\n    this.telemeter = new Telemeter(this.options);\n  }\n  this.client =\n    client || new Client(this.options, api, logger, this.telemeter, 'browser');\n  var gWindow = _gWindow();\n  var gDocument = typeof document != 'undefined' && document;\n  this.isChrome = gWindow.chrome && gWindow.chrome.runtime; // check .runtime to avoid Edge browsers\n  this.anonymousErrorsPending = 0;\n  addTransformsToNotifier(this.client.notifier, this, gWindow);\n  addPredicatesToQueue(this.client.queue);\n  this.setupUnhandledCapture();\n  if (Instrumenter) {\n    this.instrumenter = new Instrumenter(\n      this.options,\n      this.client.telemeter,\n      this,\n      gWindow,\n      gDocument,\n    );\n    this.instrumenter.instrument();\n  }\n  _.setupJSON(polyfillJSON);\n\n  // Used with rollbar-react for rollbar-react-native compatibility.\n  this.rollbar = this;\n}\n\nvar _instance = null;\nRollbar.init = function (options, client) {\n  if (_instance) {\n    return _instance.global(options).configure(options);\n  }\n  _instance = new Rollbar(options, client);\n  return _instance;\n};\n\nRollbar.prototype.components = {};\n\nRollbar.setComponents = function (components) {\n  Rollbar.prototype.components = components;\n};\n\nfunction handleUninitialized(maybeCallback) {\n  var message = 'Rollbar is not initialized';\n  logger.error(message);\n  if (maybeCallback) {\n    maybeCallback(new Error(message));\n  }\n}\n\nRollbar.prototype.global = function (options) {\n  this.client.global(options);\n  return this;\n};\nRollbar.global = function (options) {\n  if (_instance) {\n    return _instance.global(options);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n  this.options = _.handleOptions(oldOptions, options, payload, logger);\n  this.options._configuredOptions = _.handleOptions(\n    oldOptions._configuredOptions,\n    options,\n    payload,\n  );\n  this.client.configure(this.options, payloadData);\n  this.instrumenter && this.instrumenter.configure(this.options);\n  this.setupUnhandledCapture();\n  return this;\n};\nRollbar.configure = function (options, payloadData) {\n  if (_instance) {\n    return _instance.configure(options, payloadData);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.lastError = function () {\n  return this.client.lastError;\n};\nRollbar.lastError = function () {\n  if (_instance) {\n    return _instance.lastError();\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.log = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.log(item);\n  return { uuid: uuid };\n};\nRollbar.log = function () {\n  if (_instance) {\n    return _instance.log.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.debug = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.debug(item);\n  return { uuid: uuid };\n};\nRollbar.debug = function () {\n  if (_instance) {\n    return _instance.debug.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.info = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.info(item);\n  return { uuid: uuid };\n};\nRollbar.info = function () {\n  if (_instance) {\n    return _instance.info.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warn = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warn(item);\n  return { uuid: uuid };\n};\nRollbar.warn = function () {\n  if (_instance) {\n    return _instance.warn.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.warning = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.warning(item);\n  return { uuid: uuid };\n};\nRollbar.warning = function () {\n  if (_instance) {\n    return _instance.warning.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.error = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.error(item);\n  return { uuid: uuid };\n};\nRollbar.error = function () {\n  if (_instance) {\n    return _instance.error.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.critical = function () {\n  var item = this._createItem(arguments);\n  var uuid = item.uuid;\n  this.client.critical(item);\n  return { uuid: uuid };\n};\nRollbar.critical = function () {\n  if (_instance) {\n    return _instance.critical.apply(_instance, arguments);\n  } else {\n    var maybeCallback = _getFirstFunction(arguments);\n    handleUninitialized(maybeCallback);\n  }\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.client.buildJsonPayload(item);\n};\nRollbar.buildJsonPayload = function () {\n  if (_instance) {\n    return _instance.buildJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  return this.client.sendJsonPayload(jsonPayload);\n};\nRollbar.sendJsonPayload = function () {\n  if (_instance) {\n    return _instance.sendJsonPayload.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.setupUnhandledCapture = function () {\n  var gWindow = _gWindow();\n\n  if (!this.unhandledExceptionsInitialized) {\n    if (this.options.captureUncaught || this.options.handleUncaughtExceptions) {\n      globals.captureUncaughtExceptions(gWindow, this);\n      if (this.wrapGlobals && this.options.wrapGlobalEventHandlers) {\n        this.wrapGlobals(gWindow, this);\n      }\n      this.unhandledExceptionsInitialized = true;\n    }\n  }\n  if (!this.unhandledRejectionsInitialized) {\n    if (\n      this.options.captureUnhandledRejections ||\n      this.options.handleUnhandledRejections\n    ) {\n      globals.captureUnhandledRejections(gWindow, this);\n      this.unhandledRejectionsInitialized = true;\n    }\n  }\n};\n\nRollbar.prototype.handleUncaughtException = function (\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  context,\n) {\n  if (!this.options.captureUncaught && !this.options.handleUncaughtExceptions) {\n    return;\n  }\n\n  // Chrome will always send 5+ arguments and error will be valid or null, not undefined.\n  // If error is undefined, we have a different caller.\n  // Chrome also sends errors from web workers with null error, but does not invoke\n  // prepareStackTrace() for these. Test for empty url to skip them.\n  if (\n    this.options.inspectAnonymousErrors &&\n    this.isChrome &&\n    error === null &&\n    url === ''\n  ) {\n    return 'anonymous';\n  }\n\n  var item;\n  var stackInfo = _.makeUnhandledStackInfo(\n    message,\n    url,\n    lineno,\n    colno,\n    error,\n    'onerror',\n    'uncaught exception',\n    errorParser,\n  );\n  if (_.isError(error)) {\n    item = this._createItem([message, error, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else if (_.isError(url)) {\n    item = this._createItem([message, url, context]);\n    item._unhandledStackInfo = stackInfo;\n  } else {\n    item = this._createItem([message, context]);\n    item.stackInfo = stackInfo;\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  this.client.log(item);\n};\n\n/**\n * Chrome only. Other browsers will ignore.\n *\n * Use Error.prepareStackTrace to extract information about errors that\n * do not have a valid error object in onerror().\n *\n * In tested version of Chrome, onerror is called first but has no way\n * to communicate with prepareStackTrace. Use a counter to let this\n * handler know which errors to send to Rollbar.\n *\n * In config options, set inspectAnonymousErrors to enable.\n */\nRollbar.prototype.handleAnonymousErrors = function () {\n  if (!this.options.inspectAnonymousErrors || !this.isChrome) {\n    return;\n  }\n\n  var r = this;\n  function prepareStackTrace(error, _stack) {\n    // eslint-disable-line no-unused-vars\n    if (r.options.inspectAnonymousErrors) {\n      if (r.anonymousErrorsPending) {\n        // This is the only known way to detect that onerror saw an anonymous error.\n        // It depends on onerror reliably being called before Error.prepareStackTrace,\n        // which so far holds true on tested versions of Chrome. If versions of Chrome\n        // are tested that behave differently, this logic will need to be updated\n        // accordingly.\n        r.anonymousErrorsPending -= 1;\n\n        if (!error) {\n          // Not likely to get here, but calling handleUncaughtException from here\n          // without an error object would throw off the anonymousErrorsPending counter,\n          // so return now.\n          return;\n        }\n\n        // Allow this to be tracked later.\n        error._isAnonymous = true;\n\n        // url, lineno, colno shouldn't be needed for these errors.\n        // If that changes, update this accordingly, using the unused\n        // _stack param as needed (rather than parse error.toString()).\n        r.handleUncaughtException(error.message, null, null, null, error);\n      }\n    }\n\n    // Workaround to ensure stack is preserved for normal errors.\n    return error.stack;\n  }\n\n  // https://v8.dev/docs/stack-trace-api\n  try {\n    Error.prepareStackTrace = prepareStackTrace;\n  } catch (e) {\n    this.options.inspectAnonymousErrors = false;\n    this.error('anonymous error handler failed', e);\n  }\n};\n\nRollbar.prototype.handleUnhandledRejection = function (reason, promise) {\n  if (\n    !this.options.captureUnhandledRejections &&\n    !this.options.handleUnhandledRejections\n  ) {\n    return;\n  }\n\n  var message = 'unhandled rejection was null or undefined!';\n  if (reason) {\n    if (reason.message) {\n      message = reason.message;\n    } else {\n      var reasonResult = _.stringify(reason);\n      if (reasonResult.value) {\n        message = reasonResult.value;\n      }\n    }\n  }\n  var context =\n    (reason && reason._rollbarContext) || (promise && promise._rollbarContext);\n\n  var item;\n  if (_.isError(reason)) {\n    item = this._createItem([message, reason, context]);\n  } else {\n    item = this._createItem([message, reason, context]);\n    item.stackInfo = _.makeUnhandledStackInfo(\n      message,\n      '',\n      0,\n      0,\n      null,\n      'unhandledrejection',\n      '',\n      errorParser,\n    );\n  }\n  item.level = this.options.uncaughtErrorLevel;\n  item._isUncaught = true;\n  item._originalArgs = item._originalArgs || [];\n  item._originalArgs.push(promise);\n  this.client.log(item);\n};\n\nRollbar.prototype.wrap = function (f, context, _before) {\n  try {\n    var ctxFn;\n    if (_.isFunction(context)) {\n      ctxFn = context;\n    } else {\n      ctxFn = function () {\n        return context || {};\n      };\n    }\n\n    if (!_.isFunction(f)) {\n      return f;\n    }\n\n    if (f._isWrap) {\n      return f;\n    }\n\n    if (!f._rollbar_wrapped) {\n      f._rollbar_wrapped = function () {\n        if (_before && _.isFunction(_before)) {\n          _before.apply(this, arguments);\n        }\n        try {\n          return f.apply(this, arguments);\n        } catch (exc) {\n          var e = exc;\n          if (e && window._rollbarWrappedError !== e) {\n            if (_.isType(e, 'string')) {\n              e = new String(e);\n            }\n            e._rollbarContext = ctxFn() || {};\n            e._rollbarContext._wrappedSource = f.toString();\n\n            window._rollbarWrappedError = e;\n          }\n          throw e;\n        }\n      };\n\n      f._rollbar_wrapped._isWrap = true;\n\n      if (f.hasOwnProperty) {\n        for (var prop in f) {\n          if (f.hasOwnProperty(prop) && prop !== '_rollbar_wrapped') {\n            f._rollbar_wrapped[prop] = f[prop];\n          }\n        }\n      }\n    }\n\n    return f._rollbar_wrapped;\n  } catch (e) {\n    // Return the original function if the wrap fails.\n    return f;\n  }\n};\nRollbar.wrap = function (f, context) {\n  if (_instance) {\n    return _instance.wrap(f, context);\n  } else {\n    handleUninitialized();\n  }\n};\n\nRollbar.prototype.captureEvent = function () {\n  var event = _.createTelemetryEvent(arguments);\n  return this.client.captureEvent(event.type, event.metadata, event.level);\n};\nRollbar.captureEvent = function () {\n  if (_instance) {\n    return _instance.captureEvent.apply(_instance, arguments);\n  } else {\n    handleUninitialized();\n  }\n};\n\n// The following two methods are used internally and are not meant for public use\nRollbar.prototype.captureDomContentLoaded = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (e, ts) {\n  if (!ts) {\n    ts = new Date();\n  }\n  return this.client.captureLoad(ts);\n};\n\n/* Internal */\n\nfunction addTransformsToNotifier(notifier, rollbar, gWindow) {\n  notifier\n    .addTransform(transforms.handleDomException)\n    .addTransform(transforms.handleItemWithError)\n    .addTransform(transforms.ensureItemHasSomethingToSay)\n    .addTransform(transforms.addBaseInfo)\n    .addTransform(transforms.addRequestInfo(gWindow))\n    .addTransform(transforms.addClientInfo(gWindow))\n    .addTransform(transforms.addPluginInfo(gWindow))\n    .addTransform(transforms.addBody)\n    .addTransform(sharedTransforms.addMessageWithError)\n    .addTransform(sharedTransforms.addTelemetryData)\n    .addTransform(sharedTransforms.addConfigToPayload)\n    .addTransform(transforms.addScrubber(rollbar.scrub))\n    .addTransform(sharedTransforms.addPayloadOptions)\n    .addTransform(sharedTransforms.userTransform(logger))\n    .addTransform(sharedTransforms.addConfiguredOptions)\n    .addTransform(sharedTransforms.addDiagnosticKeys)\n    .addTransform(sharedTransforms.itemToPayload);\n}\n\nfunction addPredicatesToQueue(queue) {\n  queue\n    .addPredicate(sharedPredicates.checkLevel)\n    .addPredicate(predicates.checkIgnore)\n    .addPredicate(sharedPredicates.userCheckIgnore(logger))\n    .addPredicate(sharedPredicates.urlIsNotBlockListed(logger))\n    .addPredicate(sharedPredicates.urlIsSafeListed(logger))\n    .addPredicate(sharedPredicates.messageIsIgnored(logger));\n}\n\nRollbar.prototype.loadFull = function () {\n  logger.info(\n    'Unexpected Rollbar.loadFull() called on a Notifier instance. This can happen when Rollbar is loaded multiple times.',\n  );\n};\n\nRollbar.prototype._createItem = function (args) {\n  return _.createItem(args, logger, this);\n};\n\nfunction _getFirstFunction(args) {\n  for (var i = 0, len = args.length; i < len; ++i) {\n    if (_.isFunction(args[i])) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\nfunction _gWindow() {\n  return (\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self)\n  );\n}\n\nvar defaults = require('../defaults');\nvar scrubFields = require('./defaults/scrubFields');\n\nvar defaultOptions = {\n  version: defaults.version,\n  scrubFields: scrubFields.scrubFields,\n  logLevel: defaults.logLevel,\n  reportLevel: defaults.reportLevel,\n  uncaughtErrorLevel: defaults.uncaughtErrorLevel,\n  endpoint: defaults.endpoint,\n  verbose: false,\n  enabled: true,\n  transmit: true,\n  sendConfig: false,\n  includeItemsInTelemetry: true,\n  captureIp: true,\n  inspectAnonymousErrors: true,\n  ignoreDuplicateErrors: true,\n  wrapGlobalEventHandlers: false,\n};\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nmodule.exports = {\n  scrubFields: [\n    'pw',\n    'pass',\n    'passwd',\n    'password',\n    'secret',\n    'confirm_password',\n    'confirmPassword',\n    'password_confirmation',\n    'passwordConfirmation',\n    'access_token',\n    'accessToken',\n    'X-Rollbar-Access-Token',\n    'secret_key',\n    'secretKey',\n    'secretToken',\n    'cc-number',\n    'card number',\n    'cardnumber',\n    'cardnum',\n    'ccnum',\n    'ccnumber',\n    'cc num',\n    'creditcardnumber',\n    'credit card number',\n    'newcreditcardnumber',\n    'new credit card',\n    'creditcardno',\n    'credit card no',\n    'card#',\n    'card #',\n    'cc-csc',\n    'cvc',\n    'cvc2',\n    'cvv2',\n    'ccv2',\n    'security code',\n    'card verification',\n    'name on credit card',\n    'name on card',\n    'nameoncard',\n    'cardholder',\n    'card holder',\n    'name des karteninhabers',\n    'ccname',\n    'card type',\n    'cardtype',\n    'cc type',\n    'cctype',\n    'payment type',\n    'expiration date',\n    'expirationdate',\n    'expdate',\n    'cc-exp',\n    'ccmonth',\n    'ccyear',\n  ],\n};\n", "'use strict';\n\n// This detection.js module is used to encapsulate any ugly browser/feature\n// detection we may need to do.\n\n// Figure out which version of IE we're using, if any.\n// This is gleaned from http://stackoverflow.com/questions/5574842/best-way-to-check-for-ie-less-than-9-in-javascript-without-library\n// Will return an integer on IE (i.e. 8)\n// Will return undefined otherwise\nfunction getIEVersion() {\n  var undef;\n  if (typeof document === 'undefined') {\n    return undef;\n  }\n\n  var v = 3,\n    div = document.createElement('div'),\n    all = div.getElementsByTagName('i');\n\n  while (\n    ((div.innerHTML = '<!--[if gt IE ' + ++v + ']><i></i><![endif]-->'), all[0])\n  );\n\n  return v > 4 ? v : undef;\n}\n\nvar Detection = {\n  ieVersion: getIEVersion,\n};\n\nmodule.exports = Detection;\n", "'use strict';\n\nfunction getElementType(e) {\n  return (e.getAttribute('type') || '').toLowerCase();\n}\n\nfunction isDescribedElement(element, type, subtypes) {\n  if (element.tagName.toLowerCase() !== type.toLowerCase()) {\n    return false;\n  }\n  if (!subtypes) {\n    return true;\n  }\n  element = getElementType(element);\n  for (var i = 0; i < subtypes.length; i++) {\n    if (subtypes[i] === element) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getElementFromEvent(evt, doc) {\n  if (evt.target) {\n    return evt.target;\n  }\n  if (doc && doc.elementFromPoint) {\n    return doc.elementFromPoint(evt.clientX, evt.clientY);\n  }\n  return undefined;\n}\n\nfunction treeToArray(elem) {\n  var MAX_HEIGHT = 5;\n  var out = [];\n  var nextDescription;\n  for (var height = 0; elem && height < MAX_HEIGHT; height++) {\n    nextDescription = describeElement(elem);\n    if (nextDescription.tagName === 'html') {\n      break;\n    }\n    out.unshift(nextDescription);\n    elem = elem.parentNode;\n  }\n  return out;\n}\n\nfunction elementArrayToString(a) {\n  var MAX_LENGTH = 80;\n  var separator = ' > ',\n    separatorLength = separator.length;\n  var out = [],\n    len = 0,\n    nextStr,\n    totalLength;\n\n  for (var i = a.length - 1; i >= 0; i--) {\n    nextStr = descriptionToString(a[i]);\n    totalLength = len + out.length * separatorLength + nextStr.length;\n    if (i < a.length - 1 && totalLength >= MAX_LENGTH + 3) {\n      out.unshift('...');\n      break;\n    }\n    out.unshift(nextStr);\n    len += nextStr.length;\n  }\n  return out.join(separator);\n}\n\nfunction descriptionToString(desc) {\n  if (!desc || !desc.tagName) {\n    return '';\n  }\n  var out = [desc.tagName];\n  if (desc.id) {\n    out.push('#' + desc.id);\n  }\n  if (desc.classes) {\n    out.push('.' + desc.classes.join('.'));\n  }\n  for (var i = 0; i < desc.attributes.length; i++) {\n    out.push(\n      '[' + desc.attributes[i].key + '=\"' + desc.attributes[i].value + '\"]',\n    );\n  }\n\n  return out.join('');\n}\n\n/**\n * Input: a dom element\n * Output: null if tagName is falsey or input is falsey, else\n *  {\n *    tagName: String,\n *    id: String | undefined,\n *    classes: [String] | undefined,\n *    attributes: [\n *      {\n *        key: OneOf(type, name, title, alt),\n *        value: String\n *      }\n *    ]\n *  }\n */\nfunction describeElement(elem) {\n  if (!elem || !elem.tagName) {\n    return null;\n  }\n  var out = {},\n    className,\n    key,\n    attr,\n    i;\n  out.tagName = elem.tagName.toLowerCase();\n  if (elem.id) {\n    out.id = elem.id;\n  }\n  className = elem.className;\n  if (className && typeof className === 'string') {\n    out.classes = className.split(/\\s+/);\n  }\n  var attributes = ['type', 'name', 'title', 'alt'];\n  out.attributes = [];\n  for (i = 0; i < attributes.length; i++) {\n    key = attributes[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.attributes.push({ key: key, value: attr });\n    }\n  }\n  return out;\n}\n\nmodule.exports = {\n  describeElement: describeElement,\n  descriptionToString: descriptionToString,\n  elementArrayToString: elementArrayToString,\n  treeToArray: treeToArray,\n  getElementFromEvent: getElementFromEvent,\n  isDescribedElement: isDescribedElement,\n  getElementType: getElementType,\n};\n", "'use strict';\n\nfunction captureUncaughtExceptions(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  var oldOnError;\n\n  if (typeof handler._rollbarOldOnError === 'function') {\n    oldOnError = handler._rollbarOldOnError;\n  } else if (window.onerror) {\n    oldOnError = window.onerror;\n    while (oldOnError._rollbarOldOnError) {\n      oldOnError = oldOnError._rollbarOldOnError;\n    }\n    handler._rollbarOldOnError = oldOnError;\n  }\n\n  handler.handleAnonymousErrors();\n\n  var fn = function () {\n    var args = Array.prototype.slice.call(arguments, 0);\n    _rollbarWindowOnError(window, handler, oldOnError, args);\n  };\n  if (shim) {\n    fn._rollbarOldOnError = oldOnError;\n  }\n  window.onerror = fn;\n}\n\nfunction _rollbarWindowOnError(window, r, old, args) {\n  if (window._rollbarWrappedError) {\n    if (!args[4]) {\n      args[4] = window._rollbarWrappedError;\n    }\n    if (!args[5]) {\n      args[5] = window._rollbarWrappedError._rollbarContext;\n    }\n    window._rollbarWrappedError = null;\n  }\n\n  var ret = r.handleUncaughtException.apply(r, args);\n\n  if (old) {\n    old.apply(window, args);\n  }\n\n  // Let other chained onerror handlers above run before setting this.\n  // If an error is thrown and caught within a chained onerror handler,\n  // Error.prepareStackTrace() will see that one before the one we want.\n  if (ret === 'anonymous') {\n    r.anonymousErrorsPending += 1; // See Rollbar.prototype.handleAnonymousErrors()\n  }\n}\n\nfunction captureUnhandledRejections(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n\n  if (\n    typeof window._rollbarURH === 'function' &&\n    window._rollbarURH.belongsToShim\n  ) {\n    window.removeEventListener('unhandledrejection', window._rollbarURH);\n  }\n\n  var rejectionHandler = function (evt) {\n    var reason, promise, detail;\n    try {\n      reason = evt.reason;\n    } catch (e) {\n      reason = undefined;\n    }\n    try {\n      promise = evt.promise;\n    } catch (e) {\n      promise = '[unhandledrejection] error getting `promise` from event';\n    }\n    try {\n      detail = evt.detail;\n      if (!reason && detail) {\n        reason = detail.reason;\n        promise = detail.promise;\n      }\n    } catch (e) {\n      // Ignore\n    }\n    if (!reason) {\n      reason = '[unhandledrejection] error getting `reason` from event';\n    }\n\n    if (handler && handler.handleUnhandledRejection) {\n      handler.handleUnhandledRejection(reason, promise);\n    }\n  };\n  rejectionHandler.belongsToShim = shim;\n  window._rollbarURH = rejectionHandler;\n  window.addEventListener('unhandledrejection', rejectionHandler);\n}\n\nmodule.exports = {\n  captureUncaughtExceptions: captureUncaughtExceptions,\n  captureUnhandledRejections: captureUnhandledRejections,\n};\n", "'use strict';\n\n/* eslint-disable no-console */\nrequire('console-polyfill');\nvar detection = require('./detection');\nvar _ = require('../utility');\n\nfunction error() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.error(_.formatArgsAsString(args));\n  } else {\n    console.error.apply(console, args);\n  }\n}\n\nfunction info() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.info(_.formatArgsAsString(args));\n  } else {\n    console.info.apply(console, args);\n  }\n}\n\nfunction log() {\n  var args = Array.prototype.slice.call(arguments, 0);\n  args.unshift('Rollbar:');\n  if (detection.ieVersion() <= 8) {\n    console.log(_.formatArgsAsString(args));\n  } else {\n    console.log.apply(console, args);\n  }\n}\n\n/* eslint-enable no-console */\n\nmodule.exports = {\n  error: error,\n  info: info,\n  log: log,\n};\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction checkIgnore(item, settings) {\n  if (_.get(settings, 'plugins.jquery.ignoreAjaxErrors')) {\n    return !_.get(item, 'body.message.extra.isAjax');\n  }\n  return true;\n}\n\nmodule.exports = {\n  checkIgnore: checkIgnore,\n};\n", "'use strict';\n\nvar Rollbar = require('./core');\nvar telemeter = require('../telemetry');\nvar instrumenter = require('./telemetry');\nvar polyfillJSON = require('../utility/polyfillJSON');\nvar wrapGlobals = require('./wrapGlobals');\nvar scrub = require('../scrub');\nvar truncation = require('../truncation');\n\nRollbar.setComponents({\n  telemeter: telemeter,\n  instrumenter: instrumenter,\n  polyfillJSON: polyfillJSON,\n  wrapGlobals: wrapGlobals,\n  scrub: scrub,\n  truncation: truncation,\n});\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('../utility');\nvar headers = require('../utility/headers');\nvar replace = require('../utility/replace');\nvar scrub = require('../scrub');\nvar urlparser = require('./url');\nvar domUtil = require('./domUtility');\n\nvar defaults = {\n  network: true,\n  networkResponseHeaders: false,\n  networkResponseBody: false,\n  networkRequestHeaders: false,\n  networkRequestBody: false,\n  networkErrorOnHttp5xx: false,\n  networkErrorOnHttp4xx: false,\n  networkErrorOnHttp0: false,\n  log: true,\n  dom: true,\n  navigation: true,\n  connectivity: true,\n  contentSecurityPolicy: true,\n  errorOnContentSecurityPolicy: false,\n};\n\nfunction restore(replacements, type) {\n  var b;\n  while (replacements[type].length) {\n    b = replacements[type].shift();\n    b[0][b[1]] = b[2];\n  }\n}\n\nfunction nameFromDescription(description) {\n  if (!description || !description.attributes) {\n    return null;\n  }\n  var attrs = description.attributes;\n  for (var a = 0; a < attrs.length; ++a) {\n    if (attrs[a].key === 'name') {\n      return attrs[a].value;\n    }\n  }\n  return null;\n}\n\nfunction defaultValueScrubber(scrubFields) {\n  var patterns = [];\n  for (var i = 0; i < scrubFields.length; ++i) {\n    patterns.push(new RegExp(scrubFields[i], 'i'));\n  }\n  return function (description) {\n    var name = nameFromDescription(description);\n    if (!name) {\n      return false;\n    }\n    for (var i = 0; i < patterns.length; ++i) {\n      if (patterns[i].test(name)) {\n        return true;\n      }\n    }\n    return false;\n  };\n}\n\nfunction Instrumenter(options, telemeter, rollbar, _window, _document) {\n  this.options = options;\n  var autoInstrument = options.autoInstrument;\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  this.telemetryScrubber = options.telemetryScrubber;\n  this.defaultValueScrubber = defaultValueScrubber(options.scrubFields);\n  this.telemeter = telemeter;\n  this.rollbar = rollbar;\n  this.diagnostic = rollbar.client.notifier.diagnostic;\n  this._window = _window || {};\n  this._document = _document || {};\n  this.replacements = {\n    network: [],\n    log: [],\n    navigation: [],\n    connectivity: [],\n  };\n  this.eventRemovers = {\n    dom: [],\n    connectivity: [],\n    contentsecuritypolicy: [],\n  };\n\n  this._location = this._window.location;\n  this._lastHref = this._location && this._location.href;\n}\n\nInstrumenter.prototype.configure = function (options) {\n  this.options = _.merge(this.options, options);\n  var autoInstrument = options.autoInstrument;\n  var oldSettings = _.merge(this.autoInstrument);\n  if (options.enabled === false || autoInstrument === false) {\n    this.autoInstrument = {};\n  } else {\n    if (!_.isType(autoInstrument, 'object')) {\n      autoInstrument = defaults;\n    }\n    this.autoInstrument = _.merge(defaults, autoInstrument);\n  }\n  this.instrument(oldSettings);\n  if (options.scrubTelemetryInputs !== undefined) {\n    this.scrubTelemetryInputs = !!options.scrubTelemetryInputs;\n  }\n  if (options.telemetryScrubber !== undefined) {\n    this.telemetryScrubber = options.telemetryScrubber;\n  }\n};\n\n// eslint-disable-next-line complexity\nInstrumenter.prototype.instrument = function (oldSettings) {\n  if (this.autoInstrument.network && !(oldSettings && oldSettings.network)) {\n    this.instrumentNetwork();\n  } else if (\n    !this.autoInstrument.network &&\n    oldSettings &&\n    oldSettings.network\n  ) {\n    this.deinstrumentNetwork();\n  }\n\n  if (this.autoInstrument.log && !(oldSettings && oldSettings.log)) {\n    this.instrumentConsole();\n  } else if (!this.autoInstrument.log && oldSettings && oldSettings.log) {\n    this.deinstrumentConsole();\n  }\n\n  if (this.autoInstrument.dom && !(oldSettings && oldSettings.dom)) {\n    this.instrumentDom();\n  } else if (!this.autoInstrument.dom && oldSettings && oldSettings.dom) {\n    this.deinstrumentDom();\n  }\n\n  if (\n    this.autoInstrument.navigation &&\n    !(oldSettings && oldSettings.navigation)\n  ) {\n    this.instrumentNavigation();\n  } else if (\n    !this.autoInstrument.navigation &&\n    oldSettings &&\n    oldSettings.navigation\n  ) {\n    this.deinstrumentNavigation();\n  }\n\n  if (\n    this.autoInstrument.connectivity &&\n    !(oldSettings && oldSettings.connectivity)\n  ) {\n    this.instrumentConnectivity();\n  } else if (\n    !this.autoInstrument.connectivity &&\n    oldSettings &&\n    oldSettings.connectivity\n  ) {\n    this.deinstrumentConnectivity();\n  }\n\n  if (\n    this.autoInstrument.contentSecurityPolicy &&\n    !(oldSettings && oldSettings.contentSecurityPolicy)\n  ) {\n    this.instrumentContentSecurityPolicy();\n  } else if (\n    !this.autoInstrument.contentSecurityPolicy &&\n    oldSettings &&\n    oldSettings.contentSecurityPolicy\n  ) {\n    this.deinstrumentContentSecurityPolicy();\n  }\n};\n\nInstrumenter.prototype.deinstrumentNetwork = function () {\n  restore(this.replacements, 'network');\n};\n\nInstrumenter.prototype.instrumentNetwork = function () {\n  var self = this;\n\n  function wrapProp(prop, xhr) {\n    if (prop in xhr && _.isFunction(xhr[prop])) {\n      replace(xhr, prop, function (orig) {\n        return self.rollbar.wrap(orig);\n      });\n    }\n  }\n\n  if ('XMLHttpRequest' in this._window) {\n    var xhrp = this._window.XMLHttpRequest.prototype;\n    replace(\n      xhrp,\n      'open',\n      function (orig) {\n        return function (method, url) {\n          var isUrlObject = _isUrlObject(url);\n          if (_.isType(url, 'string') || isUrlObject) {\n            url = isUrlObject ? url.toString() : url;\n            if (this.__rollbar_xhr) {\n              this.__rollbar_xhr.method = method;\n              this.__rollbar_xhr.url = url;\n              this.__rollbar_xhr.status_code = null;\n              this.__rollbar_xhr.start_time_ms = _.now();\n              this.__rollbar_xhr.end_time_ms = null;\n            } else {\n              this.__rollbar_xhr = {\n                method: method,\n                url: url,\n                status_code: null,\n                start_time_ms: _.now(),\n                end_time_ms: null,\n              };\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'setRequestHeader',\n      function (orig) {\n        return function (header, value) {\n          // If xhr.open is async, __rollbar_xhr may not be initialized yet.\n          if (!this.__rollbar_xhr) {\n            this.__rollbar_xhr = {};\n          }\n          if (_.isType(header, 'string') && _.isType(value, 'string')) {\n            if (self.autoInstrument.networkRequestHeaders) {\n              if (!this.__rollbar_xhr.request_headers) {\n                this.__rollbar_xhr.request_headers = {};\n              }\n              this.__rollbar_xhr.request_headers[header] = value;\n            }\n            // We want the content type even if request header telemetry is off.\n            if (header.toLowerCase() === 'content-type') {\n              this.__rollbar_xhr.request_content_type = value;\n            }\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n\n    replace(\n      xhrp,\n      'send',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (data) {\n          /* eslint-enable no-unused-vars */\n          var xhr = this;\n\n          function onreadystatechangeHandler() {\n            if (xhr.__rollbar_xhr) {\n              if (xhr.__rollbar_xhr.status_code === null) {\n                xhr.__rollbar_xhr.status_code = 0;\n                if (self.autoInstrument.networkRequestBody) {\n                  xhr.__rollbar_xhr.request = data;\n                }\n                xhr.__rollbar_event = self.captureNetwork(\n                  xhr.__rollbar_xhr,\n                  'xhr',\n                  undefined,\n                );\n              }\n              if (xhr.readyState < 2) {\n                xhr.__rollbar_xhr.start_time_ms = _.now();\n              }\n              if (xhr.readyState > 3) {\n                xhr.__rollbar_xhr.end_time_ms = _.now();\n\n                var headers = null;\n                xhr.__rollbar_xhr.response_content_type =\n                  xhr.getResponseHeader('Content-Type');\n                if (self.autoInstrument.networkResponseHeaders) {\n                  var headersConfig =\n                    self.autoInstrument.networkResponseHeaders;\n                  headers = {};\n                  try {\n                    var header, i;\n                    if (headersConfig === true) {\n                      var allHeaders = xhr.getAllResponseHeaders();\n                      if (allHeaders) {\n                        var arr = allHeaders.trim().split(/[\\r\\n]+/);\n                        var parts, value;\n                        for (i = 0; i < arr.length; i++) {\n                          parts = arr[i].split(': ');\n                          header = parts.shift();\n                          value = parts.join(': ');\n                          headers[header] = value;\n                        }\n                      }\n                    } else {\n                      for (i = 0; i < headersConfig.length; i++) {\n                        header = headersConfig[i];\n                        headers[header] = xhr.getResponseHeader(header);\n                      }\n                    }\n                  } catch (e) {\n                    /* we ignore the errors here that could come from different\n                     * browser issues with the xhr methods */\n                  }\n                }\n                var body = null;\n                if (self.autoInstrument.networkResponseBody) {\n                  try {\n                    body = xhr.responseText;\n                  } catch (e) {\n                    /* ignore errors from reading responseText */\n                  }\n                }\n                var response = null;\n                if (body || headers) {\n                  response = {};\n                  if (body) {\n                    if (\n                      self.isJsonContentType(\n                        xhr.__rollbar_xhr.response_content_type,\n                      )\n                    ) {\n                      response.body = self.scrubJson(body);\n                    } else {\n                      response.body = body;\n                    }\n                  }\n                  if (headers) {\n                    response.headers = headers;\n                  }\n                }\n                if (response) {\n                  xhr.__rollbar_xhr.response = response;\n                }\n                try {\n                  var code = xhr.status;\n                  code = code === 1223 ? 204 : code;\n                  xhr.__rollbar_xhr.status_code = code;\n                  xhr.__rollbar_event.level =\n                    self.telemeter.levelFromStatus(code);\n                  self.errorOnHttpStatus(xhr.__rollbar_xhr);\n                } catch (e) {\n                  /* ignore possible exception from xhr.status */\n                }\n              }\n            }\n          }\n\n          wrapProp('onload', xhr);\n          wrapProp('onerror', xhr);\n          wrapProp('onprogress', xhr);\n\n          if (\n            'onreadystatechange' in xhr &&\n            _.isFunction(xhr.onreadystatechange)\n          ) {\n            replace(xhr, 'onreadystatechange', function (orig) {\n              return self.rollbar.wrap(\n                orig,\n                undefined,\n                onreadystatechangeHandler,\n              );\n            });\n          } else {\n            xhr.onreadystatechange = onreadystatechangeHandler;\n          }\n          if (xhr.__rollbar_xhr && self.trackHttpErrors()) {\n            xhr.__rollbar_xhr.stack = new Error().stack;\n          }\n          return orig.apply(this, arguments);\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n\n  if ('fetch' in this._window) {\n    replace(\n      this._window,\n      'fetch',\n      function (orig) {\n        /* eslint-disable no-unused-vars */\n        return function (fn, t) {\n          /* eslint-enable no-unused-vars */\n          var args = new Array(arguments.length);\n          for (var i = 0, len = args.length; i < len; i++) {\n            args[i] = arguments[i];\n          }\n          var input = args[0];\n          var method = 'GET';\n          var url;\n          var isUrlObject = _isUrlObject(input);\n          if (_.isType(input, 'string') || isUrlObject) {\n            url = isUrlObject ? input.toString() : input;\n          } else if (input) {\n            url = input.url;\n            if (input.method) {\n              method = input.method;\n            }\n          }\n          if (args[1] && args[1].method) {\n            method = args[1].method;\n          }\n          var metadata = {\n            method: method,\n            url: url,\n            status_code: null,\n            start_time_ms: _.now(),\n            end_time_ms: null,\n          };\n          if (args[1] && args[1].headers) {\n            // Argument may be a Headers object, or plain object. Ensure here that\n            // we are working with a Headers object with case-insensitive keys.\n            var reqHeaders = headers(args[1].headers);\n\n            metadata.request_content_type = reqHeaders.get('Content-Type');\n\n            if (self.autoInstrument.networkRequestHeaders) {\n              metadata.request_headers = self.fetchHeaders(\n                reqHeaders,\n                self.autoInstrument.networkRequestHeaders,\n              );\n            }\n          }\n\n          if (self.autoInstrument.networkRequestBody) {\n            if (args[1] && args[1].body) {\n              metadata.request = args[1].body;\n            } else if (\n              args[0] &&\n              !_.isType(args[0], 'string') &&\n              args[0].body\n            ) {\n              metadata.request = args[0].body;\n            }\n          }\n          self.captureNetwork(metadata, 'fetch', undefined);\n          if (self.trackHttpErrors()) {\n            metadata.stack = new Error().stack;\n          }\n\n          // Start our handler before returning the promise. This allows resp.clone()\n          // to execute before other handlers touch the response.\n          return orig.apply(this, args).then(function (resp) {\n            metadata.end_time_ms = _.now();\n            metadata.status_code = resp.status;\n            metadata.response_content_type = resp.headers.get('Content-Type');\n            var headers = null;\n            if (self.autoInstrument.networkResponseHeaders) {\n              headers = self.fetchHeaders(\n                resp.headers,\n                self.autoInstrument.networkResponseHeaders,\n              );\n            }\n            var body = null;\n            if (self.autoInstrument.networkResponseBody) {\n              if (typeof resp.text === 'function') {\n                // Response.text() is not implemented on some platforms\n                // The response must be cloned to prevent reading (and locking) the original stream.\n                // This must be done before other handlers touch the response.\n                body = resp.clone().text(); //returns a Promise\n              }\n            }\n            if (headers || body) {\n              metadata.response = {};\n              if (body) {\n                // Test to ensure body is a Promise, which it should always be.\n                if (typeof body.then === 'function') {\n                  body.then(function (text) {\n                    if (\n                      text &&\n                      self.isJsonContentType(metadata.response_content_type)\n                    ) {\n                      metadata.response.body = self.scrubJson(text);\n                    } else {\n                      metadata.response.body = text;\n                    }\n                  });\n                } else {\n                  metadata.response.body = body;\n                }\n              }\n              if (headers) {\n                metadata.response.headers = headers;\n              }\n            }\n            self.errorOnHttpStatus(metadata);\n            return resp;\n          });\n        };\n      },\n      this.replacements,\n      'network',\n    );\n  }\n};\n\nInstrumenter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n) {\n  if (\n    metadata.request &&\n    this.isJsonContentType(metadata.request_content_type)\n  ) {\n    metadata.request = this.scrubJson(metadata.request);\n  }\n  return this.telemeter.captureNetwork(metadata, subtype, rollbarUUID);\n};\n\nInstrumenter.prototype.isJsonContentType = function (contentType) {\n  return contentType &&\n    _.isType(contentType, 'string') &&\n    contentType.toLowerCase().includes('json')\n    ? true\n    : false;\n};\n\nInstrumenter.prototype.scrubJson = function (json) {\n  return JSON.stringify(scrub(JSON.parse(json), this.options.scrubFields));\n};\n\nInstrumenter.prototype.fetchHeaders = function (inHeaders, headersConfig) {\n  var outHeaders = {};\n  try {\n    var i;\n    if (headersConfig === true) {\n      if (typeof inHeaders.entries === 'function') {\n        // Headers.entries() is not implemented in IE\n        var allHeaders = inHeaders.entries();\n        var currentHeader = allHeaders.next();\n        while (!currentHeader.done) {\n          outHeaders[currentHeader.value[0]] = currentHeader.value[1];\n          currentHeader = allHeaders.next();\n        }\n      }\n    } else {\n      for (i = 0; i < headersConfig.length; i++) {\n        var header = headersConfig[i];\n        outHeaders[header] = inHeaders.get(header);\n      }\n    }\n  } catch (e) {\n    /* ignore probable IE errors */\n  }\n  return outHeaders;\n};\n\nInstrumenter.prototype.trackHttpErrors = function () {\n  return (\n    this.autoInstrument.networkErrorOnHttp5xx ||\n    this.autoInstrument.networkErrorOnHttp4xx ||\n    this.autoInstrument.networkErrorOnHttp0\n  );\n};\n\nInstrumenter.prototype.errorOnHttpStatus = function (metadata) {\n  var status = metadata.status_code;\n\n  if (\n    (status >= 500 && this.autoInstrument.networkErrorOnHttp5xx) ||\n    (status >= 400 && this.autoInstrument.networkErrorOnHttp4xx) ||\n    (status === 0 && this.autoInstrument.networkErrorOnHttp0)\n  ) {\n    var error = new Error('HTTP request failed with Status ' + status);\n    error.stack = metadata.stack;\n    this.rollbar.error(error, { skipFrames: 1 });\n  }\n};\n\nInstrumenter.prototype.deinstrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n  var b;\n  while (this.replacements['log'].length) {\n    b = this.replacements['log'].shift();\n    this._window.console[b[0]] = b[1];\n  }\n};\n\nInstrumenter.prototype.instrumentConsole = function () {\n  if (!('console' in this._window && this._window.console.log)) {\n    return;\n  }\n\n  var self = this;\n  var c = this._window.console;\n\n  function wrapConsole(method) {\n    'use strict'; // See https://github.com/rollbar/rollbar.js/pull/778\n\n    var orig = c[method];\n    var origConsole = c;\n    var level = method === 'warn' ? 'warning' : method;\n    c[method] = function () {\n      var args = Array.prototype.slice.call(arguments);\n      var message = _.formatArgsAsString(args);\n      self.telemeter.captureLog(message, level);\n      if (orig) {\n        Function.prototype.apply.call(orig, origConsole, args);\n      }\n    };\n    self.replacements['log'].push([method, orig]);\n  }\n  var methods = ['debug', 'info', 'warn', 'error', 'log'];\n  try {\n    for (var i = 0, len = methods.length; i < len; i++) {\n      wrapConsole(methods[i]);\n    }\n  } catch (e) {\n    this.diagnostic.instrumentConsole = { error: e.message };\n  }\n};\n\nInstrumenter.prototype.deinstrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  this.removeListeners('dom');\n};\n\nInstrumenter.prototype.instrumentDom = function () {\n  if (!('addEventListener' in this._window || 'attachEvent' in this._window)) {\n    return;\n  }\n  var clickHandler = this.handleClick.bind(this);\n  var blurHandler = this.handleBlur.bind(this);\n  this.addListener('dom', this._window, 'click', 'onclick', clickHandler, true);\n  this.addListener(\n    'dom',\n    this._window,\n    'blur',\n    'onfocusout',\n    blurHandler,\n    true,\n  );\n};\n\nInstrumenter.prototype.handleClick = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    var hasTag = e && e.tagName;\n    var anchorOrButton =\n      domUtil.isDescribedElement(e, 'a') ||\n      domUtil.isDescribedElement(e, 'button');\n    if (\n      hasTag &&\n      (anchorOrButton ||\n        domUtil.isDescribedElement(e, 'input', ['button', 'submit']))\n    ) {\n      this.captureDomEvent('click', e);\n    } else if (domUtil.isDescribedElement(e, 'input', ['checkbox', 'radio'])) {\n      this.captureDomEvent('input', e, e.value, e.checked);\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleBlur = function (evt) {\n  try {\n    var e = domUtil.getElementFromEvent(evt, this._document);\n    if (e && e.tagName) {\n      if (domUtil.isDescribedElement(e, 'textarea')) {\n        this.captureDomEvent('input', e, e.value);\n      } else if (\n        domUtil.isDescribedElement(e, 'select') &&\n        e.options &&\n        e.options.length\n      ) {\n        this.handleSelectInputChanged(e);\n      } else if (\n        domUtil.isDescribedElement(e, 'input') &&\n        !domUtil.isDescribedElement(e, 'input', [\n          'button',\n          'submit',\n          'hidden',\n          'checkbox',\n          'radio',\n        ])\n      ) {\n        this.captureDomEvent('input', e, e.value);\n      }\n    }\n  } catch (exc) {\n    // TODO: Not sure what to do here\n  }\n};\n\nInstrumenter.prototype.handleSelectInputChanged = function (elem) {\n  if (elem.multiple) {\n    for (var i = 0; i < elem.options.length; i++) {\n      if (elem.options[i].selected) {\n        this.captureDomEvent('input', elem, elem.options[i].value);\n      }\n    }\n  } else if (elem.selectedIndex >= 0 && elem.options[elem.selectedIndex]) {\n    this.captureDomEvent('input', elem, elem.options[elem.selectedIndex].value);\n  }\n};\n\nInstrumenter.prototype.captureDomEvent = function (\n  subtype,\n  element,\n  value,\n  isChecked,\n) {\n  if (value !== undefined) {\n    if (\n      this.scrubTelemetryInputs ||\n      domUtil.getElementType(element) === 'password'\n    ) {\n      value = '[scrubbed]';\n    } else {\n      var description = domUtil.describeElement(element);\n      if (this.telemetryScrubber) {\n        if (this.telemetryScrubber(description)) {\n          value = '[scrubbed]';\n        }\n      } else if (this.defaultValueScrubber(description)) {\n        value = '[scrubbed]';\n      }\n    }\n  }\n  var elementString = domUtil.elementArrayToString(\n    domUtil.treeToArray(element),\n  );\n  this.telemeter.captureDom(subtype, elementString, value, isChecked);\n};\n\nInstrumenter.prototype.deinstrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  restore(this.replacements, 'navigation');\n};\n\nInstrumenter.prototype.instrumentNavigation = function () {\n  var chrome = this._window.chrome;\n  var chromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  // See https://github.com/angular/angular.js/pull/13945/files\n  var hasPushState =\n    !chromePackagedApp &&\n    this._window.history &&\n    this._window.history.pushState;\n  if (!hasPushState) {\n    return;\n  }\n  var self = this;\n  replace(\n    this._window,\n    'onpopstate',\n    function (orig) {\n      return function () {\n        var current = self._location.href;\n        self.handleUrlChange(self._lastHref, current);\n        if (orig) {\n          orig.apply(this, arguments);\n        }\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n\n  replace(\n    this._window.history,\n    'pushState',\n    function (orig) {\n      return function () {\n        var url = arguments.length > 2 ? arguments[2] : undefined;\n        if (url) {\n          self.handleUrlChange(self._lastHref, url + '');\n        }\n        return orig.apply(this, arguments);\n      };\n    },\n    this.replacements,\n    'navigation',\n  );\n};\n\nInstrumenter.prototype.handleUrlChange = function (from, to) {\n  var parsedHref = urlparser.parse(this._location.href);\n  var parsedTo = urlparser.parse(to);\n  var parsedFrom = urlparser.parse(from);\n  this._lastHref = to;\n  if (\n    parsedHref.protocol === parsedTo.protocol &&\n    parsedHref.host === parsedTo.host\n  ) {\n    to = parsedTo.path + (parsedTo.hash || '');\n  }\n  if (\n    parsedHref.protocol === parsedFrom.protocol &&\n    parsedHref.host === parsedFrom.host\n  ) {\n    from = parsedFrom.path + (parsedFrom.hash || '');\n  }\n  this.telemeter.captureNavigation(from, to);\n};\n\nInstrumenter.prototype.deinstrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.removeListeners('connectivity');\n  } else {\n    restore(this.replacements, 'connectivity');\n  }\n};\n\nInstrumenter.prototype.instrumentConnectivity = function () {\n  if (!('addEventListener' in this._window || 'body' in this._document)) {\n    return;\n  }\n  if (this._window.addEventListener) {\n    this.addListener(\n      'connectivity',\n      this._window,\n      'online',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('online');\n      }.bind(this),\n      true,\n    );\n    this.addListener(\n      'connectivity',\n      this._window,\n      'offline',\n      undefined,\n      function () {\n        this.telemeter.captureConnectivityChange('offline');\n      }.bind(this),\n      true,\n    );\n  } else {\n    var self = this;\n    replace(\n      this._document.body,\n      'ononline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('online');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n    replace(\n      this._document.body,\n      'onoffline',\n      function (orig) {\n        return function () {\n          self.telemeter.captureConnectivityChange('offline');\n          if (orig) {\n            orig.apply(this, arguments);\n          }\n        };\n      },\n      this.replacements,\n      'connectivity',\n    );\n  }\n};\n\nInstrumenter.prototype.handleCspEvent = function (cspEvent) {\n  var message =\n    'Security Policy Violation: ' +\n    'blockedURI: ' +\n    cspEvent.blockedURI +\n    ', ' +\n    'violatedDirective: ' +\n    cspEvent.violatedDirective +\n    ', ' +\n    'effectiveDirective: ' +\n    cspEvent.effectiveDirective +\n    ', ';\n\n  if (cspEvent.sourceFile) {\n    message +=\n      'location: ' +\n      cspEvent.sourceFile +\n      ', ' +\n      'line: ' +\n      cspEvent.lineNumber +\n      ', ' +\n      'col: ' +\n      cspEvent.columnNumber +\n      ', ';\n  }\n\n  message += 'originalPolicy: ' + cspEvent.originalPolicy;\n\n  this.telemeter.captureLog(message, 'error');\n  this.handleCspError(message);\n};\n\nInstrumenter.prototype.handleCspError = function (message) {\n  if (this.autoInstrument.errorOnContentSecurityPolicy) {\n    this.rollbar.error(message);\n  }\n};\n\nInstrumenter.prototype.deinstrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  this.removeListeners('contentsecuritypolicy');\n};\n\nInstrumenter.prototype.instrumentContentSecurityPolicy = function () {\n  if (!('addEventListener' in this._document)) {\n    return;\n  }\n\n  var cspHandler = this.handleCspEvent.bind(this);\n  this.addListener(\n    'contentsecuritypolicy',\n    this._document,\n    'securitypolicyviolation',\n    null,\n    cspHandler,\n    false,\n  );\n};\n\nInstrumenter.prototype.addListener = function (\n  section,\n  obj,\n  type,\n  altType,\n  handler,\n  capture,\n) {\n  if (obj.addEventListener) {\n    obj.addEventListener(type, handler, capture);\n    this.eventRemovers[section].push(function () {\n      obj.removeEventListener(type, handler, capture);\n    });\n  } else if (altType) {\n    obj.attachEvent(altType, handler);\n    this.eventRemovers[section].push(function () {\n      obj.detachEvent(altType, handler);\n    });\n  }\n};\n\nInstrumenter.prototype.removeListeners = function (section) {\n  var r;\n  while (this.eventRemovers[section].length) {\n    r = this.eventRemovers[section].shift();\n    r();\n  }\n};\n\nfunction _isUrlObject(input) {\n  return typeof URL !== 'undefined' && input instanceof URL;\n}\n\nmodule.exports = Instrumenter;\n", "'use strict';\n\nvar _ = require('../utility');\nvar errorParser = require('../errorParser');\nvar logger = require('./logger');\n\nfunction handleDomException(item, options, callback) {\n  if (item.err && errorParser.Stack(item.err).name === 'DOMException') {\n    var originalError = new Error();\n    originalError.name = item.err.name;\n    originalError.message = item.err.message;\n    originalError.stack = item.err.stack;\n    originalError.nested = item.err;\n    item.err = originalError;\n  }\n  callback(null, item);\n}\n\nfunction handleItemWithError(item, options, callback) {\n  item.data = item.data || {};\n  if (item.err) {\n    try {\n      item.stackInfo =\n        item.err._savedStackTrace ||\n        errorParser.parse(item.err, item.skipFrames);\n\n      if (options.addErrorContext) {\n        addErrorContext(item);\n      }\n    } catch (e) {\n      logger.error('Error while parsing the error object.', e);\n      try {\n        item.message =\n          item.err.message ||\n          item.err.description ||\n          item.message ||\n          String(item.err);\n      } catch (e2) {\n        item.message = String(item.err) || String(e2);\n      }\n      delete item.err;\n    }\n  }\n  callback(null, item);\n}\n\nfunction addErrorContext(item) {\n  var chain = [];\n  var err = item.err;\n\n  chain.push(err);\n\n  while (err.nested || err.cause) {\n    err = err.nested || err.cause;\n    chain.push(err);\n  }\n\n  _.addErrorContext(item, chain);\n}\n\nfunction ensureItemHasSomethingToSay(item, options, callback) {\n  if (!item.message && !item.stackInfo && !item.custom) {\n    callback(new Error('No message, stack info, or custom data'), null);\n  }\n  callback(null, item);\n}\n\nfunction addBaseInfo(item, options, callback) {\n  var environment =\n    (options.payload && options.payload.environment) || options.environment;\n  item.data = _.merge(item.data, {\n    environment: environment,\n    level: item.level,\n    endpoint: options.endpoint,\n    platform: 'browser',\n    framework: 'browser-js',\n    language: 'javascript',\n    server: {},\n    uuid: item.uuid,\n    notifier: {\n      name: 'rollbar-browser-js',\n      version: options.version,\n    },\n    custom: item.custom,\n  });\n  callback(null, item);\n}\n\nfunction addRequestInfo(window) {\n  return function (item, options, callback) {\n    var requestInfo = {};\n\n    if (window && window.location) {\n      requestInfo.url = window.location.href;\n      requestInfo.query_string = window.location.search;\n    }\n\n    var remoteString = '$remote_ip';\n    if (!options.captureIp) {\n      remoteString = null;\n    } else if (options.captureIp !== true) {\n      remoteString += '_anonymize';\n    }\n    if (remoteString) requestInfo.user_ip = remoteString;\n\n    if (Object.keys(requestInfo).length > 0) {\n      _.set(item, 'data.request', requestInfo);\n    }\n\n    callback(null, item);\n  };\n}\n\nfunction addClientInfo(window) {\n  return function (item, options, callback) {\n    if (!window) {\n      return callback(null, item);\n    }\n    var nav = window.navigator || {};\n    var scr = window.screen || {};\n    _.set(item, 'data.client', {\n      runtime_ms: item.timestamp - window._rollbarStartTime,\n      timestamp: Math.round(item.timestamp / 1000),\n      javascript: {\n        browser: nav.userAgent,\n        language: nav.language,\n        cookie_enabled: nav.cookieEnabled,\n        screen: {\n          width: scr.width,\n          height: scr.height,\n        },\n      },\n    });\n    callback(null, item);\n  };\n}\n\nfunction addPluginInfo(window) {\n  return function (item, options, callback) {\n    if (!window || !window.navigator) {\n      return callback(null, item);\n    }\n    var plugins = [];\n    var navPlugins = window.navigator.plugins || [];\n    var cur;\n    for (var i = 0, l = navPlugins.length; i < l; ++i) {\n      cur = navPlugins[i];\n      plugins.push({ name: cur.name, description: cur.description });\n    }\n    _.set(item, 'data.client.javascript.plugins', plugins);\n    callback(null, item);\n  };\n}\n\nfunction addBody(item, options, callback) {\n  if (item.stackInfo) {\n    if (item.stackInfo.traceChain) {\n      addBodyTraceChain(item, options, callback);\n    } else {\n      addBodyTrace(item, options, callback);\n    }\n  } else {\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction addBodyMessage(item, options, callback) {\n  var message = item.message;\n  var custom = item.custom;\n\n  if (!message) {\n    message = 'Item sent with null or missing arguments.';\n  }\n  var result = {\n    body: message,\n  };\n\n  if (custom) {\n    result.extra = _.merge(custom);\n  }\n\n  _.set(item, 'data.body', { message: result });\n  callback(null, item);\n}\n\nfunction stackFromItem(item) {\n  // Transform a TraceKit stackInfo object into a Rollbar trace\n  var stack = item.stackInfo.stack;\n  if (\n    stack &&\n    stack.length === 0 &&\n    item._unhandledStackInfo &&\n    item._unhandledStackInfo.stack\n  ) {\n    stack = item._unhandledStackInfo.stack;\n  }\n  return stack;\n}\n\nfunction addBodyTraceChain(item, options, callback) {\n  var traceChain = item.stackInfo.traceChain;\n  var traces = [];\n\n  var traceChainLength = traceChain.length;\n  for (var i = 0; i < traceChainLength; i++) {\n    var trace = buildTrace(item, traceChain[i], options);\n    traces.push(trace);\n  }\n\n  _.set(item, 'data.body', { trace_chain: traces });\n  callback(null, item);\n}\n\nfunction addBodyTrace(item, options, callback) {\n  var stack = stackFromItem(item);\n\n  if (stack) {\n    var trace = buildTrace(item, item.stackInfo, options);\n    _.set(item, 'data.body', { trace: trace });\n    callback(null, item);\n  } else {\n    var stackInfo = item.stackInfo;\n    var guess = errorParser.guessErrorClass(stackInfo.message);\n    var className = errorClass(stackInfo, guess[0], options);\n    var message = guess[1];\n\n    item.message = className + ': ' + message;\n    addBodyMessage(item, options, callback);\n  }\n}\n\nfunction buildTrace(item, stackInfo, options) {\n  var description = item && item.data.description;\n  var custom = item && item.custom;\n  var stack = stackFromItem(item);\n\n  var guess = errorParser.guessErrorClass(stackInfo.message);\n  var className = errorClass(stackInfo, guess[0], options);\n  var message = guess[1];\n  var trace = {\n    exception: {\n      class: className,\n      message: message,\n    },\n  };\n\n  if (description) {\n    trace.exception.description = description;\n  }\n\n  if (stack) {\n    if (stack.length === 0) {\n      trace.exception.stack = stackInfo.rawStack;\n      trace.exception.raw = String(stackInfo.rawException);\n    }\n    var stackFrame;\n    var frame;\n    var code;\n    var pre;\n    var post;\n    var contextLength;\n    var i, mid;\n\n    trace.frames = [];\n    for (i = 0; i < stack.length; ++i) {\n      stackFrame = stack[i];\n      frame = {\n        filename: stackFrame.url ? _.sanitizeUrl(stackFrame.url) : '(unknown)',\n        lineno: stackFrame.line || null,\n        method:\n          !stackFrame.func || stackFrame.func === '?'\n            ? '[anonymous]'\n            : stackFrame.func,\n        colno: stackFrame.column,\n      };\n      if (options.sendFrameUrl) {\n        frame.url = stackFrame.url;\n      }\n      if (\n        frame.method &&\n        frame.method.endsWith &&\n        frame.method.endsWith('_rollbar_wrapped')\n      ) {\n        continue;\n      }\n\n      code = pre = post = null;\n      contextLength = stackFrame.context ? stackFrame.context.length : 0;\n      if (contextLength) {\n        mid = Math.floor(contextLength / 2);\n        pre = stackFrame.context.slice(0, mid);\n        code = stackFrame.context[mid];\n        post = stackFrame.context.slice(mid);\n      }\n\n      if (code) {\n        frame.code = code;\n      }\n\n      if (pre || post) {\n        frame.context = {};\n        if (pre && pre.length) {\n          frame.context.pre = pre;\n        }\n        if (post && post.length) {\n          frame.context.post = post;\n        }\n      }\n\n      if (stackFrame.args) {\n        frame.args = stackFrame.args;\n      }\n\n      trace.frames.push(frame);\n    }\n\n    // NOTE(cory): reverse the frames since rollbar.com expects the most recent call last\n    trace.frames.reverse();\n\n    if (custom) {\n      trace.extra = _.merge(custom);\n    }\n  }\n\n  return trace;\n}\n\nfunction errorClass(stackInfo, guess, options) {\n  if (stackInfo.name) {\n    return stackInfo.name;\n  } else if (options.guessErrorClass) {\n    return guess;\n  } else {\n    return '(unknown)';\n  }\n}\n\nfunction addScrubber(scrubFn) {\n  return function (item, options, callback) {\n    if (scrubFn) {\n      var scrubFields = options.scrubFields || [];\n      var scrubPaths = options.scrubPaths || [];\n      item.data = scrubFn(item.data, scrubFields, scrubPaths);\n    }\n    callback(null, item);\n  };\n}\n\nmodule.exports = {\n  handleDomException: handleDomException,\n  handleItemWithError: handleItemWithError,\n  ensureItemHasSomethingToSay: ensureItemHasSomethingToSay,\n  addBaseInfo: addBaseInfo,\n  addRequestInfo: addRequestInfo,\n  addClientInfo: addClientInfo,\n  addPluginInfo: addPluginInfo,\n  addBody: addBody,\n  addScrubber: addScrubber,\n};\n", "'use strict';\n\nvar _ = require('../utility');\nvar makeFetchRequest = require('./transport/fetch');\nvar makeXhrRequest = require('./transport/xhr');\n\n/*\n * accessToken may be embedded in payload but that should not\n *   be assumed\n *\n * options: {\n *   hostname\n *   protocol\n *   path\n *   port\n *   method\n *   transport ('xhr' | 'fetch')\n * }\n *\n *  params is an object containing key/value pairs. These\n *    will be appended to the path as 'key=value&key=value'\n *\n * payload is an unserialized object\n */\nfunction Transport(truncation) {\n  this.truncation = truncation;\n}\n\nTransport.prototype.get = function (\n  accessToken,\n  options,\n  params,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n  _.addParamsAndAccessTokenToPath(accessToken, options, params);\n\n  var method = 'GET';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    null,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.post = function (\n  accessToken,\n  options,\n  payload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!payload) {\n    return callback(new Error('Cannot send empty request'));\n  }\n\n  var stringifyResult;\n  if (this.truncation) {\n    stringifyResult = this.truncation.truncate(payload);\n  } else {\n    stringifyResult = _.stringify(payload);\n  }\n  if (stringifyResult.error) {\n    return callback(stringifyResult.error);\n  }\n\n  var writeData = stringifyResult.value;\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    writeData,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\nTransport.prototype.postJsonPayload = function (\n  accessToken,\n  options,\n  jsonPayload,\n  callback,\n  requestFactory,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  var method = 'POST';\n  var url = _.formatUrl(options);\n  this._makeZoneRequest(\n    accessToken,\n    url,\n    method,\n    jsonPayload,\n    callback,\n    requestFactory,\n    options.timeout,\n    options.transport,\n  );\n};\n\n// Wraps _makeRequest and if Angular 2+ Zone.js is detected, changes scope\n// so Angular change detection isn't triggered on each API call.\n// This is the equivalent of runOutsideAngular().\n//\nTransport.prototype._makeZoneRequest = function () {\n  var gWindow =\n    (typeof window != 'undefined' && window) ||\n    (typeof self != 'undefined' && self);\n  var currentZone = gWindow && gWindow.Zone && gWindow.Zone.current;\n  var args = Array.prototype.slice.call(arguments);\n\n  if (currentZone && currentZone._name === 'angular') {\n    var rootZone = currentZone._parent;\n    var self = this;\n    rootZone.run(function () {\n      self._makeRequest.apply(undefined, args);\n    });\n  } else {\n    this._makeRequest.apply(undefined, args);\n  }\n};\n\nTransport.prototype._makeRequest = function (\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n  transport,\n) {\n  if (typeof RollbarProxy !== 'undefined') {\n    return _proxyRequest(data, callback);\n  }\n\n  if (transport === 'fetch') {\n    makeFetchRequest(accessToken, url, method, data, callback, timeout);\n  } else {\n    makeXhrRequest(\n      accessToken,\n      url,\n      method,\n      data,\n      callback,\n      requestFactory,\n      timeout,\n    );\n  }\n};\n\n/* global RollbarProxy */\nfunction _proxyRequest(json, callback) {\n  var rollbarProxy = new RollbarProxy();\n  rollbarProxy.sendJsonPayload(\n    json,\n    function (_msg) {\n      /* do nothing */\n    }, // eslint-disable-line no-unused-vars\n    function (err) {\n      callback(new Error(err));\n    },\n  );\n}\n\nmodule.exports = Transport;\n", "'use strict';\n\nvar logger = require('../logger');\nvar _ = require('../../utility');\n\nfunction makeFetchRequest(accessToken, url, method, data, callback, timeout) {\n  var controller;\n  var timeoutId;\n\n  if (_.isFiniteNumber(timeout)) {\n    controller = new AbortController();\n    timeoutId = setTimeout(function () {\n      controller.abort();\n    }, timeout);\n  }\n\n  fetch(url, {\n    method: method,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Rollbar-Access-Token': accessToken,\n      signal: controller && controller.signal,\n    },\n    body: data,\n  })\n    .then(function (response) {\n      if (timeoutId) clearTimeout(timeoutId);\n      return response.json();\n    })\n    .then(function (data) {\n      callback(null, data);\n    })\n    .catch(function (error) {\n      logger.error(error.message);\n      callback(error);\n    });\n}\n\nmodule.exports = makeFetchRequest;\n", "'use strict';\n\n/*global XDomainRequest*/\n\nvar _ = require('../../utility');\nvar logger = require('../logger');\n\nfunction makeXhrRequest(\n  accessToken,\n  url,\n  method,\n  data,\n  callback,\n  requestFactory,\n  timeout,\n) {\n  var request;\n  if (requestFactory) {\n    request = requestFactory();\n  } else {\n    request = _createXMLHTTPObject();\n  }\n  if (!request) {\n    // Give up, no way to send requests\n    return callback(new Error('No way to send a request'));\n  }\n  try {\n    try {\n      var onreadystatechange = function () {\n        try {\n          if (onreadystatechange && request.readyState === 4) {\n            onreadystatechange = undefined;\n\n            var parseResponse = _.jsonParse(request.responseText);\n            if (_isSuccess(request)) {\n              callback(parseResponse.error, parseResponse.value);\n              return;\n            } else if (_isNormalFailure(request)) {\n              if (request.status === 403) {\n                // likely caused by using a server access token\n                var message =\n                  parseResponse.value && parseResponse.value.message;\n                logger.error(message);\n              }\n              // return valid http status codes\n              callback(new Error(String(request.status)));\n            } else {\n              // IE will return a status 12000+ on some sort of connection failure,\n              // so we return a blank error\n              // http://msdn.microsoft.com/en-us/library/aa383770%28VS.85%29.aspx\n              var msg =\n                'XHR response had no status code (likely connection failure)';\n              callback(_newRetriableError(msg));\n            }\n          }\n        } catch (ex) {\n          //jquery source mentions firefox may error out while accessing the\n          //request members if there is a network error\n          //https://github.com/jquery/jquery/blob/a938d7b1282fc0e5c52502c225ae8f0cef219f0a/src/ajax/xhr.js#L111\n          var exc;\n          if (ex && ex.stack) {\n            exc = ex;\n          } else {\n            exc = new Error(ex);\n          }\n          callback(exc);\n        }\n      };\n\n      request.open(method, url, true);\n      if (request.setRequestHeader) {\n        request.setRequestHeader('Content-Type', 'application/json');\n        request.setRequestHeader('X-Rollbar-Access-Token', accessToken);\n      }\n\n      if (_.isFiniteNumber(timeout)) {\n        request.timeout = timeout;\n      }\n\n      request.onreadystatechange = onreadystatechange;\n      request.send(data);\n    } catch (e1) {\n      // Sending using the normal xmlhttprequest object didn't work, try XDomainRequest\n      if (typeof XDomainRequest !== 'undefined') {\n        // Assume we are in a really old browser which has a bunch of limitations:\n        // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\n        // Extreme paranoia: if we have XDomainRequest then we have a window, but just in case\n        if (!window || !window.location) {\n          return callback(\n            new Error(\n              'No window available during request, unknown environment',\n            ),\n          );\n        }\n\n        // If the current page is http, try and send over http\n        if (\n          window.location.href.substring(0, 5) === 'http:' &&\n          url.substring(0, 5) === 'https'\n        ) {\n          url = 'http' + url.substring(5);\n        }\n\n        var xdomainrequest = new XDomainRequest();\n        xdomainrequest.onprogress = function () {};\n        xdomainrequest.ontimeout = function () {\n          var msg = 'Request timed out';\n          var code = 'ETIMEDOUT';\n          callback(_newRetriableError(msg, code));\n        };\n        xdomainrequest.onerror = function () {\n          callback(new Error('Error during request'));\n        };\n        xdomainrequest.onload = function () {\n          var parseResponse = _.jsonParse(xdomainrequest.responseText);\n          callback(parseResponse.error, parseResponse.value);\n        };\n        xdomainrequest.open(method, url, true);\n        xdomainrequest.send(data);\n      } else {\n        callback(new Error('Cannot find a method to transport a request'));\n      }\n    }\n  } catch (e2) {\n    callback(e2);\n  }\n}\n\nfunction _createXMLHTTPObject() {\n  /* global ActiveXObject:false */\n\n  var factories = [\n    function () {\n      return new XMLHttpRequest();\n    },\n    function () {\n      return new ActiveXObject('Msxml2.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Msxml3.XMLHTTP');\n    },\n    function () {\n      return new ActiveXObject('Microsoft.XMLHTTP');\n    },\n  ];\n  var xmlhttp;\n  var i;\n  var numFactories = factories.length;\n  for (i = 0; i < numFactories; i++) {\n    /* eslint-disable no-empty */\n    try {\n      xmlhttp = factories[i]();\n      break;\n    } catch (e) {\n      // pass\n    }\n    /* eslint-enable no-empty */\n  }\n  return xmlhttp;\n}\n\nfunction _isSuccess(r) {\n  return r && r.status && r.status === 200;\n}\n\nfunction _isNormalFailure(r) {\n  return r && _.isType(r.status, 'number') && r.status >= 400 && r.status < 600;\n}\n\nfunction _newRetriableError(message, code) {\n  var err = new Error(message);\n  err.code = code || 'ENOTFOUND';\n  return err;\n}\n\nmodule.exports = makeXhrRequest;\n", "'use strict';\n\n// See https://nodejs.org/docs/latest/api/url.html\nfunction parse(url) {\n  var result = {\n    protocol: null,\n    auth: null,\n    host: null,\n    path: null,\n    hash: null,\n    href: url,\n    hostname: null,\n    port: null,\n    pathname: null,\n    search: null,\n    query: null,\n  };\n\n  var i, last;\n  i = url.indexOf('//');\n  if (i !== -1) {\n    result.protocol = url.substring(0, i);\n    last = i + 2;\n  } else {\n    last = 0;\n  }\n\n  i = url.indexOf('@', last);\n  if (i !== -1) {\n    result.auth = url.substring(last, i);\n    last = i + 1;\n  }\n\n  i = url.indexOf('/', last);\n  if (i === -1) {\n    i = url.indexOf('?', last);\n    if (i === -1) {\n      i = url.indexOf('#', last);\n      if (i === -1) {\n        result.host = url.substring(last);\n      } else {\n        result.host = url.substring(last, i);\n        result.hash = url.substring(i);\n      }\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      return result;\n    } else {\n      result.host = url.substring(last, i);\n      result.hostname = result.host.split(':')[0];\n      result.port = result.host.split(':')[1];\n      if (result.port) {\n        result.port = parseInt(result.port, 10);\n      }\n      last = i;\n    }\n  } else {\n    result.host = url.substring(last, i);\n    result.hostname = result.host.split(':')[0];\n    result.port = result.host.split(':')[1];\n    if (result.port) {\n      result.port = parseInt(result.port, 10);\n    }\n    last = i;\n  }\n\n  i = url.indexOf('#', last);\n  if (i === -1) {\n    result.path = url.substring(last);\n  } else {\n    result.path = url.substring(last, i);\n    result.hash = url.substring(i);\n  }\n\n  if (result.path) {\n    var pathParts = result.path.split('?');\n    result.pathname = pathParts[0];\n    result.query = pathParts[1];\n    result.search = result.query ? '?' + result.query : null;\n  }\n  return result;\n}\n\nmodule.exports = {\n  parse: parse,\n};\n", "'use strict';\n\nfunction wrapGlobals(window, handler, shim) {\n  if (!window) {\n    return;\n  }\n  // Adapted from https://github.com/bugsnag/bugsnag-js\n  var globals =\n    'EventTarget,Window,Node,ApplicationCache,AudioTrackList,ChannelMergerNode,CryptoOperation,EventSource,FileReader,HTMLUnknownElement,IDBDatabase,IDBRequest,IDBTransaction,KeyOperation,MediaController,MessagePort,ModalWindow,Notification,SVGElementInstance,Screen,TextTrack,TextTrackCue,TextTrackList,WebSocket,WebSocketWorker,Worker,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload'.split(\n      ',',\n    );\n  var i, global;\n  for (i = 0; i < globals.length; ++i) {\n    global = globals[i];\n\n    if (window[global] && window[global].prototype) {\n      _extendListenerPrototype(handler, window[global].prototype, shim);\n    }\n  }\n}\n\nfunction _extendListenerPrototype(handler, prototype, shim) {\n  if (\n    prototype.hasOwnProperty &&\n    prototype.hasOwnProperty('addEventListener')\n  ) {\n    var oldAddEventListener = prototype.addEventListener;\n    while (\n      oldAddEventListener._rollbarOldAdd &&\n      oldAddEventListener.belongsToShim\n    ) {\n      oldAddEventListener = oldAddEventListener._rollbarOldAdd;\n    }\n    var addFn = function (event, callback, bubble) {\n      oldAddEventListener.call(this, event, handler.wrap(callback), bubble);\n    };\n    addFn._rollbarOldAdd = oldAddEventListener;\n    addFn.belongsToShim = shim;\n    prototype.addEventListener = addFn;\n\n    var oldRemoveEventListener = prototype.removeEventListener;\n    while (\n      oldRemoveEventListener._rollbarOldRemove &&\n      oldRemoveEventListener.belongsToShim\n    ) {\n      oldRemoveEventListener = oldRemoveEventListener._rollbarOldRemove;\n    }\n    var removeFn = function (event, callback, bubble) {\n      oldRemoveEventListener.call(\n        this,\n        event,\n        (callback && callback._rollbar_wrapped) || callback,\n        bubble,\n      );\n    };\n    removeFn._rollbarOldRemove = oldRemoveEventListener;\n    removeFn.belongsToShim = shim;\n    prototype.removeEventListener = removeFn;\n  }\n}\n\nmodule.exports = wrapGlobals;\n", "'use strict';\n\nmodule.exports = {\n  version: '2.26.4',\n  endpoint: 'api.rollbar.com/api/1/item/',\n  logLevel: 'debug',\n  reportLevel: 'debug',\n  uncaughtErrorLevel: 'error',\n  maxItems: 0,\n  itemsPerMin: 60,\n};\n", "'use strict';\n\nvar ErrorStackParser = require('error-stack-parser');\n\nvar UNKNOWN_FUNCTION = '?';\nvar ERR_CLASS_REGEXP = new RegExp(\n  '^(([a-zA-Z0-9-_$ ]*): *)?(Uncaught )?([a-zA-Z0-9-_$ ]*): ',\n);\n\nfunction guessFunctionName() {\n  return UNKNOWN_FUNCTION;\n}\n\nfunction gatherContext() {\n  return null;\n}\n\nfunction Frame(stackFrame) {\n  var data = {};\n\n  data._stackFrame = stackFrame;\n\n  data.url = stackFrame.fileName;\n  data.line = stackFrame.lineNumber;\n  data.func = stackFrame.functionName;\n  data.column = stackFrame.columnNumber;\n  data.args = stackFrame.args;\n\n  data.context = gatherContext();\n\n  return data;\n}\n\nfunction Stack(exception, skip) {\n  function getStack() {\n    var parserStack = [];\n\n    skip = skip || 0;\n\n    try {\n      parserStack = ErrorStackParser.parse(exception);\n    } catch (e) {\n      parserStack = [];\n    }\n\n    var stack = [];\n\n    for (var i = skip; i < parserStack.length; i++) {\n      stack.push(new Frame(parserStack[i]));\n    }\n\n    return stack;\n  }\n\n  return {\n    stack: getStack(),\n    message: exception.message,\n    name: _mostSpecificErrorName(exception),\n    rawStack: exception.stack,\n    rawException: exception,\n  };\n}\n\nfunction parse(e, skip) {\n  var err = e;\n\n  if (err.nested || err.cause) {\n    var traceChain = [];\n    while (err) {\n      traceChain.push(new Stack(err, skip));\n      err = err.nested || err.cause;\n\n      skip = 0; // Only apply skip value to primary error\n    }\n\n    // Return primary error with full trace chain attached.\n    traceChain[0].traceChain = traceChain;\n    return traceChain[0];\n  } else {\n    return new Stack(err, skip);\n  }\n}\n\nfunction guessErrorClass(errMsg) {\n  if (!errMsg || !errMsg.match) {\n    return ['Unknown error. There was no error message to display.', ''];\n  }\n  var errClassMatch = errMsg.match(ERR_CLASS_REGEXP);\n  var errClass = '(unknown)';\n\n  if (errClassMatch) {\n    errClass = errClassMatch[errClassMatch.length - 1];\n    errMsg = errMsg.replace(\n      (errClassMatch[errClassMatch.length - 2] || '') + errClass + ':',\n      '',\n    );\n    errMsg = errMsg.replace(/(^[\\s]+|[\\s]+$)/g, '');\n  }\n  return [errClass, errMsg];\n}\n\n// * Prefers any value over an empty string\n// * Prefers any value over 'Error' where possible\n// * Prefers name over constructor.name when both are more specific than 'Error'\nfunction _mostSpecificErrorName(error) {\n  var name = error.name && error.name.length && error.name;\n  var constructorName =\n    error.constructor.name &&\n    error.constructor.name.length &&\n    error.constructor.name;\n\n  if (!name || !constructorName) {\n    return name || constructorName;\n  }\n\n  if (name === 'Error') {\n    return constructorName;\n  }\n  return name;\n}\n\nmodule.exports = {\n  guessFunctionName: guessFunctionName,\n  guessErrorClass: guessErrorClass,\n  gatherContext: gatherContext,\n  parse: parse,\n  Stack: Stack,\n  Frame: Frame,\n};\n", "'use strict';\n\n'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\n\nvar isPlainObject = function isPlainObject(obj) {\n  if (!obj || toStr.call(obj) !== '[object Object]') {\n    return false;\n  }\n\n  var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n  var hasIsPrototypeOf =\n    obj.constructor &&\n    obj.constructor.prototype &&\n    hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n  // Not own constructor property must be Object\n  if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n    return false;\n  }\n\n  // Own properties are enumerated firstly, so to speed up,\n  // if last one is own, then all properties are own.\n  var key;\n  for (key in obj) {\n    /**/\n  }\n\n  return typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\nfunction merge() {\n  var i,\n    src,\n    copy,\n    clone,\n    name,\n    result = {},\n    current = null,\n    length = arguments.length;\n\n  for (i = 0; i < length; i++) {\n    current = arguments[i];\n    if (current == null) {\n      continue;\n    }\n\n    for (name in current) {\n      src = result[name];\n      copy = current[name];\n      if (result !== copy) {\n        if (copy && isPlainObject(copy)) {\n          clone = src && isPlainObject(src) ? src : {};\n          result[name] = merge(clone, copy);\n        } else if (typeof copy !== 'undefined') {\n          result[name] = copy;\n        }\n      }\n    }\n  }\n  return result;\n}\n\nmodule.exports = merge;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Notifier - the internal object responsible for delegating between the client exposed API, the\n * chain of transforms necessary to turn an item into something that can be sent to Rollbar, and the\n * queue which handles the communcation with the Rollbar API servers.\n *\n * @param queue - an object that conforms to the interface: addItem(item, callback)\n * @param options - an object representing the options to be set for this notifier, this should have\n * any defaults already set by the caller\n */\nfunction Notifier(queue, options) {\n  this.queue = queue;\n  this.options = options;\n  this.transforms = [];\n  this.diagnostic = {};\n}\n\n/*\n * configure - updates the options for this notifier with the passed in object\n *\n * @param options - an object which gets merged with the current options set on this notifier\n * @returns this\n */\nNotifier.prototype.configure = function (options) {\n  this.queue && this.queue.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addTransform - adds a transform onto the end of the queue of transforms for this notifier\n *\n * @param transform - a function which takes three arguments:\n *    * item: An Object representing the data to eventually be sent to Rollbar\n *    * options: The current value of the options for this notifier\n *    * callback: function(err: (Null|Error), item: (Null|Object)) the transform must call this\n *    callback with a null value for error if it wants the processing chain to continue, otherwise\n *    with an error to terminate the processing. The item should be the updated item after this\n *    transform is finished modifying it.\n */\nNotifier.prototype.addTransform = function (transform) {\n  if (_.isFunction(transform)) {\n    this.transforms.push(transform);\n  }\n  return this;\n};\n\n/*\n * log - the internal log function which applies the configured transforms and then pushes onto the\n * queue to be sent to the backend.\n *\n * @param item - An object with the following structure:\n *    message [String] - An optional string to be sent to rollbar\n *    error [Error] - An optional error\n *\n * @param callback - A function of type function(err, resp) which will be called with exactly one\n * null argument and one non-null argument. The callback will be called once, either during the\n * transform stage if an error occurs inside a transform, or in response to the communication with\n * the backend. The second argument will be the response from the backend in case of success.\n */\nNotifier.prototype.log = function (item, callback) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {};\n  }\n\n  if (!this.options.enabled) {\n    return callback(new Error('Rollbar is not enabled'));\n  }\n\n  this.queue.addPendingItem(item);\n  var originalError = item.err;\n  this._applyTransforms(\n    item,\n    function (err, i) {\n      if (err) {\n        this.queue.removePendingItem(item);\n        return callback(err, null);\n      }\n      this.queue.addItem(i, callback, originalError, item);\n    }.bind(this),\n  );\n};\n\n/* Internal */\n\n/*\n * _applyTransforms - Applies the transforms that have been added to this notifier sequentially. See\n * `addTransform` for more information.\n *\n * @param item - An item to be transformed\n * @param callback - A function of type function(err, item) which will be called with a non-null\n * error and a null item in the case of a transform failure, or a null error and non-null item after\n * all transforms have been applied.\n */\nNotifier.prototype._applyTransforms = function (item, callback) {\n  var transformIndex = -1;\n  var transformsLength = this.transforms.length;\n  var transforms = this.transforms;\n  var options = this.options;\n\n  var cb = function (err, i) {\n    if (err) {\n      callback(err, null);\n      return;\n    }\n\n    transformIndex++;\n\n    if (transformIndex === transformsLength) {\n      callback(null, i);\n      return;\n    }\n\n    transforms[transformIndex](i, options, cb);\n  };\n\n  cb(null, item);\n};\n\nmodule.exports = Notifier;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction checkLevel(item, settings) {\n  var level = item.level;\n  var levelVal = _.LEVELS[level] || 0;\n  var reportLevel = settings.reportLevel;\n  var reportLevelVal = _.LEVELS[reportLevel] || 0;\n\n  if (levelVal < reportLevelVal) {\n    return false;\n  }\n  return true;\n}\n\nfunction userCheckIgnore(logger) {\n  return function (item, settings) {\n    var isUncaught = !!item._isUncaught;\n    delete item._isUncaught;\n    var args = item._originalArgs;\n    delete item._originalArgs;\n    try {\n      if (_.isFunction(settings.onSendCallback)) {\n        settings.onSendCallback(isUncaught, args, item);\n      }\n    } catch (e) {\n      settings.onSendCallback = null;\n      logger.error('Error while calling onSendCallback, removing', e);\n    }\n    try {\n      if (\n        _.isFunction(settings.checkIgnore) &&\n        settings.checkIgnore(isUncaught, args, item)\n      ) {\n        return false;\n      }\n    } catch (e) {\n      settings.checkIgnore = null;\n      logger.error('Error while calling custom checkIgnore(), removing', e);\n    }\n    return true;\n  };\n}\n\nfunction urlIsNotBlockListed(logger) {\n  return function (item, settings) {\n    return !urlIsOnAList(item, settings, 'blocklist', logger);\n  };\n}\n\nfunction urlIsSafeListed(logger) {\n  return function (item, settings) {\n    return urlIsOnAList(item, settings, 'safelist', logger);\n  };\n}\n\nfunction matchFrames(trace, list, block) {\n  if (!trace) {\n    return !block;\n  }\n\n  var frames = trace.frames;\n\n  if (!frames || frames.length === 0) {\n    return !block;\n  }\n\n  var frame, filename, url, urlRegex;\n  var listLength = list.length;\n  var frameLength = frames.length;\n  for (var i = 0; i < frameLength; i++) {\n    frame = frames[i];\n    filename = frame.filename;\n\n    if (!_.isType(filename, 'string')) {\n      return !block;\n    }\n\n    for (var j = 0; j < listLength; j++) {\n      url = list[j];\n      urlRegex = new RegExp(url);\n\n      if (urlRegex.test(filename)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction urlIsOnAList(item, settings, safeOrBlock, logger) {\n  // safelist is the default\n  var block = false;\n  if (safeOrBlock === 'blocklist') {\n    block = true;\n  }\n\n  var list, traces;\n  try {\n    list = block ? settings.hostBlockList : settings.hostSafeList;\n    traces = _.get(item, 'body.trace_chain') || [_.get(item, 'body.trace')];\n\n    // These two checks are important to come first as they are defaults\n    // in case the list is missing or the trace is missing or not well-formed\n    if (!list || list.length === 0) {\n      return !block;\n    }\n    if (traces.length === 0 || !traces[0]) {\n      return !block;\n    }\n\n    var tracesLength = traces.length;\n    for (var i = 0; i < tracesLength; i++) {\n      if (matchFrames(traces[i], list, block)) {\n        return true;\n      }\n    }\n  } catch (\n    e\n    /* istanbul ignore next */\n  ) {\n    if (block) {\n      settings.hostBlockList = null;\n    } else {\n      settings.hostSafeList = null;\n    }\n    var listName = block ? 'hostBlockList' : 'hostSafeList';\n    logger.error(\n      \"Error while reading your configuration's \" +\n        listName +\n        ' option. Removing custom ' +\n        listName +\n        '.',\n      e,\n    );\n    return !block;\n  }\n  return false;\n}\n\nfunction messageIsIgnored(logger) {\n  return function (item, settings) {\n    var i, j, ignoredMessages, len, messageIsIgnored, rIgnoredMessage, messages;\n\n    try {\n      messageIsIgnored = false;\n      ignoredMessages = settings.ignoredMessages;\n\n      if (!ignoredMessages || ignoredMessages.length === 0) {\n        return true;\n      }\n\n      messages = messagesFromItem(item);\n\n      if (messages.length === 0) {\n        return true;\n      }\n\n      len = ignoredMessages.length;\n      for (i = 0; i < len; i++) {\n        rIgnoredMessage = new RegExp(ignoredMessages[i], 'gi');\n\n        for (j = 0; j < messages.length; j++) {\n          messageIsIgnored = rIgnoredMessage.test(messages[j]);\n\n          if (messageIsIgnored) {\n            return false;\n          }\n        }\n      }\n    } catch (\n      e\n      /* istanbul ignore next */\n    ) {\n      settings.ignoredMessages = null;\n      logger.error(\n        \"Error while reading your configuration's ignoredMessages option. Removing custom ignoredMessages.\",\n      );\n    }\n\n    return true;\n  };\n}\n\nfunction messagesFromItem(item) {\n  var body = item.body;\n  var messages = [];\n\n  // The payload schema only allows one of trace_chain, message, or trace.\n  // However, existing test cases are based on having both trace and message present.\n  // So here we preserve the ability to collect strings from any combination of these keys.\n  if (body.trace_chain) {\n    var traceChain = body.trace_chain;\n    for (var i = 0; i < traceChain.length; i++) {\n      var trace = traceChain[i];\n      messages.push(_.get(trace, 'exception.message'));\n    }\n  }\n  if (body.trace) {\n    messages.push(_.get(body, 'trace.exception.message'));\n  }\n  if (body.message) {\n    messages.push(_.get(body, 'message.body'));\n  }\n  return messages;\n}\n\nmodule.exports = {\n  checkLevel: checkLevel,\n  userCheckIgnore: userCheckIgnore,\n  urlIsNotBlockListed: urlIsNotBlockListed,\n  urlIsSafeListed: urlIsSafeListed,\n  messageIsIgnored: messageIsIgnored,\n};\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * Queue - an object which handles which handles a queue of items to be sent to Rollbar.\n *   This object handles rate limiting via a passed in rate limiter, retries based on connection\n *   errors, and filtering of items based on a set of configurable predicates. The communication to\n *   the backend is performed via a given API object.\n *\n * @param rateLimiter - An object which conforms to the interface\n *    rateLimiter.shouldSend(item) -> bool\n * @param api - An object which conforms to the interface\n *    api.postItem(payload, function(err, response))\n * @param logger - An object used to log verbose messages if desired\n * @param options - see Queue.prototype.configure\n */\nfunction Queue(rateLimiter, api, logger, options) {\n  this.rateLimiter = rateLimiter;\n  this.api = api;\n  this.logger = logger;\n  this.options = options;\n  this.predicates = [];\n  this.pendingItems = [];\n  this.pendingRequests = [];\n  this.retryQueue = [];\n  this.retryHandle = null;\n  this.waitCallback = null;\n  this.waitIntervalID = null;\n}\n\n/*\n * configure - updates the options this queue uses\n *\n * @param options\n */\nQueue.prototype.configure = function (options) {\n  this.api && this.api.configure(options);\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  return this;\n};\n\n/*\n * addPredicate - adds a predicate to the end of the list of predicates for this queue\n *\n * @param predicate - function(item, options) -> (bool|{err: Error})\n *  Returning true means that this predicate passes and the item is okay to go on the queue\n *  Returning false means do not add the item to the queue, but it is not an error\n *  Returning {err: Error} means do not add the item to the queue, and the given error explains why\n *  Returning {err: undefined} is equivalent to returning true but don't do that\n */\nQueue.prototype.addPredicate = function (predicate) {\n  if (_.isFunction(predicate)) {\n    this.predicates.push(predicate);\n  }\n  return this;\n};\n\nQueue.prototype.addPendingItem = function (item) {\n  this.pendingItems.push(item);\n};\n\nQueue.prototype.removePendingItem = function (item) {\n  var idx = this.pendingItems.indexOf(item);\n  if (idx !== -1) {\n    this.pendingItems.splice(idx, 1);\n  }\n};\n\n/*\n * addItem - Send an item to the Rollbar API if all of the predicates are satisfied\n *\n * @param item - The payload to send to the backend\n * @param callback - function(error, repsonse) which will be called with the response from the API\n *  in the case of a success, otherwise response will be null and error will have a value. If both\n *  error and response are null then the item was stopped by a predicate which did not consider this\n *  to be an error condition, but nonetheless did not send the item to the API.\n *  @param originalError - The original error before any transformations that is to be logged if any\n */\nQueue.prototype.addItem = function (\n  item,\n  callback,\n  originalError,\n  originalItem,\n) {\n  if (!callback || !_.isFunction(callback)) {\n    callback = function () {\n      return;\n    };\n  }\n  var predicateResult = this._applyPredicates(item);\n  if (predicateResult.stop) {\n    this.removePendingItem(originalItem);\n    callback(predicateResult.err);\n    return;\n  }\n  this._maybeLog(item, originalError);\n  this.removePendingItem(originalItem);\n  if (!this.options.transmit) {\n    callback(new Error('Transmit disabled'));\n    return;\n  }\n  this.pendingRequests.push(item);\n  try {\n    this._makeApiRequest(\n      item,\n      function (err, resp) {\n        this._dequeuePendingRequest(item);\n        callback(err, resp);\n      }.bind(this),\n    );\n  } catch (e) {\n    this._dequeuePendingRequest(item);\n    callback(e);\n  }\n};\n\n/*\n * wait - Stop any further errors from being added to the queue, and get called back when all items\n *   currently processing have finished sending to the backend.\n *\n * @param callback - function() called when all pending items have been sent\n */\nQueue.prototype.wait = function (callback) {\n  if (!_.isFunction(callback)) {\n    return;\n  }\n  this.waitCallback = callback;\n  if (this._maybeCallWait()) {\n    return;\n  }\n  if (this.waitIntervalID) {\n    this.waitIntervalID = clearInterval(this.waitIntervalID);\n  }\n  this.waitIntervalID = setInterval(\n    function () {\n      this._maybeCallWait();\n    }.bind(this),\n    500,\n  );\n};\n\n/* _applyPredicates - Sequentially applies the predicates that have been added to the queue to the\n *   given item with the currently configured options.\n *\n * @param item - An item in the queue\n * @returns {stop: bool, err: (Error|null)} - stop being true means do not add item to the queue,\n *   the error value should be passed up to a callbak if we are stopping.\n */\nQueue.prototype._applyPredicates = function (item) {\n  var p = null;\n  for (var i = 0, len = this.predicates.length; i < len; i++) {\n    p = this.predicates[i](item, this.options);\n    if (!p || p.err !== undefined) {\n      return { stop: true, err: p.err };\n    }\n  }\n  return { stop: false, err: null };\n};\n\n/*\n * _makeApiRequest - Send an item to Rollbar, callback when done, if there is an error make an\n *   effort to retry if we are configured to do so.\n *\n * @param item - an item ready to send to the backend\n * @param callback - function(err, response)\n */\nQueue.prototype._makeApiRequest = function (item, callback) {\n  var rateLimitResponse = this.rateLimiter.shouldSend(item);\n  if (rateLimitResponse.shouldSend) {\n    this.api.postItem(\n      item,\n      function (err, resp) {\n        if (err) {\n          this._maybeRetry(err, item, callback);\n        } else {\n          callback(err, resp);\n        }\n      }.bind(this),\n    );\n  } else if (rateLimitResponse.error) {\n    callback(rateLimitResponse.error);\n  } else {\n    this.api.postItem(rateLimitResponse.payload, callback);\n  }\n};\n\n// These are errors basically mean there is no internet connection\nvar RETRIABLE_ERRORS = [\n  'ECONNRESET',\n  'ENOTFOUND',\n  'ESOCKETTIMEDOUT',\n  'ETIMEDOUT',\n  'ECONNREFUSED',\n  'EHOSTUNREACH',\n  'EPIPE',\n  'EAI_AGAIN',\n];\n\n/*\n * _maybeRetry - Given the error returned by the API, decide if we should retry or just callback\n *   with the error.\n *\n * @param err - an error returned by the API transport\n * @param item - the item that was trying to be sent when this error occured\n * @param callback - function(err, response)\n */\nQueue.prototype._maybeRetry = function (err, item, callback) {\n  var shouldRetry = false;\n  if (this.options.retryInterval) {\n    for (var i = 0, len = RETRIABLE_ERRORS.length; i < len; i++) {\n      if (err.code === RETRIABLE_ERRORS[i]) {\n        shouldRetry = true;\n        break;\n      }\n    }\n    if (shouldRetry && _.isFiniteNumber(this.options.maxRetries)) {\n      item.retries = item.retries ? item.retries + 1 : 1;\n      if (item.retries > this.options.maxRetries) {\n        shouldRetry = false;\n      }\n    }\n  }\n  if (shouldRetry) {\n    this._retryApiRequest(item, callback);\n  } else {\n    callback(err);\n  }\n};\n\n/*\n * _retryApiRequest - Add an item and a callback to a queue and possibly start a timer to process\n *   that queue based on the retryInterval in the options for this queue.\n *\n * @param item - an item that failed to send due to an error we deem retriable\n * @param callback - function(err, response)\n */\nQueue.prototype._retryApiRequest = function (item, callback) {\n  this.retryQueue.push({ item: item, callback: callback });\n\n  if (!this.retryHandle) {\n    this.retryHandle = setInterval(\n      function () {\n        while (this.retryQueue.length) {\n          var retryObject = this.retryQueue.shift();\n          this._makeApiRequest(retryObject.item, retryObject.callback);\n        }\n      }.bind(this),\n      this.options.retryInterval,\n    );\n  }\n};\n\n/*\n * _dequeuePendingRequest - Removes the item from the pending request queue, this queue is used to\n *   enable to functionality of providing a callback that clients can pass to `wait` to be notified\n *   when the pending request queue has been emptied. This must be called when the API finishes\n *   processing this item. If a `wait` callback is configured, it is called by this function.\n *\n * @param item - the item previously added to the pending request queue\n */\nQueue.prototype._dequeuePendingRequest = function (item) {\n  var idx = this.pendingRequests.indexOf(item);\n  if (idx !== -1) {\n    this.pendingRequests.splice(idx, 1);\n    this._maybeCallWait();\n  }\n};\n\nQueue.prototype._maybeLog = function (data, originalError) {\n  if (this.logger && this.options.verbose) {\n    var message = originalError;\n    message = message || _.get(data, 'body.trace.exception.message');\n    message = message || _.get(data, 'body.trace_chain.0.exception.message');\n    if (message) {\n      this.logger.error(message);\n      return;\n    }\n    message = _.get(data, 'body.message.body');\n    if (message) {\n      this.logger.log(message);\n    }\n  }\n};\n\nQueue.prototype._maybeCallWait = function () {\n  if (\n    _.isFunction(this.waitCallback) &&\n    this.pendingItems.length === 0 &&\n    this.pendingRequests.length === 0\n  ) {\n    if (this.waitIntervalID) {\n      this.waitIntervalID = clearInterval(this.waitIntervalID);\n    }\n    this.waitCallback();\n    return true;\n  }\n  return false;\n};\n\nmodule.exports = Queue;\n", "'use strict';\n\nvar _ = require('./utility');\n\n/*\n * RateLimiter - an object that encapsulates the logic for counting items sent to Rollbar\n *\n * @param options - the same options that are accepted by configureGlobal offered as a convenience\n */\nfunction RateLimiter(options) {\n  this.startTime = _.now();\n  this.counter = 0;\n  this.perMinCounter = 0;\n  this.platform = null;\n  this.platformOptions = {};\n  this.configureGlobal(options);\n}\n\nRateLimiter.globalSettings = {\n  startTime: _.now(),\n  maxItems: undefined,\n  itemsPerMinute: undefined,\n};\n\n/*\n * configureGlobal - set the global rate limiter options\n *\n * @param options - Only the following values are recognized:\n *    startTime: a timestamp of the form returned by (new Date()).getTime()\n *    maxItems: the maximum items\n *    itemsPerMinute: the max number of items to send in a given minute\n */\nRateLimiter.prototype.configureGlobal = function (options) {\n  if (options.startTime !== undefined) {\n    RateLimiter.globalSettings.startTime = options.startTime;\n  }\n  if (options.maxItems !== undefined) {\n    RateLimiter.globalSettings.maxItems = options.maxItems;\n  }\n  if (options.itemsPerMinute !== undefined) {\n    RateLimiter.globalSettings.itemsPerMinute = options.itemsPerMinute;\n  }\n};\n\n/*\n * shouldSend - determine if we should send a given item based on rate limit settings\n *\n * @param item - the item we are about to send\n * @returns An object with the following structure:\n *  error: (Error|null)\n *  shouldSend: bool\n *  payload: (Object|null)\n *  If shouldSend is false, the item passed as a parameter should not be sent to Rollbar, and\n *  exactly one of error or payload will be non-null. If error is non-null, the returned Error will\n *  describe the situation, but it means that we were already over a rate limit (either globally or\n *  per minute) when this item was checked. If error is null, and therefore payload is non-null, it\n *  means this item put us over the global rate limit and the payload should be sent to Rollbar in\n *  place of the passed in item.\n */\nRateLimiter.prototype.shouldSend = function (item, now) {\n  now = now || _.now();\n  var elapsedTime = now - this.startTime;\n  if (elapsedTime < 0 || elapsedTime >= 60000) {\n    this.startTime = now;\n    this.perMinCounter = 0;\n  }\n\n  var globalRateLimit = RateLimiter.globalSettings.maxItems;\n  var globalRateLimitPerMin = RateLimiter.globalSettings.itemsPerMinute;\n\n  if (checkRate(item, globalRateLimit, this.counter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimit + ' max items reached',\n      false,\n    );\n  } else if (checkRate(item, globalRateLimitPerMin, this.perMinCounter)) {\n    return shouldSendValue(\n      this.platform,\n      this.platformOptions,\n      globalRateLimitPerMin + ' items per minute reached',\n      false,\n    );\n  }\n  this.counter++;\n  this.perMinCounter++;\n\n  var shouldSend = !checkRate(item, globalRateLimit, this.counter);\n  var perMinute = shouldSend;\n  shouldSend =\n    shouldSend && !checkRate(item, globalRateLimitPerMin, this.perMinCounter);\n  return shouldSendValue(\n    this.platform,\n    this.platformOptions,\n    null,\n    shouldSend,\n    globalRateLimit,\n    globalRateLimitPerMin,\n    perMinute,\n  );\n};\n\nRateLimiter.prototype.setPlatformOptions = function (platform, options) {\n  this.platform = platform;\n  this.platformOptions = options;\n};\n\n/* Helpers */\n\nfunction checkRate(item, limit, counter) {\n  return !item.ignoreRateLimit && limit >= 1 && counter > limit;\n}\n\nfunction shouldSendValue(\n  platform,\n  options,\n  error,\n  shouldSend,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var payload = null;\n  if (error) {\n    error = new Error(error);\n  }\n  if (!error && !shouldSend) {\n    payload = rateLimitPayload(\n      platform,\n      options,\n      globalRateLimit,\n      limitPerMin,\n      perMinute,\n    );\n  }\n  return { error: error, shouldSend: shouldSend, payload: payload };\n}\n\nfunction rateLimitPayload(\n  platform,\n  options,\n  globalRateLimit,\n  limitPerMin,\n  perMinute,\n) {\n  var environment =\n    options.environment || (options.payload && options.payload.environment);\n  var msg;\n  if (perMinute) {\n    msg = 'item per minute limit reached, ignoring errors until timeout';\n  } else {\n    msg = 'maxItems has been hit, ignoring errors until reset.';\n  }\n  var item = {\n    body: {\n      message: {\n        body: msg,\n        extra: {\n          maxItems: globalRateLimit,\n          itemsPerMinute: limitPerMin,\n        },\n      },\n    },\n    language: 'javascript',\n    environment: environment,\n    notifier: {\n      version:\n        (options.notifier && options.notifier.version) || options.version,\n    },\n  };\n  if (platform === 'browser') {\n    item.platform = 'browser';\n    item.framework = 'browser-js';\n    item.notifier.name = 'rollbar-browser-js';\n  } else if (platform === 'server') {\n    item.framework = options.framework || 'node-js';\n    item.notifier.name = options.notifier.name;\n  } else if (platform === 'react-native') {\n    item.framework = options.framework || 'react-native';\n    item.notifier.name = options.notifier.name;\n  }\n  return item;\n}\n\nmodule.exports = RateLimiter;\n", "'use strict';\n\nvar RateLimiter = require('./rateLimiter');\nvar Queue = require('./queue');\nvar Notifier = require('./notifier');\nvar _ = require('./utility');\n\n/*\n * Rollbar - the interface to Rollbar\n *\n * @param options\n * @param api\n * @param logger\n */\nfunction Rollbar(options, api, logger, telemeter, platform) {\n  this.options = _.merge(options);\n  this.logger = logger;\n  Rollbar.rateLimiter.configureGlobal(this.options);\n  Rollbar.rateLimiter.setPlatformOptions(platform, this.options);\n  this.api = api;\n  this.queue = new Queue(Rollbar.rateLimiter, api, logger, this.options);\n\n  // This must happen before the Notifier is created\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier = new Notifier(this.queue, this.options);\n  this.telemeter = telemeter;\n  setStackTraceLimit(options);\n  this.lastError = null;\n  this.lastErrorHash = 'none';\n}\n\nvar defaultOptions = {\n  maxItems: 0,\n  itemsPerMinute: 60,\n};\n\nRollbar.rateLimiter = new RateLimiter(defaultOptions);\n\nRollbar.prototype.global = function (options) {\n  Rollbar.rateLimiter.configureGlobal(options);\n  return this;\n};\n\nRollbar.prototype.configure = function (options, payloadData) {\n  var oldOptions = this.options;\n  var payload = {};\n  if (payloadData) {\n    payload = { payload: payloadData };\n  }\n\n  this.options = _.merge(oldOptions, options, payload);\n\n  // This must happen before the Notifier is configured\n  var tracer = this.options.tracer || null;\n  if (validateTracer(tracer)) {\n    this.tracer = tracer;\n    // set to a string for api response serialization\n    this.options.tracer = 'opentracing-tracer-enabled';\n    this.options._configuredOptions.tracer = 'opentracing-tracer-enabled';\n  } else {\n    this.tracer = null;\n  }\n\n  this.notifier && this.notifier.configure(this.options);\n  this.telemeter && this.telemeter.configure(this.options);\n  setStackTraceLimit(options);\n  this.global(this.options);\n\n  if (validateTracer(options.tracer)) {\n    this.tracer = options.tracer;\n  }\n\n  return this;\n};\n\nRollbar.prototype.log = function (item) {\n  var level = this._defaultLogLevel();\n  return this._log(level, item);\n};\n\nRollbar.prototype.debug = function (item) {\n  this._log('debug', item);\n};\n\nRollbar.prototype.info = function (item) {\n  this._log('info', item);\n};\n\nRollbar.prototype.warn = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.warning = function (item) {\n  this._log('warning', item);\n};\n\nRollbar.prototype.error = function (item) {\n  this._log('error', item);\n};\n\nRollbar.prototype.critical = function (item) {\n  this._log('critical', item);\n};\n\nRollbar.prototype.wait = function (callback) {\n  this.queue.wait(callback);\n};\n\nRollbar.prototype.captureEvent = function (type, metadata, level) {\n  return this.telemeter && this.telemeter.captureEvent(type, metadata, level);\n};\n\nRollbar.prototype.captureDomContentLoaded = function (ts) {\n  return this.telemeter && this.telemeter.captureDomContentLoaded(ts);\n};\n\nRollbar.prototype.captureLoad = function (ts) {\n  return this.telemeter && this.telemeter.captureLoad(ts);\n};\n\nRollbar.prototype.buildJsonPayload = function (item) {\n  return this.api.buildJsonPayload(item);\n};\n\nRollbar.prototype.sendJsonPayload = function (jsonPayload) {\n  this.api.postJsonPayload(jsonPayload);\n};\n\n/* Internal */\n\nRollbar.prototype._log = function (defaultLevel, item) {\n  var callback;\n  if (item.callback) {\n    callback = item.callback;\n    delete item.callback;\n  }\n  if (this.options.ignoreDuplicateErrors && this._sameAsLastError(item)) {\n    if (callback) {\n      var error = new Error('ignored identical item');\n      error.item = item;\n      callback(error);\n    }\n    return;\n  }\n  try {\n    this._addTracingInfo(item);\n    item.level = item.level || defaultLevel;\n    this.telemeter && this.telemeter._captureRollbarItem(item);\n    item.telemetryEvents =\n      (this.telemeter && this.telemeter.copyEvents()) || [];\n    this.notifier.log(item, callback);\n  } catch (e) {\n    if (callback) {\n      callback(e);\n    }\n    this.logger.error(e);\n  }\n};\n\nRollbar.prototype._defaultLogLevel = function () {\n  return this.options.logLevel || 'debug';\n};\n\nRollbar.prototype._sameAsLastError = function (item) {\n  if (!item._isUncaught) {\n    return false;\n  }\n  var itemHash = generateItemHash(item);\n  if (this.lastErrorHash === itemHash) {\n    return true;\n  }\n  this.lastError = item.err;\n  this.lastErrorHash = itemHash;\n  return false;\n};\n\nRollbar.prototype._addTracingInfo = function (item) {\n  // Tracer validation occurs in the constructor\n  // or in the Rollbar.prototype.configure methods\n  if (this.tracer) {\n    // add rollbar occurrence uuid to span\n    var span = this.tracer.scope().active();\n\n    if (validateSpan(span)) {\n      span.setTag('rollbar.error_uuid', item.uuid);\n      span.setTag('rollbar.has_error', true);\n      span.setTag('error', true);\n      span.setTag(\n        'rollbar.item_url',\n        `https://rollbar.com/item/uuid/?uuid=${item.uuid}`,\n      );\n      span.setTag(\n        'rollbar.occurrence_url',\n        `https://rollbar.com/occurrence/uuid/?uuid=${item.uuid}`,\n      );\n\n      // add span ID & trace ID to occurrence\n      var opentracingSpanId = span.context().toSpanId();\n      var opentracingTraceId = span.context().toTraceId();\n\n      if (item.custom) {\n        item.custom.opentracing_span_id = opentracingSpanId;\n        item.custom.opentracing_trace_id = opentracingTraceId;\n      } else {\n        item.custom = {\n          opentracing_span_id: opentracingSpanId,\n          opentracing_trace_id: opentracingTraceId,\n        };\n      }\n    }\n  }\n};\n\nfunction generateItemHash(item) {\n  var message = item.message || '';\n  var stack = (item.err || {}).stack || String(item.err);\n  return message + '::' + stack;\n}\n\n// Node.js, Chrome, Safari, and some other browsers support this property\n// which globally sets the number of stack frames returned in an Error object.\n// If a browser can't use it, no harm done.\nfunction setStackTraceLimit(options) {\n  if (options.stackTraceLimit) {\n    Error.stackTraceLimit = options.stackTraceLimit;\n  }\n}\n\n/**\n * Validate the Tracer object provided to the Client\n * is valid for our Opentracing use case.\n * @param {opentracer.Tracer} tracer\n */\nfunction validateTracer(tracer) {\n  if (!tracer) {\n    return false;\n  }\n\n  if (!tracer.scope || typeof tracer.scope !== 'function') {\n    return false;\n  }\n\n  var scope = tracer.scope();\n\n  if (!scope || !scope.active || typeof scope.active !== 'function') {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Validate the Span object provided\n * @param {opentracer.Span} span\n */\nfunction validateSpan(span) {\n  if (!span || !span.context || typeof span.context !== 'function') {\n    return false;\n  }\n\n  var spanContext = span.context();\n\n  if (\n    !spanContext ||\n    !spanContext.toSpanId ||\n    !spanContext.toTraceId ||\n    typeof spanContext.toSpanId !== 'function' ||\n    typeof spanContext.toTraceId !== 'function'\n  ) {\n    return false;\n  }\n\n  return true;\n}\n\nmodule.exports = Rollbar;\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction scrub(data, scrubFields, scrubPaths) {\n  scrubFields = scrubFields || [];\n\n  if (scrubPaths) {\n    for (var i = 0; i < scrubPaths.length; ++i) {\n      scrubPath(data, scrubPaths[i]);\n    }\n  }\n\n  var paramRes = _getScrubFieldRegexs(scrubFields);\n  var queryRes = _getScrubQueryParamRegexs(scrubFields);\n\n  function redactQueryParam(dummy0, paramPart) {\n    return paramPart + _.redact();\n  }\n\n  function paramScrubber(v) {\n    var i;\n    if (_.isType(v, 'string')) {\n      for (i = 0; i < queryRes.length; ++i) {\n        v = v.replace(queryRes[i], redactQueryParam);\n      }\n    }\n    return v;\n  }\n\n  function valScrubber(k, v) {\n    var i;\n    for (i = 0; i < paramRes.length; ++i) {\n      if (paramRes[i].test(k)) {\n        v = _.redact();\n        break;\n      }\n    }\n    return v;\n  }\n\n  function scrubber(k, v, seen) {\n    var tmpV = valScrubber(k, v);\n    if (tmpV === v) {\n      if (_.isType(v, 'object') || _.isType(v, 'array')) {\n        return traverse(v, scrubber, seen);\n      }\n      return paramScrubber(tmpV);\n    } else {\n      return tmpV;\n    }\n  }\n\n  return traverse(data, scrubber);\n}\n\nfunction scrubPath(obj, path) {\n  var keys = path.split('.');\n  var last = keys.length - 1;\n  try {\n    for (var i = 0; i <= last; ++i) {\n      if (i < last) {\n        obj = obj[keys[i]];\n      } else {\n        obj[keys[i]] = _.redact();\n      }\n    }\n  } catch (e) {\n    // Missing key is OK;\n  }\n}\n\nfunction _getScrubFieldRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '^\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?$';\n    ret.push(new RegExp(pat, 'i'));\n  }\n  return ret;\n}\n\nfunction _getScrubQueryParamRegexs(scrubFields) {\n  var ret = [];\n  var pat;\n  for (var i = 0; i < scrubFields.length; ++i) {\n    pat = '\\\\[?(%5[bB])?' + scrubFields[i] + '\\\\[?(%5[bB])?\\\\]?(%5[dD])?';\n    ret.push(new RegExp('(' + pat + '=)([^&\\\\n]+)', 'igm'));\n  }\n  return ret;\n}\n\nmodule.exports = scrub;\n", "'use strict';\n\nvar _ = require('./utility');\n\nvar MAX_EVENTS = 100;\n\nfunction Telemeter(options) {\n  this.queue = [];\n  this.options = _.merge(options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  this.maxQueueSize = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n}\n\nTelemeter.prototype.configure = function (options) {\n  var oldOptions = this.options;\n  this.options = _.merge(oldOptions, options);\n  var maxTelemetryEvents = this.options.maxTelemetryEvents || MAX_EVENTS;\n  var newMaxEvents = Math.max(0, Math.min(maxTelemetryEvents, MAX_EVENTS));\n  var deleteCount = 0;\n  if (this.queue.length > newMaxEvents) {\n    deleteCount = this.queue.length - newMaxEvents;\n  }\n  this.maxQueueSize = newMaxEvents;\n  this.queue.splice(0, deleteCount);\n};\n\nTelemeter.prototype.copyEvents = function () {\n  var events = Array.prototype.slice.call(this.queue, 0);\n  if (_.isFunction(this.options.filterTelemetry)) {\n    try {\n      var i = events.length;\n      while (i--) {\n        if (this.options.filterTelemetry(events[i])) {\n          events.splice(i, 1);\n        }\n      }\n    } catch (e) {\n      this.options.filterTelemetry = null;\n    }\n  }\n  return events;\n};\n\nTelemeter.prototype.capture = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var e = {\n    level: getLevel(type, level),\n    type: type,\n    timestamp_ms: timestamp || _.now(),\n    body: metadata,\n    source: 'client',\n  };\n  if (rollbarUUID) {\n    e.uuid = rollbarUUID;\n  }\n\n  try {\n    if (\n      _.isFunction(this.options.filterTelemetry) &&\n      this.options.filterTelemetry(e)\n    ) {\n      return false;\n    }\n  } catch (exc) {\n    this.options.filterTelemetry = null;\n  }\n\n  this.push(e);\n  return e;\n};\n\nTelemeter.prototype.captureEvent = function (\n  type,\n  metadata,\n  level,\n  rollbarUUID,\n) {\n  return this.capture(type, metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.captureError = function (\n  err,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  var metadata = {\n    message: err.message || String(err),\n  };\n  if (err.stack) {\n    metadata.stack = err.stack;\n  }\n  return this.capture('error', metadata, level, rollbarUUID, timestamp);\n};\n\nTelemeter.prototype.captureLog = function (\n  message,\n  level,\n  rollbarUUID,\n  timestamp,\n) {\n  return this.capture(\n    'log',\n    {\n      message: message,\n    },\n    level,\n    rollbarUUID,\n    timestamp,\n  );\n};\n\nTelemeter.prototype.captureNetwork = function (\n  metadata,\n  subtype,\n  rollbarUUID,\n  requestData,\n) {\n  subtype = subtype || 'xhr';\n  metadata.subtype = metadata.subtype || subtype;\n  if (requestData) {\n    metadata.request = requestData;\n  }\n  var level = this.levelFromStatus(metadata.status_code);\n  return this.capture('network', metadata, level, rollbarUUID);\n};\n\nTelemeter.prototype.levelFromStatus = function (statusCode) {\n  if (statusCode >= 200 && statusCode < 400) {\n    return 'info';\n  }\n  if (statusCode === 0 || statusCode >= 400) {\n    return 'error';\n  }\n  return 'info';\n};\n\nTelemeter.prototype.captureDom = function (\n  subtype,\n  element,\n  value,\n  checked,\n  rollbarUUID,\n) {\n  var metadata = {\n    subtype: subtype,\n    element: element,\n  };\n  if (value !== undefined) {\n    metadata.value = value;\n  }\n  if (checked !== undefined) {\n    metadata.checked = checked;\n  }\n  return this.capture('dom', metadata, 'info', rollbarUUID);\n};\n\nTelemeter.prototype.captureNavigation = function (from, to, rollbarUUID) {\n  return this.capture(\n    'navigation',\n    { from: from, to: to },\n    'info',\n    rollbarUUID,\n  );\n};\n\nTelemeter.prototype.captureDomContentLoaded = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'DOMContentLoaded' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'DOMContentLoaded'}, 'info', undefined, ts && ts.getTime());\n  */\n};\nTelemeter.prototype.captureLoad = function (ts) {\n  return this.capture(\n    'navigation',\n    { subtype: 'load' },\n    'info',\n    undefined,\n    ts && ts.getTime(),\n  );\n  /**\n   * If we decide to make this a dom event instead, then use the line below:\n  return this.capture('dom', {subtype: 'load'}, 'info', undefined, ts && ts.getTime());\n  */\n};\n\nTelemeter.prototype.captureConnectivityChange = function (type, rollbarUUID) {\n  return this.captureNetwork({ change: type }, 'connectivity', rollbarUUID);\n};\n\n// Only intended to be used internally by the notifier\nTelemeter.prototype._captureRollbarItem = function (item) {\n  if (!this.options.includeItemsInTelemetry) {\n    return;\n  }\n  if (item.err) {\n    return this.captureError(item.err, item.level, item.uuid, item.timestamp);\n  }\n  if (item.message) {\n    return this.captureLog(item.message, item.level, item.uuid, item.timestamp);\n  }\n  if (item.custom) {\n    return this.capture(\n      'log',\n      item.custom,\n      item.level,\n      item.uuid,\n      item.timestamp,\n    );\n  }\n};\n\nTelemeter.prototype.push = function (e) {\n  this.queue.push(e);\n  if (this.queue.length > this.maxQueueSize) {\n    this.queue.shift();\n  }\n};\n\nfunction getLevel(type, level) {\n  if (level) {\n    return level;\n  }\n  var defaultLevel = {\n    error: 'error',\n    manual: 'info',\n  };\n  return defaultLevel[type] || 'info';\n}\n\nmodule.exports = Telemeter;\n", "'use strict';\n\nvar _ = require('./utility');\n\nfunction itemToPayload(item, options, callback) {\n  var data = item.data;\n\n  if (item._isUncaught) {\n    data._isUncaught = true;\n  }\n  if (item._originalArgs) {\n    data._originalArgs = item._originalArgs;\n  }\n  callback(null, data);\n}\n\nfunction addPayloadOptions(item, options, callback) {\n  var payloadOptions = options.payload || {};\n  if (payloadOptions.body) {\n    delete payloadOptions.body;\n  }\n\n  item.data = _.merge(item.data, payloadOptions);\n  callback(null, item);\n}\n\nfunction addTelemetryData(item, options, callback) {\n  if (item.telemetryEvents) {\n    _.set(item, 'data.body.telemetry', item.telemetryEvents);\n  }\n  callback(null, item);\n}\n\nfunction addMessageWithError(item, options, callback) {\n  if (!item.message) {\n    callback(null, item);\n    return;\n  }\n  var tracePath = 'data.body.trace_chain.0';\n  var trace = _.get(item, tracePath);\n  if (!trace) {\n    tracePath = 'data.body.trace';\n    trace = _.get(item, tracePath);\n  }\n  if (trace) {\n    if (!(trace.exception && trace.exception.description)) {\n      _.set(item, tracePath + '.exception.description', item.message);\n      callback(null, item);\n      return;\n    }\n    var extra = _.get(item, tracePath + '.extra') || {};\n    var newExtra = _.merge(extra, { message: item.message });\n    _.set(item, tracePath + '.extra', newExtra);\n  }\n  callback(null, item);\n}\n\nfunction userTransform(logger) {\n  return function (item, options, callback) {\n    var newItem = _.merge(item);\n    var response = null;\n    try {\n      if (_.isFunction(options.transform)) {\n        response = options.transform(newItem.data, item);\n      }\n    } catch (e) {\n      options.transform = null;\n      logger.error(\n        'Error while calling custom transform() function. Removing custom transform().',\n        e,\n      );\n      callback(null, item);\n      return;\n    }\n    if (_.isPromise(response)) {\n      response.then(\n        function (promisedItem) {\n          if (promisedItem) {\n            newItem.data = promisedItem;\n          }\n          callback(null, newItem);\n        },\n        function (error) {\n          callback(error, item);\n        },\n      );\n    } else {\n      callback(null, newItem);\n    }\n  };\n}\n\nfunction addConfigToPayload(item, options, callback) {\n  if (!options.sendConfig) {\n    return callback(null, item);\n  }\n  var configKey = '_rollbarConfig';\n  var custom = _.get(item, 'data.custom') || {};\n  custom[configKey] = options;\n  item.data.custom = custom;\n  callback(null, item);\n}\n\nfunction addFunctionOption(options, name) {\n  if (_.isFunction(options[name])) {\n    options[name] = options[name].toString();\n  }\n}\n\nfunction addConfiguredOptions(item, options, callback) {\n  var configuredOptions = options._configuredOptions;\n\n  // These must be stringified or they'll get dropped during serialization.\n  addFunctionOption(configuredOptions, 'transform');\n  addFunctionOption(configuredOptions, 'checkIgnore');\n  addFunctionOption(configuredOptions, 'onSendCallback');\n\n  delete configuredOptions.accessToken;\n  item.data.notifier.configured_options = configuredOptions;\n  callback(null, item);\n}\n\nfunction addDiagnosticKeys(item, options, callback) {\n  var diagnostic = _.merge(\n    item.notifier.client.notifier.diagnostic,\n    item.diagnostic,\n  );\n\n  if (_.get(item, 'err._isAnonymous')) {\n    diagnostic.is_anonymous = true;\n  }\n\n  if (item._isUncaught) {\n    diagnostic.is_uncaught = item._isUncaught;\n  }\n\n  if (item.err) {\n    try {\n      diagnostic.raw_error = {\n        message: item.err.message,\n        name: item.err.name,\n        constructor_name: item.err.constructor && item.err.constructor.name,\n        filename: item.err.fileName,\n        line: item.err.lineNumber,\n        column: item.err.columnNumber,\n        stack: item.err.stack,\n      };\n    } catch (e) {\n      diagnostic.raw_error = { failed: String(e) };\n    }\n  }\n\n  item.data.notifier.diagnostic = _.merge(\n    item.data.notifier.diagnostic,\n    diagnostic,\n  );\n  callback(null, item);\n}\n\nmodule.exports = {\n  itemToPayload: itemToPayload,\n  addPayloadOptions: addPayloadOptions,\n  addTelemetryData: addTelemetryData,\n  addMessageWithError: addMessageWithError,\n  userTransform: userTransform,\n  addConfigToPayload: addConfigToPayload,\n  addConfiguredOptions: addConfiguredOptions,\n  addDiagnosticKeys: addDiagnosticKeys,\n};\n", "'use strict';\n\nvar _ = require('./utility');\nvar traverse = require('./utility/traverse');\n\nfunction raw(payload, jsonBackup) {\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction selectFrames(frames, range) {\n  var len = frames.length;\n  if (len > range * 2) {\n    return frames.slice(0, range).concat(frames.slice(len - range));\n  }\n  return frames;\n}\n\nfunction truncateFrames(payload, jsonBackup, range) {\n  range = typeof range === 'undefined' ? 30 : range;\n  var body = payload.data.body;\n  var frames;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      frames = chain[i].frames;\n      frames = selectFrames(frames, range);\n      chain[i].frames = frames;\n    }\n  } else if (body.trace) {\n    frames = body.trace.frames;\n    frames = selectFrames(frames, range);\n    body.trace.frames = frames;\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction maybeTruncateValue(len, val) {\n  if (!val) {\n    return val;\n  }\n  if (val.length > len) {\n    return val.slice(0, len - 3).concat('...');\n  }\n  return val;\n}\n\nfunction truncateStrings(len, payload, jsonBackup) {\n  function truncator(k, v, seen) {\n    switch (_.typeName(v)) {\n      case 'string':\n        return maybeTruncateValue(len, v);\n      case 'object':\n      case 'array':\n        return traverse(v, truncator, seen);\n      default:\n        return v;\n    }\n  }\n  payload = traverse(payload, truncator);\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction truncateTraceData(traceData) {\n  if (traceData.exception) {\n    delete traceData.exception.description;\n    traceData.exception.message = maybeTruncateValue(\n      255,\n      traceData.exception.message,\n    );\n  }\n  traceData.frames = selectFrames(traceData.frames, 1);\n  return traceData;\n}\n\nfunction minBody(payload, jsonBackup) {\n  var body = payload.data.body;\n  if (body.trace_chain) {\n    var chain = body.trace_chain;\n    for (var i = 0; i < chain.length; i++) {\n      chain[i] = truncateTraceData(chain[i]);\n    }\n  } else if (body.trace) {\n    body.trace = truncateTraceData(body.trace);\n  }\n  return [payload, _.stringify(payload, jsonBackup)];\n}\n\nfunction needsTruncation(payload, maxSize) {\n  return _.maxByteSize(payload) > maxSize;\n}\n\nfunction truncate(payload, jsonBackup, maxSize) {\n  maxSize = typeof maxSize === 'undefined' ? 512 * 1024 : maxSize;\n  var strategies = [\n    raw,\n    truncateFrames,\n    truncateStrings.bind(null, 1024),\n    truncateStrings.bind(null, 512),\n    truncateStrings.bind(null, 256),\n    minBody,\n  ];\n  var strategy, results, result;\n\n  while ((strategy = strategies.shift())) {\n    results = strategy(payload, jsonBackup);\n    payload = results[0];\n    result = results[1];\n    if (result.error || !needsTruncation(result.value, maxSize)) {\n      return result;\n    }\n  }\n  return result;\n}\n\nmodule.exports = {\n  truncate: truncate,\n\n  /* for testing */\n  raw: raw,\n  truncateFrames: truncateFrames,\n  truncateStrings: truncateStrings,\n  maybeTruncateValue: maybeTruncateValue,\n};\n", "'use strict';\n\nvar merge = require('./merge');\n\nvar RollbarJSON = {};\nfunction setupJSON(polyfillJSON) {\n  if (isFunction(RollbarJSON.stringify) && isFunction(RollbarJSON.parse)) {\n    return;\n  }\n\n  if (isDefined(JSON)) {\n    // If polyfill is provided, prefer it over existing non-native shims.\n    if (polyfillJSON) {\n      if (isNativeFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isNativeFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    } else {\n      // else accept any interface that is present.\n      if (isFunction(JSON.stringify)) {\n        RollbarJSON.stringify = JSON.stringify;\n      }\n      if (isFunction(JSON.parse)) {\n        RollbarJSON.parse = JSON.parse;\n      }\n    }\n  }\n  if (!isFunction(RollbarJSON.stringify) || !isFunction(RollbarJSON.parse)) {\n    polyfillJSON && polyfillJSON(RollbarJSON);\n  }\n}\n\n/*\n * isType - Given a Javascript value and a string, returns true if the type of the value matches the\n * given string.\n *\n * @param x - any value\n * @param t - a lowercase string containing one of the following type names:\n *    - undefined\n *    - null\n *    - error\n *    - number\n *    - boolean\n *    - string\n *    - symbol\n *    - function\n *    - object\n *    - array\n * @returns true if x is of type t, otherwise false\n */\nfunction isType(x, t) {\n  return t === typeName(x);\n}\n\n/*\n * typeName - Given a Javascript value, returns the type of the object as a string\n */\nfunction typeName(x) {\n  var name = typeof x;\n  if (name !== 'object') {\n    return name;\n  }\n  if (!x) {\n    return 'null';\n  }\n  if (x instanceof Error) {\n    return 'error';\n  }\n  return {}.toString\n    .call(x)\n    .match(/\\s([a-zA-Z]+)/)[1]\n    .toLowerCase();\n}\n\n/* isFunction - a convenience function for checking if a value is a function\n *\n * @param f - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isFunction(f) {\n  return isType(f, 'function');\n}\n\n/* isNativeFunction - a convenience function for checking if a value is a native JS function\n *\n * @param f - any value\n * @returns true if f is a native JS function, otherwise false\n */\nfunction isNativeFunction(f) {\n  var reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n  var funcMatchString = Function.prototype.toString\n    .call(Object.prototype.hasOwnProperty)\n    .replace(reRegExpChar, '\\\\$&')\n    .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?');\n  var reIsNative = RegExp('^' + funcMatchString + '$');\n  return isObject(f) && reIsNative.test(f);\n}\n\n/* isObject - Checks if the argument is an object\n *\n * @param value - any value\n * @returns true is value is an object function is an object)\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\n/* isString - Checks if the argument is a string\n *\n * @param value - any value\n * @returns true if value is a string\n */\nfunction isString(value) {\n  return typeof value === 'string' || value instanceof String;\n}\n\n/**\n * isFiniteNumber - determines whether the passed value is a finite number\n *\n * @param {*} n - any value\n * @returns true if value is a finite number\n */\nfunction isFiniteNumber(n) {\n  return Number.isFinite(n);\n}\n\n/*\n * isDefined - a convenience function for checking if a value is not equal to undefined\n *\n * @param u - any value\n * @returns true if u is anything other than undefined\n */\nfunction isDefined(u) {\n  return !isType(u, 'undefined');\n}\n\n/*\n * isIterable - convenience function for checking if a value can be iterated, essentially\n * whether it is an object or an array.\n *\n * @param i - any value\n * @returns true if i is an object or an array as determined by `typeName`\n */\nfunction isIterable(i) {\n  var type = typeName(i);\n  return type === 'object' || type === 'array';\n}\n\n/*\n * isError - convenience function for checking if a value is of an error type\n *\n * @param e - any value\n * @returns true if e is an error\n */\nfunction isError(e) {\n  // Detect both Error and Firefox Exception type\n  return isType(e, 'error') || isType(e, 'exception');\n}\n\n/* isPromise - a convenience function for checking if a value is a promise\n *\n * @param p - any value\n * @returns true if f is a function, otherwise false\n */\nfunction isPromise(p) {\n  return isObject(p) && isType(p.then, 'function');\n}\n\nfunction redact() {\n  return '********';\n}\n\n// from http://stackoverflow.com/a/8809472/1138191\nfunction uuid4() {\n  var d = now();\n  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(\n    /[xy]/g,\n    function (c) {\n      var r = (d + Math.random() * 16) % 16 | 0;\n      d = Math.floor(d / 16);\n      return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16);\n    },\n  );\n  return uuid;\n}\n\nvar LEVELS = {\n  debug: 0,\n  info: 1,\n  warning: 2,\n  error: 3,\n  critical: 4,\n};\n\nfunction sanitizeUrl(url) {\n  var baseUrlParts = parseUri(url);\n  if (!baseUrlParts) {\n    return '(unknown)';\n  }\n\n  // remove a trailing # if there is no anchor\n  if (baseUrlParts.anchor === '') {\n    baseUrlParts.source = baseUrlParts.source.replace('#', '');\n  }\n\n  url = baseUrlParts.source.replace('?' + baseUrlParts.query, '');\n  return url;\n}\n\nvar parseUriOptions = {\n  strictMode: false,\n  key: [\n    'source',\n    'protocol',\n    'authority',\n    'userInfo',\n    'user',\n    'password',\n    'host',\n    'port',\n    'relative',\n    'path',\n    'directory',\n    'file',\n    'query',\n    'anchor',\n  ],\n  q: {\n    name: 'queryKey',\n    parser: /(?:^|&)([^&=]*)=?([^&]*)/g,\n  },\n  parser: {\n    strict:\n      /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?))?((((?:[^?#\\/]*\\/)*)([^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n    loose:\n      /^(?:(?![^:@]+:[^:@\\/]*@)([^:\\/?#.]+):)?(?:\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/,\n  },\n};\n\nfunction parseUri(str) {\n  if (!isType(str, 'string')) {\n    return undefined;\n  }\n\n  var o = parseUriOptions;\n  var m = o.parser[o.strictMode ? 'strict' : 'loose'].exec(str);\n  var uri = {};\n\n  for (var i = 0, l = o.key.length; i < l; ++i) {\n    uri[o.key[i]] = m[i] || '';\n  }\n\n  uri[o.q.name] = {};\n  uri[o.key[12]].replace(o.q.parser, function ($0, $1, $2) {\n    if ($1) {\n      uri[o.q.name][$1] = $2;\n    }\n  });\n\n  return uri;\n}\n\nfunction addParamsAndAccessTokenToPath(accessToken, options, params) {\n  params = params || {};\n  params.access_token = accessToken;\n  var paramsArray = [];\n  var k;\n  for (k in params) {\n    if (Object.prototype.hasOwnProperty.call(params, k)) {\n      paramsArray.push([k, params[k]].join('='));\n    }\n  }\n  var query = '?' + paramsArray.sort().join('&');\n\n  options = options || {};\n  options.path = options.path || '';\n  var qs = options.path.indexOf('?');\n  var h = options.path.indexOf('#');\n  var p;\n  if (qs !== -1 && (h === -1 || h > qs)) {\n    p = options.path;\n    options.path = p.substring(0, qs) + query + '&' + p.substring(qs + 1);\n  } else {\n    if (h !== -1) {\n      p = options.path;\n      options.path = p.substring(0, h) + query + p.substring(h);\n    } else {\n      options.path = options.path + query;\n    }\n  }\n}\n\nfunction formatUrl(u, protocol) {\n  protocol = protocol || u.protocol;\n  if (!protocol && u.port) {\n    if (u.port === 80) {\n      protocol = 'http:';\n    } else if (u.port === 443) {\n      protocol = 'https:';\n    }\n  }\n  protocol = protocol || 'https:';\n\n  if (!u.hostname) {\n    return null;\n  }\n  var result = protocol + '//' + u.hostname;\n  if (u.port) {\n    result = result + ':' + u.port;\n  }\n  if (u.path) {\n    result = result + u.path;\n  }\n  return result;\n}\n\nfunction stringify(obj, backup) {\n  var value, error;\n  try {\n    value = RollbarJSON.stringify(obj);\n  } catch (jsonError) {\n    if (backup && isFunction(backup)) {\n      try {\n        value = backup(obj);\n      } catch (backupError) {\n        error = backupError;\n      }\n    } else {\n      error = jsonError;\n    }\n  }\n  return { error: error, value: value };\n}\n\nfunction maxByteSize(string) {\n  // The transport will use utf-8, so assume utf-8 encoding.\n  //\n  // This minimal implementation will accurately count bytes for all UCS-2 and\n  // single code point UTF-16. If presented with multi code point UTF-16,\n  // which should be rare, it will safely overcount, not undercount.\n  //\n  // While robust utf-8 encoders exist, this is far smaller and far more performant.\n  // For quickly counting payload size for truncation, smaller is better.\n\n  var count = 0;\n  var length = string.length;\n\n  for (var i = 0; i < length; i++) {\n    var code = string.charCodeAt(i);\n    if (code < 128) {\n      // up to 7 bits\n      count = count + 1;\n    } else if (code < 2048) {\n      // up to 11 bits\n      count = count + 2;\n    } else if (code < 65536) {\n      // up to 16 bits\n      count = count + 3;\n    }\n  }\n\n  return count;\n}\n\nfunction jsonParse(s) {\n  var value, error;\n  try {\n    value = RollbarJSON.parse(s);\n  } catch (e) {\n    error = e;\n  }\n  return { error: error, value: value };\n}\n\nfunction makeUnhandledStackInfo(\n  message,\n  url,\n  lineno,\n  colno,\n  error,\n  mode,\n  backupMessage,\n  errorParser,\n) {\n  var location = {\n    url: url || '',\n    line: lineno,\n    column: colno,\n  };\n  location.func = errorParser.guessFunctionName(location.url, location.line);\n  location.context = errorParser.gatherContext(location.url, location.line);\n  var href =\n    typeof document !== 'undefined' &&\n    document &&\n    document.location &&\n    document.location.href;\n  var useragent =\n    typeof window !== 'undefined' &&\n    window &&\n    window.navigator &&\n    window.navigator.userAgent;\n  return {\n    mode: mode,\n    message: error ? String(error) : message || backupMessage,\n    url: href,\n    stack: [location],\n    useragent: useragent,\n  };\n}\n\nfunction wrapCallback(logger, f) {\n  return function (err, resp) {\n    try {\n      f(err, resp);\n    } catch (e) {\n      logger.error(e);\n    }\n  };\n}\n\nfunction nonCircularClone(obj) {\n  var seen = [obj];\n\n  function clone(obj, seen) {\n    var value,\n      name,\n      newSeen,\n      result = {};\n\n    try {\n      for (name in obj) {\n        value = obj[name];\n\n        if (value && (isType(value, 'object') || isType(value, 'array'))) {\n          if (seen.includes(value)) {\n            result[name] = 'Removed circular reference: ' + typeName(value);\n          } else {\n            newSeen = seen.slice();\n            newSeen.push(value);\n            result[name] = clone(value, newSeen);\n          }\n          continue;\n        }\n\n        result[name] = value;\n      }\n    } catch (e) {\n      result = 'Failed cloning custom data: ' + e.message;\n    }\n    return result;\n  }\n  return clone(obj, seen);\n}\n\nfunction createItem(args, logger, notifier, requestKeys, lambdaContext) {\n  var message, err, custom, callback, request;\n  var arg;\n  var extraArgs = [];\n  var diagnostic = {};\n  var argTypes = [];\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    argTypes.push(typ);\n    switch (typ) {\n      case 'undefined':\n        break;\n      case 'string':\n        message ? extraArgs.push(arg) : (message = arg);\n        break;\n      case 'function':\n        callback = wrapCallback(logger, arg);\n        break;\n      case 'date':\n        extraArgs.push(arg);\n        break;\n      case 'error':\n      case 'domexception':\n      case 'exception': // Firefox Exception type\n        err ? extraArgs.push(arg) : (err = arg);\n        break;\n      case 'object':\n      case 'array':\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        if (requestKeys && typ === 'object' && !request) {\n          for (var j = 0, len = requestKeys.length; j < len; ++j) {\n            if (arg[requestKeys[j]] !== undefined) {\n              request = arg;\n              break;\n            }\n          }\n          if (request) {\n            break;\n          }\n        }\n        custom ? extraArgs.push(arg) : (custom = arg);\n        break;\n      default:\n        if (\n          arg instanceof Error ||\n          (typeof DOMException !== 'undefined' && arg instanceof DOMException)\n        ) {\n          err ? extraArgs.push(arg) : (err = arg);\n          break;\n        }\n        extraArgs.push(arg);\n    }\n  }\n\n  // if custom is an array this turns it into an object with integer keys\n  if (custom) custom = nonCircularClone(custom);\n\n  if (extraArgs.length > 0) {\n    if (!custom) custom = nonCircularClone({});\n    custom.extraArgs = nonCircularClone(extraArgs);\n  }\n\n  var item = {\n    message: message,\n    err: err,\n    custom: custom,\n    timestamp: now(),\n    callback: callback,\n    notifier: notifier,\n    diagnostic: diagnostic,\n    uuid: uuid4(),\n  };\n\n  setCustomItemKeys(item, custom);\n\n  if (requestKeys && request) {\n    item.request = request;\n  }\n  if (lambdaContext) {\n    item.lambdaContext = lambdaContext;\n  }\n  item._originalArgs = args;\n  item.diagnostic.original_arg_types = argTypes;\n  return item;\n}\n\nfunction setCustomItemKeys(item, custom) {\n  if (custom && custom.level !== undefined) {\n    item.level = custom.level;\n    delete custom.level;\n  }\n  if (custom && custom.skipFrames !== undefined) {\n    item.skipFrames = custom.skipFrames;\n    delete custom.skipFrames;\n  }\n}\n\nfunction addErrorContext(item, errors) {\n  var custom = item.data.custom || {};\n  var contextAdded = false;\n\n  try {\n    for (var i = 0; i < errors.length; ++i) {\n      if (errors[i].hasOwnProperty('rollbarContext')) {\n        custom = merge(custom, nonCircularClone(errors[i].rollbarContext));\n        contextAdded = true;\n      }\n    }\n\n    // Avoid adding an empty object to the data.\n    if (contextAdded) {\n      item.data.custom = custom;\n    }\n  } catch (e) {\n    item.diagnostic.error_context = 'Failed: ' + e.message;\n  }\n}\n\nvar TELEMETRY_TYPES = [\n  'log',\n  'network',\n  'dom',\n  'navigation',\n  'error',\n  'manual',\n];\nvar TELEMETRY_LEVELS = ['critical', 'error', 'warning', 'info', 'debug'];\n\nfunction arrayIncludes(arr, val) {\n  for (var k = 0; k < arr.length; ++k) {\n    if (arr[k] === val) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction createTelemetryEvent(args) {\n  var type, metadata, level;\n  var arg;\n\n  for (var i = 0, l = args.length; i < l; ++i) {\n    arg = args[i];\n\n    var typ = typeName(arg);\n    switch (typ) {\n      case 'string':\n        if (!type && arrayIncludes(TELEMETRY_TYPES, arg)) {\n          type = arg;\n        } else if (!level && arrayIncludes(TELEMETRY_LEVELS, arg)) {\n          level = arg;\n        }\n        break;\n      case 'object':\n        metadata = arg;\n        break;\n      default:\n        break;\n    }\n  }\n  var event = {\n    type: type || 'manual',\n    metadata: metadata || {},\n    level: level,\n  };\n\n  return event;\n}\n\n/*\n * get - given an obj/array and a keypath, return the value at that keypath or\n *       undefined if not possible.\n *\n * @param obj - an object or array\n * @param path - a string of keys separated by '.' such as 'plugin.jquery.0.message'\n *    which would correspond to 42 in `{plugin: {jquery: [{message: 42}]}}`\n */\nfunction get(obj, path) {\n  if (!obj) {\n    return undefined;\n  }\n  var keys = path.split('.');\n  var result = obj;\n  try {\n    for (var i = 0, len = keys.length; i < len; ++i) {\n      result = result[keys[i]];\n    }\n  } catch (e) {\n    result = undefined;\n  }\n  return result;\n}\n\nfunction set(obj, path, value) {\n  if (!obj) {\n    return;\n  }\n  var keys = path.split('.');\n  var len = keys.length;\n  if (len < 1) {\n    return;\n  }\n  if (len === 1) {\n    obj[keys[0]] = value;\n    return;\n  }\n  try {\n    var temp = obj[keys[0]] || {};\n    var replacement = temp;\n    for (var i = 1; i < len - 1; ++i) {\n      temp[keys[i]] = temp[keys[i]] || {};\n      temp = temp[keys[i]];\n    }\n    temp[keys[len - 1]] = value;\n    obj[keys[0]] = replacement;\n  } catch (e) {\n    return;\n  }\n}\n\nfunction formatArgsAsString(args) {\n  var i, len, arg;\n  var result = [];\n  for (i = 0, len = args.length; i < len; ++i) {\n    arg = args[i];\n    switch (typeName(arg)) {\n      case 'object':\n        arg = stringify(arg);\n        arg = arg.error || arg.value;\n        if (arg.length > 500) {\n          arg = arg.substr(0, 497) + '...';\n        }\n        break;\n      case 'null':\n        arg = 'null';\n        break;\n      case 'undefined':\n        arg = 'undefined';\n        break;\n      case 'symbol':\n        arg = arg.toString();\n        break;\n    }\n    result.push(arg);\n  }\n  return result.join(' ');\n}\n\nfunction now() {\n  if (Date.now) {\n    return +Date.now();\n  }\n  return +new Date();\n}\n\nfunction filterIp(requestData, captureIp) {\n  if (!requestData || !requestData['user_ip'] || captureIp === true) {\n    return;\n  }\n  var newIp = requestData['user_ip'];\n  if (!captureIp) {\n    newIp = null;\n  } else {\n    try {\n      var parts;\n      if (newIp.indexOf('.') !== -1) {\n        parts = newIp.split('.');\n        parts.pop();\n        parts.push('0');\n        newIp = parts.join('.');\n      } else if (newIp.indexOf(':') !== -1) {\n        parts = newIp.split(':');\n        if (parts.length > 2) {\n          var beginning = parts.slice(0, 3);\n          var slashIdx = beginning[2].indexOf('/');\n          if (slashIdx !== -1) {\n            beginning[2] = beginning[2].substring(0, slashIdx);\n          }\n          var terminal = '0000:0000:0000:0000:0000';\n          newIp = beginning.concat(terminal).join(':');\n        }\n      } else {\n        newIp = null;\n      }\n    } catch (e) {\n      newIp = null;\n    }\n  }\n  requestData['user_ip'] = newIp;\n}\n\nfunction handleOptions(current, input, payload, logger) {\n  var result = merge(current, input, payload);\n  result = updateDeprecatedOptions(result, logger);\n  if (!input || input.overwriteScrubFields) {\n    return result;\n  }\n  if (input.scrubFields) {\n    result.scrubFields = (current.scrubFields || []).concat(input.scrubFields);\n  }\n  return result;\n}\n\nfunction updateDeprecatedOptions(options, logger) {\n  if (options.hostWhiteList && !options.hostSafeList) {\n    options.hostSafeList = options.hostWhiteList;\n    options.hostWhiteList = undefined;\n    logger && logger.log('hostWhiteList is deprecated. Use hostSafeList.');\n  }\n  if (options.hostBlackList && !options.hostBlockList) {\n    options.hostBlockList = options.hostBlackList;\n    options.hostBlackList = undefined;\n    logger && logger.log('hostBlackList is deprecated. Use hostBlockList.');\n  }\n  return options;\n}\n\nmodule.exports = {\n  addParamsAndAccessTokenToPath: addParamsAndAccessTokenToPath,\n  createItem: createItem,\n  addErrorContext: addErrorContext,\n  createTelemetryEvent: createTelemetryEvent,\n  filterIp: filterIp,\n  formatArgsAsString: formatArgsAsString,\n  formatUrl: formatUrl,\n  get: get,\n  handleOptions: handleOptions,\n  isError: isError,\n  isFiniteNumber: isFiniteNumber,\n  isFunction: isFunction,\n  isIterable: isIterable,\n  isNativeFunction: isNativeFunction,\n  isObject: isObject,\n  isString: isString,\n  isType: isType,\n  isPromise: isPromise,\n  jsonParse: jsonParse,\n  LEVELS: LEVELS,\n  makeUnhandledStackInfo: makeUnhandledStackInfo,\n  merge: merge,\n  now: now,\n  redact: redact,\n  RollbarJSON: RollbarJSON,\n  sanitizeUrl: sanitizeUrl,\n  set: set,\n  setupJSON: setupJSON,\n  stringify: stringify,\n  maxByteSize: maxByteSize,\n  typeName: typeName,\n  uuid4: uuid4,\n};\n", "'use strict';\n\n/*\n * headers - Detect when fetch Headers are undefined and use a partial polyfill.\n *\n * A full polyfill is not used in order to keep package size as small as possible.\n * Since this is only used internally and is not added to the window object,\n * the full interface doesn't need to be supported.\n *\n * This implementation is modified from whatwg-fetch:\n * https://github.com/github/fetch\n */\nfunction headers(headers) {\n  if (typeof Headers === 'undefined') {\n    return new FetchHeaders(headers);\n  }\n\n  return new Headers(headers);\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name);\n  }\n  return name.toLowerCase();\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value);\n  }\n  return value;\n}\n\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function () {\n      var value = items.shift();\n      return { done: value === undefined, value: value };\n    },\n  };\n\n  return iterator;\n}\n\nfunction FetchHeaders(headers) {\n  this.map = {};\n\n  if (headers instanceof FetchHeaders) {\n    headers.forEach(function (value, name) {\n      this.append(name, value);\n    }, this);\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function (header) {\n      this.append(header[0], header[1]);\n    }, this);\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function (name) {\n      this.append(name, headers[name]);\n    }, this);\n  }\n}\n\nFetchHeaders.prototype.append = function (name, value) {\n  name = normalizeName(name);\n  value = normalizeValue(value);\n  var oldValue = this.map[name];\n  this.map[name] = oldValue ? oldValue + ', ' + value : value;\n};\n\nFetchHeaders.prototype.get = function (name) {\n  name = normalizeName(name);\n  return this.has(name) ? this.map[name] : null;\n};\n\nFetchHeaders.prototype.has = function (name) {\n  return this.map.hasOwnProperty(normalizeName(name));\n};\n\nFetchHeaders.prototype.forEach = function (callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this);\n    }\n  }\n};\n\nFetchHeaders.prototype.entries = function () {\n  var items = [];\n  this.forEach(function (value, name) {\n    items.push([name, value]);\n  });\n  return iteratorFor(items);\n};\n\nmodule.exports = headers;\n", "'use strict';\n\nvar polyfillJSON = require('../../vendor/JSON-js/json3');\n\nmodule.exports = polyfillJSON;\n", "'use strict';\n\nfunction replace(obj, name, replacement, replacements, type) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (replacements) {\n    replacements[type].push([obj, name, orig]);\n  }\n}\n\nmodule.exports = replace;\n", "'use strict';\n\nvar _ = require('../utility');\n\nfunction traverse(obj, func, seen) {\n  var k, v, i;\n  var isObj = _.isType(obj, 'object');\n  var isArray = _.isType(obj, 'array');\n  var keys = [];\n  var seenIndex;\n\n  // Best might be to use Map here with `obj` as the keys, but we want to support IE < 11.\n  seen = seen || { obj: [], mapped: [] };\n\n  if (isObj) {\n    seenIndex = seen.obj.indexOf(obj);\n\n    if (isObj && seenIndex !== -1) {\n      // Prefer the mapped object if there is one.\n      return seen.mapped[seenIndex] || seen.obj[seenIndex];\n    }\n\n    seen.obj.push(obj);\n    seenIndex = seen.obj.length - 1;\n  }\n\n  if (isObj) {\n    for (k in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, k)) {\n        keys.push(k);\n      }\n    }\n  } else if (isArray) {\n    for (i = 0; i < obj.length; ++i) {\n      keys.push(i);\n    }\n  }\n\n  var result = isObj ? {} : [];\n  var same = true;\n  for (i = 0; i < keys.length; ++i) {\n    k = keys[i];\n    v = obj[k];\n    result[k] = func(k, v, seen);\n    same = same && result[k] === obj[k];\n  }\n\n  if (isObj && !same) {\n    seen.mapped[seenIndex] = result;\n  }\n\n  return !same ? result : obj;\n}\n\nmodule.exports = traverse;\n", "//  json3.js\n//  2017-02-21\n//  Public Domain.\n//  NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n//  See http://www.JSON.org/js.html\n//  This code should be minified before deployment.\n//  See http://javascript.crockford.com/jsmin.html\n\n//  USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n//  NOT CONTROL.\n\n//  This file creates a global JSON object containing two methods: stringify\n//  and parse. This file provides the ES5 JSON capability to ES3 systems.\n//  If a project might run on IE8 or earlier, then this file should be included.\n//  This file does nothing on ES5 systems.\n\n//      JSON.stringify(value, replacer, space)\n//          value       any JavaScript value, usually an object or array.\n//          replacer    an optional parameter that determines how object\n//                      values are stringified for objects. It can be a\n//                      function or an array of strings.\n//          space       an optional parameter that specifies the indentation\n//                      of nested structures. If it is omitted, the text will\n//                      be packed without extra whitespace. If it is a number,\n//                      it will specify the number of spaces to indent at each\n//                      level. If it is a string (such as \"\\t\" or \"&nbsp;\"),\n//                      it contains the characters used to indent at each level.\n//          This method produces a JSON text from a JavaScript value.\n//          When an object value is found, if the object contains a toJSON\n//          method, its toJSON method will be called and the result will be\n//          stringified. A toJSON method does not serialize: it returns the\n//          value represented by the name/value pair that should be serialized,\n//          or undefined if nothing should be serialized. The toJSON method\n//          will be passed the key associated with the value, and this will be\n//          bound to the value.\n\n//          For example, this would serialize Dates as ISO strings.\n\n//              Date.prototype.toJSON = function (key) {\n//                  function f(n) {\n//                      // Format integers to have at least two digits.\n//                      return (n < 10)\n//                          ? \"0\" + n\n//                          : n;\n//                  }\n//                  return this.getUTCFullYear()   + \"-\" +\n//                       f(this.getUTCMonth() + 1) + \"-\" +\n//                       f(this.getUTCDate())      + \"T\" +\n//                       f(this.getUTCHours())     + \":\" +\n//                       f(this.getUTCMinutes())   + \":\" +\n//                       f(this.getUTCSeconds())   + \"Z\";\n//              };\n\n//          You can provide an optional replacer method. It will be passed the\n//          key and value of each member, with this bound to the containing\n//          object. The value that is returned from your method will be\n//          serialized. If your method returns undefined, then the member will\n//          be excluded from the serialization.\n\n//          If the replacer parameter is an array of strings, then it will be\n//          used to select the members to be serialized. It filters the results\n//          such that only members with keys listed in the replacer array are\n//          stringified.\n\n//          Values that do not have JSON representations, such as undefined or\n//          functions, will not be serialized. Such values in objects will be\n//          dropped; in arrays they will be replaced with null. You can use\n//          a replacer function to replace those with JSON values.\n\n//          JSON.stringify(undefined) returns undefined.\n\n//          The optional space parameter produces a stringification of the\n//          value that is filled with line breaks and indentation to make it\n//          easier to read.\n\n//          If the space parameter is a non-empty string, then that string will\n//          be used for indentation. If the space parameter is a number, then\n//          the indentation will be that many spaces.\n\n//          Example:\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}]);\n//          // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n//          text = JSON.stringify([\"e\", {pluribus: \"unum\"}], null, \"\\t\");\n//          // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n//          text = JSON.stringify([new Date()], function (key, value) {\n//              return this[key] instanceof Date\n//                  ? \"Date(\" + this[key] + \")\"\n//                  : value;\n//          });\n//          // text is '[\"Date(---current time---)\"]'\n\n//      JSON.parse(text, reviver)\n//          This method parses a JSON text to produce an object or array.\n//          It can throw a SyntaxError exception.\n//          This has been modified to use JSON-js/json_parse_state.js as the\n//          parser instead of the one built around eval found in JSON-js/json2.js\n\n//          The optional reviver parameter is a function that can filter and\n//          transform the results. It receives each of the keys and values,\n//          and its return value is used instead of the original value.\n//          If it returns what it received, then the structure is not modified.\n//          If it returns undefined then the member is deleted.\n\n//          Example:\n\n//          // Parse the text. Values that look like ISO date strings will\n//          // be converted to Date objects.\n\n//          myData = JSON.parse(text, function (key, value) {\n//              var a;\n//              if (typeof value === \"string\") {\n//                  a =\n//   /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n//                  if (a) {\n//                      return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n//                          +a[5], +a[6]));\n//                  }\n//              }\n//              return value;\n//          });\n\n//          myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n//              var d;\n//              if (typeof value === \"string\" &&\n//                      value.slice(0, 5) === \"Date(\" &&\n//                      value.slice(-1) === \")\") {\n//                  d = new Date(value.slice(5, -1));\n//                  if (d) {\n//                      return d;\n//                  }\n//              }\n//              return value;\n//          });\n\n//  This is a reference implementation. You are free to copy, modify, or\n//  redistribute.\n\n/*jslint\n  for, this\n  */\n\n/*property\n  JSON, apply, call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n  getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n  lastIndex, length, parse, prototype, push, replace, slice, stringify,\n  test, toJSON, toString, valueOf\n  */\n\nvar setupCustomJSON = function(JSON) {\n\n  var rx_one = /^[\\],:{}\\s]*$/;\n  var rx_two = /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9a-fA-F]{4})/g;\n  var rx_three = /\"[^\"\\\\\\n\\r]*\"|true|false|null|-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\n  var rx_four = /(?:^|:|,)(?:\\s*\\[)+/g;\n  var rx_escapable = /[\\\\\"\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n  var rx_dangerous = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g;\n\n  function f(n) {\n    // Format integers to have at least two digits.\n    return n < 10\n      ? \"0\" + n\n      : n;\n  }\n\n  function this_value() {\n    return this.valueOf();\n  }\n\n  if (typeof Date.prototype.toJSON !== \"function\") {\n\n    Date.prototype.toJSON = function () {\n\n      return isFinite(this.valueOf())\n        ? this.getUTCFullYear() + \"-\" +\n        f(this.getUTCMonth() + 1) + \"-\" +\n        f(this.getUTCDate()) + \"T\" +\n        f(this.getUTCHours()) + \":\" +\n        f(this.getUTCMinutes()) + \":\" +\n        f(this.getUTCSeconds()) + \"Z\"\n        : null;\n    };\n\n    Boolean.prototype.toJSON = this_value;\n    Number.prototype.toJSON = this_value;\n    String.prototype.toJSON = this_value;\n  }\n\n  var gap;\n  var indent;\n  var meta;\n  var rep;\n\n\n  function quote(string) {\n\n    // If the string contains no control characters, no quote characters, and no\n    // backslash characters, then we can safely slap some quotes around it.\n    // Otherwise we must also replace the offending characters with safe escape\n    // sequences.\n\n    rx_escapable.lastIndex = 0;\n    return rx_escapable.test(string)\n      ? \"\\\"\" + string.replace(rx_escapable, function (a) {\n        var c = meta[a];\n        return typeof c === \"string\"\n          ? c\n          : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n      }) + \"\\\"\"\n    : \"\\\"\" + string + \"\\\"\";\n  }\n\n\n  function str(key, holder) {\n\n    // Produce a string from holder[key].\n\n    var i;          // The loop counter.\n    var k;          // The member key.\n    var v;          // The member value.\n    var length;\n    var mind = gap;\n    var partial;\n    var value = holder[key];\n\n    // If the value has a toJSON method, call it to obtain a replacement value.\n\n    if (value && typeof value === \"object\" &&\n        typeof value.toJSON === \"function\") {\n      value = value.toJSON(key);\n    }\n\n    // If we were called with a replacer function, then call the replacer to\n    // obtain a replacement value.\n\n    if (typeof rep === \"function\") {\n      value = rep.call(holder, key, value);\n    }\n\n    // What happens next depends on the value's type.\n\n    switch (typeof value) {\n      case \"string\":\n        return quote(value);\n\n      case \"number\":\n\n        // JSON numbers must be finite. Encode non-finite numbers as null.\n\n        return isFinite(value)\n          ? String(value)\n          : \"null\";\n\n      case \"boolean\":\n      case \"null\":\n\n        // If the value is a boolean or null, convert it to a string. Note:\n        // typeof null does not produce \"null\". The case is included here in\n        // the remote chance that this gets fixed someday.\n\n        return String(value);\n\n        // If the type is \"object\", we might be dealing with an object or an array or\n        // null.\n\n      case \"object\":\n\n        // Due to a specification blunder in ECMAScript, typeof null is \"object\",\n        // so watch out for that case.\n\n        if (!value) {\n          return \"null\";\n        }\n\n        // Make an array to hold the partial results of stringifying this object value.\n\n        gap += indent;\n        partial = [];\n\n        // Is the value an array?\n\n        if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n\n          // The value is an array. Stringify every element. Use null as a placeholder\n          // for non-JSON values.\n\n          length = value.length;\n          for (i = 0; i < length; i += 1) {\n            partial[i] = str(i, value) || \"null\";\n          }\n\n          // Join all of the elements together, separated with commas, and wrap them in\n          // brackets.\n\n          v = partial.length === 0\n            ? \"[]\"\n            : gap\n            ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\"\n            : \"[\" + partial.join(\",\") + \"]\";\n          gap = mind;\n          return v;\n        }\n\n        // If the replacer is an array, use it to select the members to be stringified.\n\n        if (rep && typeof rep === \"object\") {\n          length = rep.length;\n          for (i = 0; i < length; i += 1) {\n            if (typeof rep[i] === \"string\") {\n              k = rep[i];\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        } else {\n\n          // Otherwise, iterate through all of the keys in the object.\n\n          for (k in value) {\n            if (Object.prototype.hasOwnProperty.call(value, k)) {\n              v = str(k, value);\n              if (v) {\n                partial.push(quote(k) + (\n                      gap\n                      ? \": \"\n                      : \":\"\n                      ) + v);\n              }\n            }\n          }\n        }\n\n        // Join all of the member texts together, separated with commas,\n        // and wrap them in braces.\n\n        v = partial.length === 0\n          ? \"{}\"\n          : gap\n          ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\"\n          : \"{\" + partial.join(\",\") + \"}\";\n        gap = mind;\n        return v;\n    }\n  }\n\n  // If the JSON object does not yet have a stringify method, give it one.\n\n  if (typeof JSON.stringify !== \"function\") {\n    meta = {    // table of character substitutions\n      \"\\b\": \"\\\\b\",\n      \"\\t\": \"\\\\t\",\n      \"\\n\": \"\\\\n\",\n      \"\\f\": \"\\\\f\",\n      \"\\r\": \"\\\\r\",\n      \"\\\"\": \"\\\\\\\"\",\n      \"\\\\\": \"\\\\\\\\\"\n    };\n    JSON.stringify = function (value, replacer, space) {\n\n      // The stringify method takes a value and an optional replacer, and an optional\n      // space parameter, and returns a JSON text. The replacer can be a function\n      // that can replace values, or an array of strings that will select the keys.\n      // A default replacer method can be provided. Use of the space parameter can\n      // produce text that is more easily readable.\n\n      var i;\n      gap = \"\";\n      indent = \"\";\n\n      // If the space parameter is a number, make an indent string containing that\n      // many spaces.\n\n      if (typeof space === \"number\") {\n        for (i = 0; i < space; i += 1) {\n          indent += \" \";\n        }\n\n        // If the space parameter is a string, it will be used as the indent string.\n\n      } else if (typeof space === \"string\") {\n        indent = space;\n      }\n\n      // If there is a replacer, it must be a function or an array.\n      // Otherwise, throw an error.\n\n      rep = replacer;\n      if (replacer && typeof replacer !== \"function\" &&\n          (typeof replacer !== \"object\" ||\n           typeof replacer.length !== \"number\")) {\n        throw new Error(\"JSON.stringify\");\n      }\n\n      // Make a fake root object containing our value under the key of \"\".\n      // Return the result of stringifying the value.\n\n      return str(\"\", {\"\": value});\n    };\n  }\n\n\n  // If the JSON object does not yet have a parse method, give it one.\n\n  if (typeof JSON.parse !== \"function\") {\n    JSON.parse = (function () {\n\n      // This function creates a JSON parse function that uses a state machine rather\n      // than the dangerous eval function to parse a JSON text.\n\n      var state;      // The state of the parser, one of\n      // 'go'         The starting state\n      // 'ok'         The final, accepting state\n      // 'firstokey'  Ready for the first key of the object or\n      //              the closing of an empty object\n      // 'okey'       Ready for the next key of the object\n      // 'colon'      Ready for the colon\n      // 'ovalue'     Ready for the value half of a key/value pair\n      // 'ocomma'     Ready for a comma or closing }\n      // 'firstavalue' Ready for the first value of an array or\n      //              an empty array\n      // 'avalue'     Ready for the next value of an array\n      // 'acomma'     Ready for a comma or closing ]\n      var stack;      // The stack, for controlling nesting.\n      var container;  // The current container object or array\n      var key;        // The current key\n      var value;      // The current value\n      var escapes = { // Escapement translation table\n        \"\\\\\": \"\\\\\",\n        \"\\\"\": \"\\\"\",\n        \"/\": \"/\",\n        \"t\": \"\\t\",\n        \"n\": \"\\n\",\n        \"r\": \"\\r\",\n        \"f\": \"\\f\",\n        \"b\": \"\\b\"\n      };\n      var string = {   // The actions for string tokens\n        go: function () {\n          state = \"ok\";\n        },\n        firstokey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        okey: function () {\n          key = value;\n          state = \"colon\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var number = {   // The actions for number tokens\n        go: function () {\n          state = \"ok\";\n        },\n        ovalue: function () {\n          state = \"ocomma\";\n        },\n        firstavalue: function () {\n          state = \"acomma\";\n        },\n        avalue: function () {\n          state = \"acomma\";\n        }\n      };\n      var action = {\n\n        // The action table describes the behavior of the machine. It contains an\n        // object for each token. Each object contains a method that is called when\n        // a token is matched in a state. An object will lack a method for illegal\n        // states.\n\n        \"{\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = {};\n            state = \"firstokey\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = {};\n            state = \"firstokey\";\n          }\n        },\n        \"}\": {\n          firstokey: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          ocomma: function () {\n            var pop = stack.pop();\n            container[key] = value;\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \"[\": {\n          go: function () {\n            stack.push({state: \"ok\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          ovalue: function () {\n            stack.push({container: container, state: \"ocomma\", key: key});\n            container = [];\n            state = \"firstavalue\";\n          },\n          firstavalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          },\n          avalue: function () {\n            stack.push({container: container, state: \"acomma\"});\n            container = [];\n            state = \"firstavalue\";\n          }\n        },\n        \"]\": {\n          firstavalue: function () {\n            var pop = stack.pop();\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          },\n          acomma: function () {\n            var pop = stack.pop();\n            container.push(value);\n            value = container;\n            container = pop.container;\n            key = pop.key;\n            state = pop.state;\n          }\n        },\n        \":\": {\n          colon: function () {\n            if (Object.hasOwnProperty.call(container, key)) {\n              throw new SyntaxError(\"Duplicate key '\" + key + \"\\\"\");\n            }\n            state = \"ovalue\";\n          }\n        },\n        \",\": {\n          ocomma: function () {\n            container[key] = value;\n            state = \"okey\";\n          },\n          acomma: function () {\n            container.push(value);\n            state = \"avalue\";\n          }\n        },\n        \"true\": {\n          go: function () {\n            value = true;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = true;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = true;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = true;\n            state = \"acomma\";\n          }\n        },\n        \"false\": {\n          go: function () {\n            value = false;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = false;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = false;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = false;\n            state = \"acomma\";\n          }\n        },\n        \"null\": {\n          go: function () {\n            value = null;\n            state = \"ok\";\n          },\n          ovalue: function () {\n            value = null;\n            state = \"ocomma\";\n          },\n          firstavalue: function () {\n            value = null;\n            state = \"acomma\";\n          },\n          avalue: function () {\n            value = null;\n            state = \"acomma\";\n          }\n        }\n      };\n\n      function debackslashify(text) {\n\n        // Remove and replace any backslash escapement.\n\n        return text.replace(/\\\\(?:u(.{4})|([^u]))/g, function (ignore, b, c) {\n          return b\n            ? String.fromCharCode(parseInt(b, 16))\n            : escapes[c];\n        });\n      }\n\n      return function (source, reviver) {\n\n        // A regular expression is used to extract tokens from the JSON text.\n        // The extraction process is cautious.\n\n        var result;\n        var tx = /^[\\u0020\\t\\n\\r]*(?:([,:\\[\\]{}]|true|false|null)|(-?\\d+(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?)|\"((?:[^\\r\\n\\t\\\\\\\"]|\\\\(?:[\"\\\\\\/trnfb]|u[0-9a-fA-F]{4}))*)\")/;\n\n        // Set the starting state.\n\n        state = \"go\";\n\n        // The stack records the container, key, and state for each object or array\n        // that contains another object or array while processing nested structures.\n\n        stack = [];\n\n        // If any error occurs, we will catch it and ultimately throw a syntax error.\n\n        try {\n\n          // For each token...\n\n          while (true) {\n            result = tx.exec(source);\n            if (!result) {\n              break;\n            }\n\n            // result is the result array from matching the tokenizing regular expression.\n            //  result[0] contains everything that matched, including any initial whitespace.\n            //  result[1] contains any punctuation that was matched, or true, false, or null.\n            //  result[2] contains a matched number, still in string form.\n            //  result[3] contains a matched string, without quotes but with escapement.\n\n            if (result[1]) {\n\n              // Token: Execute the action for this state and token.\n\n              action[result[1]][state]();\n\n            } else if (result[2]) {\n\n              // Number token: Convert the number string into a number value and execute\n              // the action for this state and number.\n\n              value = +result[2];\n              number[state]();\n            } else {\n\n              // String token: Replace the escapement sequences and execute the action for\n              // this state and string.\n\n              value = debackslashify(result[3]);\n              string[state]();\n            }\n\n            // Remove the token from the string. The loop will continue as long as there\n            // are tokens. This is a slow process, but it allows the use of ^ matching,\n            // which assures that no illegal tokens slip through.\n\n            source = source.slice(result[0].length);\n          }\n\n          // If we find a state/token combination that is illegal, then the action will\n          // cause an error. We handle the error by simply changing the state.\n\n        } catch (e) {\n          state = e;\n        }\n\n        // The parsing is finished. If we are not in the final \"ok\" state, or if the\n        // remaining source contains anything except whitespace, then we did not have\n        //a well-formed JSON text.\n\n        if (state !== \"ok\" || (/[^\\u0020\\t\\n\\r]/.test(source))) {\n          throw (state instanceof SyntaxError)\n            ? state\n            : new SyntaxError(\"JSON\");\n        }\n\n        // If there is a reviver function, we recursively walk the new structure,\n        // passing each name/value pair to the reviver function for possible\n        // transformation, starting with a temporary root object that holds the current\n        // value in an empty key. If there is not a reviver function, we simply return\n        // that value.\n\n        return (typeof reviver === \"function\")\n          ? (function walk(holder, key) {\n            var k;\n            var v;\n            var val = holder[key];\n            if (val && typeof val === \"object\") {\n              for (k in value) {\n                if (Object.prototype.hasOwnProperty.call(val, k)) {\n                  v = walk(val, k);\n                  if (v !== undefined) {\n                    val[k] = v;\n                  } else {\n                    delete val[k];\n                  }\n                }\n              }\n            }\n            return reviver.call(holder, key, val);\n          }({\"\": value}, \"\"))\n        : value;\n      };\n    }());\n  }\n}\n\nmodule.exports = setupCustomJSON;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(409);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "global", "console", "prop", "method", "con", "dummy", "properties", "methods", "split", "pop", "window", "StackFrame", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "Error", "extractLocation", "urlLike", "indexOf", "parts", "exec", "replace", "undefined", "filter", "line", "map", "sanitizedLine", "location", "tokens", "slice", "locationParts", "functionName", "join", "fileName", "lineNumber", "columnNumber", "source", "functionNameRegex", "matches", "e", "message", "length", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "i", "len", "push", "argsRaw", "functionCall", "shift", "args", "_isNumber", "n", "isNaN", "parseFloat", "isFinite", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "p", "booleanProps", "numericProps", "stringProps", "arrayProps", "objectProps", "props", "concat", "obj", "prototype", "getArgs", "set<PERSON>rgs", "v", "Object", "toString", "call", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "getLineNumber", "getColumnNumber", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "argsEndIndex", "lastIndexOf", "locationString", "Boolean", "j", "Number", "k", "String", "_", "helpers", "defaultOptions", "hostname", "path", "search", "version", "protocol", "port", "Api", "options", "transport", "urllib", "truncation", "jsonBackup", "url", "accessToken", "transportOptions", "_getTransport", "getTransportFromOptions", "postItem", "data", "callback", "payload", "buildPayload", "self", "setTimeout", "post", "buildJsonPayload", "stringifyResult", "truncate", "stringify", "value", "postJsonPayload", "jsonPayload", "configure", "oldOptions", "merge", "isType", "context", "contextResult", "substr", "access_token", "defaults", "timeout", "gWindow", "defaultTransport", "fetch", "XMLHttpRequest", "detectTransport", "proxy", "endpoint", "opts", "pathname", "transportAPI", "host", "appendPathToPath", "base", "baseTrailingSlash", "test", "pathBeginningSlash", "rollbar", "_rollbarConfig", "alias", "globalAlias", "shim<PERSON>unning", "shimId", "_rollbarStartTime", "Date", "getTime", "<PERSON><PERSON>", "_rollbarDidLoad", "Client", "API", "logger", "globals", "Transport", "transforms", "sharedTransforms", "predicates", "sharedPredicates", "error<PERSON><PERSON>er", "client", "handleOptions", "_configuredOptions", "Telemeter", "components", "telemeter", "Instrumenter", "instrumenter", "polyfillJSON", "wrapGlobals", "scrub", "api", "_gWindow", "gDocument", "document", "isChrome", "chrome", "runtime", "anonymousErrorsPending", "notifier", "addTransform", "handleDomException", "handleItemWithError", "ensureItemHasSomethingToSay", "addBaseInfo", "addRequestInfo", "addClientInfo", "addPluginInfo", "addBody", "addMessageWithError", "addTelemetryData", "addConfigToPayload", "addScrubber", "addPayloadOptions", "userTransform", "addConfiguredOptions", "addDiagnosticKeys", "itemToPayload", "addTransformsToNotifier", "queue", "addPredicate", "checkLevel", "checkIgnore", "userCheckIgnore", "urlIsNotBlockListed", "urlIsSafeListed", "messageIsIgnored", "setupUnhandledCapture", "instrument", "setupJSON", "_instance", "handleUninitialized", "maybe<PERSON><PERSON><PERSON>", "_getFirstFunction", "isFunction", "init", "setComponents", "payloadData", "lastError", "log", "item", "_createItem", "arguments", "uuid", "apply", "debug", "info", "warn", "warning", "critical", "sendJsonPayload", "unhandledExceptionsInitialized", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUncaughtExceptions", "captureUncaughtExceptions", "wrapGlobalEventHandlers", "unhandledRejectionsInitialized", "captureUnhandledRejections", "handleUnhandledRejections", "handleUncaughtException", "lineno", "colno", "inspectAnonymousErrors", "stackInfo", "makeUnhandledStackInfo", "isError", "_unhandledStackInfo", "level", "uncaughtErrorLevel", "_is<PERSON><PERSON><PERSON>t", "handleAnonymousErrors", "r", "prepareStackTrace", "_stack", "_isAnonymous", "handleUnhandledRejection", "reason", "promise", "reasonResult", "_rollbarContext", "_originalArgs", "wrap", "f", "_before", "ctxFn", "_isWrap", "_rollbar_wrapped", "exc", "_rollbarWrappedError", "_wrappedSource", "hasOwnProperty", "captureEvent", "event", "createTelemetryEvent", "type", "metadata", "captureDomContentLoaded", "ts", "captureLoad", "loadFull", "createItem", "scrubFields", "logLevel", "reportLevel", "verbose", "enabled", "transmit", "sendConfig", "includeItemsInTelemetry", "captureIp", "ignoreDuplicateErrors", "Detection", "ieVersion", "undef", "div", "createElement", "all", "getElementsByTagName", "innerHTML", "getElementType", "getAttribute", "toLowerCase", "descriptionToString", "desc", "tagName", "out", "id", "classes", "attributes", "key", "describeElement", "elem", "className", "attr", "elementArrayToString", "a", "nextStr", "totalLength", "separator", "MAX_LENGTH", "unshift", "treeToArray", "nextDescription", "height", "parentNode", "getElementFromEvent", "evt", "doc", "target", "elementFromPoint", "clientX", "clientY", "isDescribedElement", "element", "subtypes", "handler", "shim", "oldOnError", "_rollbarOldOnError", "onerror", "fn", "Array", "old", "ret", "_rollbarWindowOnError", "_rollbarURH", "belongs<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "detail", "addEventListener", "detection", "formatArgsAsString", "settings", "get", "headers", "url<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "network", "networkResponseHeaders", "networkResponseBody", "networkRequestHeaders", "networkRequestBody", "networkErrorOnHttp5xx", "networkErrorOnHttp4xx", "networkErrorOnHttp0", "dom", "navigation", "connectivity", "contentSecurityPolicy", "errorOnContentSecurityPolicy", "restore", "replacements", "b", "_window", "_document", "autoInstrument", "scrubTelemetryInputs", "telemetryScrubber", "defaultValueScrubber", "patterns", "RegExp", "description", "name", "attrs", "nameFromDescription", "diagnostic", "eventRemovers", "contentsecuritypolicy", "_location", "_lastHref", "href", "_isUrlObject", "input", "URL", "oldSettings", "deinstrumentNetwork", "instrumentNetwork", "deinstrumentConsole", "instrumentConsole", "deinstrumentDom", "instrumentDom", "deinstrumentNavigation", "instrumentNavigation", "deinstrumentConnectivity", "instrumentConnectivity", "deinstrumentContentSecurityPolicy", "instrumentContentSecurityPolicy", "wrapProp", "xhr", "orig", "xhrp", "isUrlObject", "__rollbar_xhr", "status_code", "start_time_ms", "now", "end_time_ms", "header", "request_headers", "request_content_type", "onreadystatechangeHandler", "request", "__rollbar_event", "captureNetwork", "readyState", "response_content_type", "getResponseHeader", "headersConfig", "allHeaders", "getAllResponseHeaders", "arr", "trim", "body", "responseText", "response", "isJsonContentType", "scrubJson", "code", "status", "levelFromStatus", "errorOnHttpStatus", "onreadystatechange", "trackHttpErrors", "t", "reqHeaders", "fetchHeaders", "then", "resp", "text", "clone", "subtype", "rollbarUUID", "contentType", "includes", "json", "JSON", "inHeaders", "outHeaders", "entries", "<PERSON><PERSON><PERSON><PERSON>", "next", "done", "skip<PERSON><PERSON><PERSON>", "c", "wrapConsole", "origConsole", "captureLog", "Function", "removeListeners", "clickHandler", "handleClick", "bind", "<PERSON><PERSON><PERSON><PERSON>", "handleBlur", "addListener", "hasTag", "anchorOrButton", "captureDomEvent", "checked", "handleSelectInputChanged", "multiple", "selected", "selectedIndex", "isChecked", "elementString", "captureDom", "app", "history", "pushState", "current", "handleUrlChange", "from", "to", "parsedHref", "parsedTo", "parsedFrom", "hash", "captureNavigation", "captureConnectivityChange", "handleCspEvent", "cspEvent", "blockedURI", "violatedDirective", "effectiveDirective", "sourceFile", "originalPolicy", "handleCspError", "c<PERSON><PERSON><PERSON><PERSON>", "section", "altType", "capture", "attachEvent", "detachEvent", "addBodyMessage", "custom", "extra", "set", "stackFromItem", "buildTrace", "guess", "guessErrorClass", "trace", "exception", "class", "errorClass", "stackFrame", "frame", "pre", "contextLength", "mid", "rawStack", "raw", "rawException", "frames", "filename", "sanitizeUrl", "func", "column", "sendFrameUrl", "endsWith", "Math", "floor", "reverse", "err", "<PERSON><PERSON>", "originalError", "nested", "_savedStackTrace", "addErrorContext", "chain", "cause", "e2", "environment", "platform", "framework", "language", "server", "requestInfo", "query_string", "remoteString", "user_ip", "keys", "nav", "navigator", "scr", "screen", "runtime_ms", "timestamp", "round", "javascript", "browser", "userAgent", "cookie_enabled", "cookieEnabled", "width", "cur", "plugins", "navPlugins", "l", "<PERSON><PERSON><PERSON><PERSON>", "traces", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trace_chain", "addBodyTraceChain", "addBodyTrace", "scrubFn", "scrubPaths", "makeFetchRequest", "makeXhrRequest", "params", "requestFactory", "addParamsAndAccessTokenToPath", "formatUrl", "_makeZoneRequest", "writeData", "currentZone", "Zone", "_name", "rootZone", "_parent", "run", "_makeRequest", "RollbarProxy", "_msg", "_proxyRequest", "controller", "timeoutId", "isFiniteNumber", "AbortController", "abort", "signal", "clearTimeout", "catch", "_newRetriableError", "xmlhttp", "factories", "ActiveXObject", "numFactories", "_createXMLHTTPObject", "parseResponse", "jsonParse", "_isNormalFailure", "ex", "open", "setRequestHeader", "send", "e1", "XDomainRequest", "xdomainrequest", "onprogress", "ontimeout", "onload", "last", "auth", "query", "parseInt", "pathParts", "_extendListenerPrototype", "oldAddEventListener", "_rollbarOldAdd", "addFn", "bubble", "oldRemoveEventListener", "_rollbarOldRemove", "removeFn", "maxItems", "itemsPerMin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ERR_CLASS_REGEXP", "gatherContext", "<PERSON>ame", "_stackFrame", "skip", "parserStack", "getStack", "constructorName", "constructor", "guessFunctionName", "errMsg", "errClassMatch", "err<PERSON><PERSON>", "hasOwn", "toStr", "isPlainObject", "hasOwnConstructor", "hasIsPrototypeOf", "src", "copy", "Notifier", "transform", "addPendingItem", "_applyTransforms", "removePendingItem", "addItem", "transformIndex", "<PERSON><PERSON><PERSON><PERSON>", "cb", "matchFrames", "list", "block", "listLength", "frameLength", "urlIsOnAList", "safeOrBlock", "hostBlockList", "hostSafeList", "<PERSON><PERSON><PERSON><PERSON>", "listName", "levelVal", "LEVELS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onSendCallback", "ignoredMessages", "rIgnoredMessage", "messages", "messagesFromItem", "Queue", "rateLimiter", "pendingItems", "pendingRequests", "retryQueue", "retryHandle", "waitCallback", "waitIntervalID", "predicate", "idx", "splice", "originalItem", "predicateResult", "_applyPredicates", "stop", "_<PERSON><PERSON><PERSON>", "_makeApiRequest", "_dequeuePendingRequest", "wait", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearInterval", "setInterval", "rateLimitResponse", "shouldSend", "_maybeRetry", "RETRIABLE_ERRORS", "shouldRetry", "retryInterval", "maxRetries", "retries", "_retryApiRequest", "retryObject", "RateLimiter", "startTime", "counter", "per<PERSON><PERSON><PERSON><PERSON><PERSON>", "platformOptions", "configureGlobal", "checkRate", "limit", "ignoreRateLimit", "shouldSendV<PERSON>ue", "globalRateLimit", "limitPerMin", "perMinute", "itemsPerMinute", "rateLimitPayload", "globalSettings", "elapsedTime", "globalRateLimitPerMin", "setPlatformOptions", "tracer", "validateTracer", "setStackTraceLimit", "lastErrorHash", "stackTraceLimit", "scope", "active", "_defaultLogLevel", "_log", "defaultLevel", "_sameAsLastError", "_addTracingInfo", "_captureRollbarItem", "telemetryEvents", "copyEvents", "itemHash", "generateItemHash", "span", "spanContext", "toSpanId", "toTraceId", "validateSpan", "setTag", "opentracingSpanId", "opentracingTraceId", "opentracing_span_id", "opentracing_trace_id", "traverse", "scrubPath", "redact", "paramRes", "pat", "_getScrubFieldRegexs", "queryRes", "_getScrubQueryParamRegexs", "redactQueryParam", "dummy0", "paramPart", "scrubber", "seen", "tmpV", "valScrubber", "param<PERSON><PERSON><PERSON><PERSON><PERSON>", "MAX_EVENTS", "maxTelemetryEvents", "maxQueueSize", "max", "min", "getLevel", "manual", "newMaxEvents", "deleteCount", "events", "filterTelemetry", "timestamp_ms", "captureError", "requestData", "statusCode", "change", "addFunctionOption", "payloadOptions", "tracePath", "newExtra", "newItem", "isPromise", "promisedItem", "configuredOptions", "configured_options", "is_anonymous", "is_uncaught", "raw_error", "constructor_name", "failed", "selectFrames", "range", "truncateFrames", "maybeTruncateValue", "val", "truncateStrings", "truncator", "typeName", "truncateTraceData", "traceData", "minBody", "needsTruncation", "maxSize", "maxByteSize", "strategy", "results", "strategies", "RollbarJSON", "x", "isNativeFunction", "funcMatchString", "reIsNative", "isObject", "uuid4", "d", "random", "parseUriOptions", "strictMode", "q", "parser", "strict", "loose", "backup", "jsonError", "backup<PERSON><PERSON>r", "wrapCallback", "nonCircularClone", "newSeen", "TELEMETRY_TYPES", "TELEMETRY_LEVELS", "arrayIncludes", "paramsArray", "sort", "qs", "h", "requestKeys", "lambdaContext", "arg", "extraArgs", "argTypes", "typ", "DOMException", "setCustomItemKeys", "original_arg_types", "errors", "contextAdded", "rollbarContext", "error_context", "filterIp", "newIp", "beginning", "slashIdx", "u", "hostWhiteList", "hostBlackList", "updateDeprecatedOptions", "overwriteScrubFields", "isIterable", "isString", "s", "mode", "backupMessage", "useragent", "baseUrlParts", "o", "m", "uri", "$0", "$1", "$2", "parseUri", "anchor", "temp", "replacement", "string", "count", "charCodeAt", "normalizeName", "FetchHeaders", "for<PERSON>ach", "append", "isArray", "getOwnPropertyNames", "normalizeValue", "oldValue", "has", "thisArg", "items", "iteratorFor", "Headers", "seenIndex", "isObj", "mapped", "same", "gap", "indent", "meta", "rep", "state", "container", "escapes", "number", "action", "rx_escapable", "this_value", "valueOf", "quote", "lastIndex", "holder", "partial", "mind", "toJSON", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "replacer", "space", "go", "firstokey", "okey", "ovalue", "firstavalue", "avalue", "ocomma", "acomma", "colon", "SyntaxError", "reviver", "tx", "ignore", "fromCharCode", "walk", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}