{"name": "rollbar", "version": "2.26.4", "repository": {"type": "git", "url": "http://github.com/rollbar/rollbar.js"}, "description": "Effortlessly track and debug errors in your JavaScript applications with Rollbar. This package includes advanced error tracking features and an intuitive interface to help you identify and fix issues more quickly.", "keywords": ["error", "tracking", "logging", "debugging", "javascript"], "license": "MIT", "main": "src/server/rollbar.js", "browser": "dist/rollbar.umd.min.js", "types": "./index.d.ts", "dependencies": {"async": "~3.2.3", "console-polyfill": "0.3.0", "error-stack-parser": "^2.0.4", "json-stringify-safe": "~5.0.0", "lru-cache": "~2.2.1", "request-ip": "~3.3.0", "source-map": "^0.5.7"}, "devDependencies": {"@babel/core": "^7.22.11", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.4", "bluebird": "^3.3.5", "chai": "^4.2.0", "chalk": "^1.1.1", "coverage-istanbul-loader": "^3.0.5", "eslint": "^6.8.0", "eslint-loader": "^3.0.3", "express": "^4.18.2", "glob": "^5.0.14", "grunt": "^1.1.0", "grunt-bumpup": "^0.6.3", "grunt-cli": "^1.3.2", "grunt-contrib-concat": "^2.1.0", "grunt-contrib-connect": "^2.1.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-karma": "^4.0.2", "grunt-parallel": "^0.5.1", "grunt-text-replace": "^0.4.0", "grunt-vows": "^0.4.2", "grunt-webpack": "^5.0.0", "jade": "~0.27.7", "jasmine-core": "^2.3.4", "jquery-mockjax": "^2.5.0", "karma": "^6.4.2", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-expect": "^1.1.0", "karma-firefox-launcher": "^0.1.6", "karma-html2js-preprocessor": "^1.1.0", "karma-jquery": "^0.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-requirejs": "^0.2.2", "karma-safari-launcher": "^0.1.1", "karma-sinon": "^1.0.4", "karma-sourcemap-loader": "^0.3.5", "karma-webpack": "^5.0.0", "mocha": "^10.2.0", "natives": "^1.1.6", "nock": "^11.9.1", "node-libs-browser": "^0.5.2", "prettier": "^3.2.5", "requirejs": "^2.1.20", "script-loader": "0.6.1", "sinon": "^8.1.1", "stackframe": "^0.2.2", "strict-loader": "^1.2.0", "time-grunt": "^1.0.0", "vows": "^0.8.3", "webpack": "^5.88.2"}, "optionalDependencies": {"decache": "^3.0.5"}, "scripts": {"build": "./node_modules/.bin/grunt", "test": "./node_modules/.bin/grunt test", "test-browser": "./node_modules/.bin/grunt test-browser", "test-server": "./node_modules/.bin/grunt test-server", "test_ci": "./node_modules/.bin/grunt test", "lint": "./node_modules/.bin/eslint . --ext .js"}, "cdn": {"host": "cdn.rollbar.com"}, "defaults": {"endpoint": "api.rollbar.com/api/1/item/", "server": {"scrubHeaders": ["authorization", "www-authorization", "http_authorization", "omniauth.auth", "cookie", "oauth-access-token", "x-access-token", "x_csrf_token", "http_x_csrf_token", "x-csrf-token"], "scrubFields": ["pw", "pass", "passwd", "password", "password_confirmation", "passwordConfirmation", "confirm_password", "confirmPassword", "secret", "secret_token", "secretToken", "secret_key", "secret<PERSON>ey", "api_key", "access_token", "accessToken", "authenticity_token", "oauth_token", "token", "user_session_secret", "request.session.csrf", "request.session._csrf", "request.params._csrf", "request.cookie", "request.cookies"]}, "reactNative": {"rewriteFilenamePatterns": ["^.*/[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}/[^/]*.app/(.*)$", "^.*/[0-9A-Fa-f]{64}/codepush_ios/(.*)$", "^.*/[0-9A-Fa-f]{64}/codepush_android/(.*)$", "^.*/[0-9A-Fa-f]{64}/CodePush/(.*)$"]}, "logLevel": "debug", "reportLevel": "debug", "uncaughtErrorLevel": "error", "maxItems": 0, "itemsPerMin": 60}, "plugins": {"jquery": {"version": "0.0.8"}}}