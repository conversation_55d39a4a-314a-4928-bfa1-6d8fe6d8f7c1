JSON in JavaScript


Douglas <PERSON>
<EMAIL>

2015-05-03


JSON is a light-weight, language independent, data interchange format.
See http://www.JSON.org/

The files in this collection implement JSON encoders/decoders in JavaScript.

JSON became a built-in feature of JavaScript when the ECMAScript Programming
Language Standard - Fifth Edition was adopted by the ECMA General Assembly
in December 2009. Most of the files in this collection are for applications
that are expected to run in obsolete web browsers. For most purposes, json2.js
is the best choice.


json2.js: This file creates a JSON property in the global object, if there
isn't already one, setting its value to an object containing a stringify
method and a parse method. The parse method uses the eval method to do the
parsing, guarding it with several regular expressions to defend against
accidental code execution hazards. On current browsers, this file does nothing,
preferring the built-in JSON object. There is no reason to use this file unless
fate compels you to support IE8, which is something that no one should ever 
have to do again.

json_parse.js: This file contains an alternative JSON parse function that
uses recursive descent instead of eval.

json_parse_state.js: This files contains an alternative JSON parse function that
uses a state machine instead of eval.

cycle.js: This file contains two functions, JSON.decycle and JSON.retrocycle,
which make it possible to encode cyclical structures and dags in JSON, and to
then recover them. This is a capability that is not provided by ES5. JSONPath 
is used to represent the links. [http://GOESSNER.net/articles/JsonPath/]
