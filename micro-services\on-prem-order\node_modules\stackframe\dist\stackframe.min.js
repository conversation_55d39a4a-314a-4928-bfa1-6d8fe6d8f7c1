!function(t,e){"use strict";"function"==typeof define&&define.amd?define("stackframe",[],e):"object"==typeof exports?module.exports=e():t.StackFrame=e()}(this,function(){"use strict";function t(t){return t.charAt(0).toUpperCase()+t.substring(1)}function e(t){return function(){return this[t]}}var r=["isConstructor","isEval","isNative","isToplevel"],n=["columnNumber","lineNumber"],i=["fileName","functionName","source"],o=r.concat(n,i,["args"],["evalOrigin"]);function s(e){if(e)for(var r=0;r<o.length;r++)void 0!==e[o[r]]&&this["set"+t(o[r])](e[o[r]])}s.prototype={getArgs:function(){return this.args},setArgs:function(t){if("[object Array]"!==Object.prototype.toString.call(t))throw new TypeError("Args must be an Array");this.args=t},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(t){if(t instanceof s)this.evalOrigin=t;else{if(!(t instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new s(t)}},toString:function(){var t=this.getFileName()||"",e=this.getLineNumber()||"",r=this.getColumnNumber()||"",n=this.getFunctionName()||"";return this.getIsEval()?t?"[eval] ("+t+":"+e+":"+r+")":"[eval]:"+e+":"+r:n?n+" ("+t+":"+e+":"+r+")":t+":"+e+":"+r}},s.fromString=function(t){var e=t.indexOf("("),r=t.lastIndexOf(")"),n=t.substring(0,e),i=t.substring(e+1,r).split(","),o=t.substring(r+1);if(0===o.indexOf("@"))var u=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(o,""),a=u[1],f=u[2],c=u[3];return new s({functionName:n,args:i||void 0,fileName:a,lineNumber:f||void 0,columnNumber:c||void 0})};for(var u=0;u<r.length;u++)s.prototype["get"+t(r[u])]=e(r[u]),s.prototype["set"+t(r[u])]=function(t){return function(e){this[t]=Boolean(e)}}(r[u]);for(var a=0;a<n.length;a++)s.prototype["get"+t(n[a])]=e(n[a]),s.prototype["set"+t(n[a])]=function(t){return function(e){if(r=e,isNaN(parseFloat(r))||!isFinite(r))throw new TypeError(t+" must be a Number");var r;this[t]=Number(e)}}(n[a]);for(var f=0;f<i.length;f++)s.prototype["get"+t(i[f])]=e(i[f]),s.prototype["set"+t(i[f])]=function(t){return function(e){this[t]=String(e)}}(i[f]);return s});
//# sourceMappingURL=stackframe.min.js.map