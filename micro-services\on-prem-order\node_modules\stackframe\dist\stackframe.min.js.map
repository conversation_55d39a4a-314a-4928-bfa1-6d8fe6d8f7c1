{"version": 3, "sources": ["stackframe.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "StackFrame", "this", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "p", "booleanProps", "numericProps", "stringProps", "props", "concat", "obj", "i", "length", "undefined", "prototype", "getArgs", "args", "set<PERSON>rgs", "v", "Object", "toString", "call", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "getFileName", "lineNumber", "getLineNumber", "columnNumber", "getColumnNumber", "functionName", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "indexOf", "argsEndIndex", "lastIndexOf", "split", "locationString", "parts", "exec", "Boolean", "j", "n", "isNaN", "parseFloat", "isFinite", "Number", "k", "String"], "mappings": "CAAC,SAASA,EAAMC,GACZ,aAIsB,mBAAXC,QAAyBA,OAAOC,IACvCD,OAAO,gBAAkBD,GACC,iBAAZG,QACdC,OAAOD,QAAUH,IAEjBD,EAAKM,WAAaL,IAV1B,CAYEM,KAAM,WACJ,aAKA,SAASC,EAAYC,GACjB,OAAOA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,UAAU,GAGvD,SAASC,EAAQC,GACb,OAAO,WACH,OAAOP,KAAKO,IAIpB,IAAIC,GAAgB,gBAAiB,SAAU,WAAY,cACvDC,GAAgB,eAAgB,cAChCC,GAAe,WAAY,eAAgB,UAI3CC,EAAQH,EAAaI,OAAOH,EAAcC,GAH5B,SACC,eAInB,SAASX,EAAWc,GAChB,GAAKA,EACL,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAMI,OAAQD,SACRE,IAAlBH,EAAIF,EAAMG,KACVd,KAAK,MAAQC,EAAYU,EAAMG,KAAKD,EAAIF,EAAMG,KAK1Df,EAAWkB,WACPC,QAAS,WACL,OAAOlB,KAAKmB,MAEhBC,QAAS,SAASC,GACd,GAA0C,mBAAtCC,OAAOL,UAAUM,SAASC,KAAKH,GAC/B,MAAM,IAAII,UAAU,yBAExBzB,KAAKmB,KAAOE,GAGhBK,cAAe,WACX,OAAO1B,KAAK2B,YAEhBC,cAAe,SAASP,GACpB,GAAIA,aAAatB,EACbC,KAAK2B,WAAaN,MACf,CAAA,KAAIA,aAAaC,QAGpB,MAAM,IAAIG,UAAU,+CAFpBzB,KAAK2B,WAAa,IAAI5B,EAAWsB,KAMzCE,SAAU,WACN,IAAIM,EAAW7B,KAAK8B,eAAiB,GACjCC,EAAa/B,KAAKgC,iBAAmB,GACrCC,EAAejC,KAAKkC,mBAAqB,GACzCC,EAAenC,KAAKoC,mBAAqB,GAC7C,OAAIpC,KAAKqC,YACDR,EACO,WAAaA,EAAW,IAAME,EAAa,IAAME,EAAe,IAEpE,UAAYF,EAAa,IAAME,EAEtCE,EACOA,EAAe,KAAON,EAAW,IAAME,EAAa,IAAME,EAAe,IAE7EJ,EAAW,IAAME,EAAa,IAAME,IAInDlC,EAAWuC,WAAa,SAAgCpC,GACpD,IAAIqC,EAAiBrC,EAAIsC,QAAQ,KAC7BC,EAAevC,EAAIwC,YAAY,KAE/BP,EAAejC,EAAIG,UAAU,EAAGkC,GAChCpB,EAAOjB,EAAIG,UAAUkC,EAAiB,EAAGE,GAAcE,MAAM,KAC7DC,EAAiB1C,EAAIG,UAAUoC,EAAe,GAElD,GAAoC,IAAhCG,EAAeJ,QAAQ,KACvB,IAAIK,EAAQ,gCAAgCC,KAAKF,EAAgB,IAC7Df,EAAWgB,EAAM,GACjBd,EAAac,EAAM,GACnBZ,EAAeY,EAAM,GAG7B,OAAO,IAAI9C,GACPoC,aAAcA,EACdhB,KAAMA,QAAQH,EACda,SAAUA,EACVE,WAAYA,QAAcf,EAC1BiB,aAAcA,QAAgBjB,KAItC,IAAK,IAAIF,EAAI,EAAGA,EAAIN,EAAaO,OAAQD,IACrCf,EAAWkB,UAAU,MAAQhB,EAAYO,EAAaM,KAAOR,EAAQE,EAAaM,IAClFf,EAAWkB,UAAU,MAAQhB,EAAYO,EAAaM,KAAO,SAAUP,GACnE,OAAO,SAASc,GACZrB,KAAKO,GAAKwC,QAAQ1B,IAFmC,CAI1Db,EAAaM,IAGpB,IAAK,IAAIkC,EAAI,EAAGA,EAAIvC,EAAaM,OAAQiC,IACrCjD,EAAWkB,UAAU,MAAQhB,EAAYQ,EAAauC,KAAO1C,EAAQG,EAAauC,IAClFjD,EAAWkB,UAAU,MAAQhB,EAAYQ,EAAauC,KAAO,SAAUzC,GACnE,OAAO,SAASc,GACZ,GA9GO4B,EA8GQ5B,EA7Gf6B,MAAMC,WAAWF,MAAOG,SAASH,GA8G7B,MAAM,IAAIxB,UAAUlB,EAAI,qBA/GxC,IAAmB0C,EAiHPjD,KAAKO,GAAK8C,OAAOhC,IALoC,CAO1DZ,EAAauC,IAGpB,IAAK,IAAIM,EAAI,EAAGA,EAAI5C,EAAYK,OAAQuC,IACpCvD,EAAWkB,UAAU,MAAQhB,EAAYS,EAAY4C,KAAOhD,EAAQI,EAAY4C,IAChFvD,EAAWkB,UAAU,MAAQhB,EAAYS,EAAY4C,KAAO,SAAU/C,GAClE,OAAO,SAASc,GACZrB,KAAKO,GAAKgD,OAAOlC,IAFmC,CAIzDX,EAAY4C,IAGnB,OAAOvD"}