import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

import { Transform } from 'class-transformer';
import { Customer } from './on-prem-customer.entity';
import { User } from './on-prem-user.entity';
import { Floor } from './on-prem-floor.entity';
import { Table } from './on-prem-table.entity';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class OnPremOrder {
  @Transform(({ value }) => value.toString())
  _id: string;

  @Prop({ type: Number })
  subTotal: number;

  @Prop({ type: String, index: true, unique: true })
  orderNo: string;

  @Prop({ type: Date, default: null })
  readyAtKitchen: Date;

  @Prop({ type: Date, default: null })
  holdTime: Date;

  @Prop({ type: Number })
  total: number;

  @Prop({ type: Number })
  orderPrepTime: number;

  @Prop({ type: Date })
  orderReadyTime: Date;

  @Prop({ type: Number })
  total_discount_amount: number;

  @Prop({ type: String })
  voucherId: string;

  @Prop({ type: [String] })
  alternatePhoneNo: string[];

  @Prop({ type: String })
  reason: string;

  @Prop({ type: String })
  reasonSubject: string;

  @Prop({ type: String })
  customerPhoneNo?: string;

  @Prop({ type: String })
  customerName?: string;

  @Prop({ type: String })
  customerNotes: string;

  @Prop({ type: String })
  cancellationNotes: string;

  @Prop({ type: Types.ObjectId, ref: Customer.name })
  customerId: Customer;

  @Prop({ type: Types.ObjectId, ref: User.name })
  waiterId: User;

  @Prop({ type: Types.ObjectId, ref: Floor.name })
  floorId: Floor;

  @Prop({ type: Types.ObjectId, ref: Table.name })
  tableId: Table;

  @Prop({ type: Date, default: null })
  readyForPickUpAt: Date;

  @Prop({ type: Boolean, default: false })
  isReviewed: boolean;

  @Prop({ type: Date })
  orderUpdatedAt: Date;

  @Prop({ type: Number, default: 0 })
  taxAmount: number;

  @Prop({ type: Number, default: 0 })
  taxRate: number;

  @Prop({ type: Boolean, default: false })
  isTaxExclusive: boolean;

  @Prop({ type: String, index: true })
  status: any;

  @Prop({ type: String, required: true })
  source: any;

  @Prop({ type: String, required: true })
  type: any;

  @Prop({ type: [String], default: [] })
  lateStatus: any[];

  @Prop({ type: Object })
  payment: any;

  @Prop({ type: [Object], default: [] })
  orderItems: any[];

  @Prop({ type: String, default: '' })
  vehicleNo: string;

  @Prop({ type: Object, default: null })
  discount: any;

  @Prop({ type: Number })
  guestCount: number;

  @Prop({ type: Number })
  discAmountWithOutTax: number;

  @Prop({ type: Boolean, default: false })
  dynamicsFinalized: boolean;

  @Prop({ type: Boolean, default: false })
  dynamicsCreated: boolean;

  @Prop({ type: Boolean, default: false })
  dynamicsUpdated: boolean;

  @Prop({ type: Boolean, default: false })
  dynamicsCompleted: boolean;

  @Prop({ type: String, default: 'FBR' })
  taxGroup: string;

  @Prop()
  createdAt?: Date;

  @Prop()
  updatedAt?: Date;
}
export type OnPremOrderDocument = OnPremOrder & Document;

export const OnPremOrderSchema = SchemaFactory.createForClass(OnPremOrder);
