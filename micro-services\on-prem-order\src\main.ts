import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { configService } from './config/config';

import Rollbar = require('rollbar');

export const rollBar: Rollbar = new Rollbar({
  accessToken: configService.getRollBarToken(),
  captureUncaught: true,
  captureUnhandledRejections: true,
});


async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.TCP,
      options: {
        host: configService.getHost(),
        port: parseInt(configService.getPort()),
      },
    },
  );
  await app.listen();
}
bootstrap();
