import { Controller, UseFilters } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { formatResponse } from './utils/response';
import { OnPremOrderService } from './on-prem-order.service';
import { HttpExceptionFilter } from './utils/exceptionFilter';

@Controller()
export class OnPremOrderController {
  constructor(private readonly onPremOrderService: OnPremOrderService) {}

  // find all on-prem orders route
  @UseFilters(new HttpExceptionFilter())
  @MessagePattern({ cmd: 'order-findAllOnPremOrders' })
  async findAllOnPremOrders(data) {
    const { find, page, limit, sort } = data;
    
    const orders = await this.onPremOrderService.findAll(find, {
      page,
      limit,
      sort,
    });
    
    const total = await this.onPremOrderService.getTotal(find);
    
    const result = {
      orders,
      total,
      page,
      limit,
    };

    return formatResponse(true, 200, 'ONPREM_ORDER_S1001', result);
  }

  // find one on-prem order by id
  @UseFilters(new HttpExceptionFilter())
  @MessagePattern({ cmd: 'order-findOneOnPremOrder' })
  async findOneOnPremOrder(data) {
    const order = await this.onPremOrderService.findById(data.id);
    
    if (order) {
      return formatResponse(true, 200, 'ONPREM_ORDER_S1002', order);
    } else {
      return formatResponse(false, 404, 'Order not found');
    }
  }
}