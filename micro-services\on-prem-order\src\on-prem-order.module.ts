import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OnPremOrder, OnPremOrderSchema } from 'src/entity/on-prem-order.entity';
import { OnPremOrderService } from './on-prem-order.service';
import { OnPremOrderRepository } from './on-prem-order.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: OnPremOrder.name, schema: OnPremOrderSchema },
    ]),
  ],
  providers: [OnPremOrderService, OnPremOrderRepository],
  exports: [OnPremOrderService, OnPremOrderRepository],
})
export class OnPremOrderModule {}