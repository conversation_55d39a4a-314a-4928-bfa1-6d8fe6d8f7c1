import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OnPremOrder, OnPremOrderDocument } from 'src/entity/on-prem-order.entity';

@Injectable()
export class OnPremOrderRepository {
  constructor(
    @InjectModel(OnPremOrder.name,'onPremConnection') 
    private onPremOrderModel: Model<OnPremOrderDocument>,
  ) {}

  // find all with filters and pagination
  async findAll(find: any, options: any) {
    try {
      const { page = 0, limit = 10, sort = 'desc' } = options;
      return await this.onPremOrderModel
        .find(find)
        .skip(page * limit)
        .limit(limit)
        .sort({ createdAt: sort === 'desc' ? -1 : 1 });
    } catch (error) {
      console.log(JSON.stringify(error));
      throw new HttpException(error, 400);
    }
  }

  // get total count
  async getTotal(find: any) {
    try {
      return await this.onPremOrderModel.countDocuments(find);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw new HttpException(error, 400);
    }
  }

  // find by id
  async findById(id: string) {
    try {
      return await this.onPremOrderModel.findById(id);
    } catch (error) {
      console.log(JSON.stringify(error));
      throw new HttpException(error, 400);
    }
  }
}