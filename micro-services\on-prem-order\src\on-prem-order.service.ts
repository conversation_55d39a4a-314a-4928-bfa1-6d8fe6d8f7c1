import { Injectable } from '@nestjs/common';
import { OnPremOrderRepository } from './on-prem-order.repository';

@Injectable()
export class OnPremOrderService {
  constructor(
    private readonly onPremOrderRepository: OnPremOrderRepository,
  ) {}

  async findAll(find: any, options: any) {
    return await this.onPremOrderRepository.findAll(find, options);
  }

  async getTotal(find: any) {
    return await this.onPremOrderRepository.getTotal(find);
  }

  async findById(id: string) {
    return await this.onPremOrderRepository.findById(id);
  }
}