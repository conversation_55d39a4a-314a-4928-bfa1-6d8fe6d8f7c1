import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  BadRequestException,
  ForbiddenException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { rollBar } from 'src/main';
// Exception filter for all unsuccessfull responses with a json body in response
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly knownHttpExceptions = [
    BadRequestException,
    NotFoundException,
    UnauthorizedException,
    ForbiddenException,
    // Add other known HTTP exceptions here if necessary
  ];

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    ctx.getRequest<Request>();
    const status = exception.getStatus();

    const errMessage =
      exception.getResponse() instanceof Object
        ? exception.getResponse()['message']
        : exception.getResponse();

    // Check if the exception is a known HTTP exception
    const isKnownHttpException = this.knownHttpExceptions.some(
      (knownException) => exception instanceof knownException,
    );

    // Send unexpected exceptions to Rollbar
    if (!isKnownHttpException) {
      rollBar.error(exception);
    }
    console.log(exception);

    if (exception instanceof HttpException) {
      const error = {
        statusCode: status,
        messge: errMessage,
        time: new Date().toISOString(),
      };
      response.status(status).json(error);
    } else {
      response.json({
        message: errMessage,
        code: status,
        isSuccess: false,
      });
    }
  }
}
